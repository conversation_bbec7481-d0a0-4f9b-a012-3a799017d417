# 🔄 電子書轉檔工具 - 替代方案指南

## ❌ **Calibre 安裝問題**

如果您遇到 Calibre 安裝失敗的問題，不用擔心！我們提供了多個替代解決方案。

## 🚀 **快速解決方案**

### **方案一：自動安裝替代工具**
```bash
python install_alternatives.py
```

這個腳本會自動安裝：
- Python 電子書處理庫
- Pandoc 可攜版
- 內建簡單轉換器

### **方案二：手動安裝 Pandoc**
1. 前往 [Pandoc 官網](https://pandoc.org/installing.html)
2. 下載 Windows 版本
3. 安裝後重新啟動程式

### **方案三：使用內建轉換器**
程式已內建基本轉換功能，支援：
- TXT ↔ HTML
- HTML ↔ TXT
- TXT → Markdown

## 📋 **支援的轉換格式**

### **完整支援（需要 Calibre）**
```
epub ↔ pdf ↔ mobi ↔ azw3 ↔ txt ↔ html ↔ docx ↔ fb2 ↔ cbz ↔ cbr
```

### **Pandoc 支援**
```
txt ↔ html ↔ epub ↔ docx ↔ pdf ↔ markdown
```

### **Python 庫支援**
```
txt ↔ html
html ↔ txt
txt → markdown
```

### **內建轉換器**
```
txt → html （基本格式）
html → txt （移除標籤）
txt → markdown （簡單格式化）
```

## 🛠️ **手動安裝步驟**

### **安裝 Python 電子書庫**
```bash
pip install pypdf2 ebooklib python-docx beautifulsoup4 lxml markdown
```

### **安裝 Pandoc**
1. **Windows**:
   - 下載: https://github.com/jgm/pandoc/releases
   - 安裝 MSI 檔案
   - 重新啟動命令提示字元

2. **使用 Chocolatey**:
   ```bash
   choco install pandoc
   ```

3. **使用 Scoop**:
   ```bash
   scoop install pandoc
   ```

## 🔍 **檢查安裝狀態**

程式會自動檢測可用的轉檔引擎：

```python
# 程式啟動時會顯示
可用的轉檔引擎:
✅ Python 內建轉換器
✅ Pandoc
❌ Calibre (未安裝)
```

## 📝 **使用說明**

### **1. 啟動程式**
```bash
python main.py
```

### **2. 選擇檔案**
- 點擊「瀏覽」選擇要轉換的檔案
- 程式會自動檢測可用的轉換方式

### **3. 選擇格式**
- 從下拉選單選擇輸出格式
- 程式會自動生成輸出路徑

### **4. 開始轉換**
- 點擊「開始轉檔」
- 程式會自動選擇最佳的轉換引擎

## ⚡ **轉換引擎優先順序**

1. **Calibre** (最佳，支援最多格式)
2. **Pandoc** (良好，支援常見格式)
3. **Python 庫** (基本，支援簡單轉換)
4. **內建轉換器** (最基本，純文字轉換)

## 🎯 **推薦解決方案**

### **對於一般使用者**
```bash
# 執行自動安裝
python install_alternatives.py
```

### **對於進階使用者**
1. 手動安裝 Pandoc
2. 安裝 Python 電子書庫
3. 如果需要完整功能，解決 Calibre 安裝問題

### **對於基本需求**
- 直接使用內建轉換器
- 支援 TXT、HTML 基本轉換

## 🔧 **故障排除**

### **問題：所有轉換都失敗**
**解決方案**:
```bash
# 重新安裝 Python 庫
pip install --upgrade pypdf2 ebooklib python-docx

# 檢查 Pandoc
pandoc --version

# 重新執行替代方案安裝
python install_alternatives.py
```

### **問題：特定格式無法轉換**
**解決方案**:
- 檢查檔案是否損壞
- 嘗試不同的轉換引擎
- 使用線上轉換工具作為備案

### **問題：轉換速度很慢**
**解決方案**:
- 檔案過大時會較慢
- Calibre 通常最快
- 可以嘗試分割大檔案

## 📞 **技術支援**

如果所有方案都無法解決問題：

1. **檢查系統需求**:
   - Windows 10/11
   - Python 3.7+
   - 足夠的磁碟空間

2. **收集錯誤資訊**:
   - 錯誤訊息截圖
   - 系統版本
   - Python 版本

3. **嘗試線上工具**:
   - CloudConvert
   - Online-Convert
   - Zamzar

## 🎉 **總結**

即使 Calibre 無法安裝，您仍然可以：
- ✅ 使用 Pandoc 進行大部分轉換
- ✅ 使用 Python 庫處理常見格式
- ✅ 使用內建轉換器處理基本需求
- ✅ 享受自動輸出路徑等便利功能

電子書轉檔工具設計為多引擎支援，確保在任何情況下都能為您提供轉檔服務！
