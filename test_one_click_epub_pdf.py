"""
測試一鍵 EPUB → PDF 轉換
驗證 Pandoc 是否能實現完全自動化轉換
"""

import subprocess
import sys
import os
from pathlib import Path
import zipfile
import time


def setup_pandoc_path():
    """設定 Pandoc PATH"""
    pandoc_exe = Path("pandoc-3.1.8/pandoc.exe")
    if pandoc_exe.exists():
        pandoc_dir = str(pandoc_exe.parent.absolute())
        current_path = os.environ.get('PATH', '')
        if pandoc_dir not in current_path:
            os.environ['PATH'] = f"{pandoc_dir};{current_path}"
            print(f"✅ 已設定 Pandoc PATH: {pandoc_dir}")
            return True
    
    print("❌ 找不到 Pandoc")
    return False


def test_pandoc():
    """測試 Pandoc 功能"""
    print("🧪 測試 Pandoc 功能")
    print("-" * 30)
    
    try:
        result = subprocess.run(['pandoc', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ Pandoc 版本: {version_line}")
            
            # 檢查支援的格式
            help_result = subprocess.run(['pandoc', '--list-input-formats'], 
                                       capture_output=True, text=True, timeout=10)
            input_formats = help_result.stdout.strip().split('\n')
            
            help_result = subprocess.run(['pandoc', '--list-output-formats'], 
                                       capture_output=True, text=True, timeout=10)
            output_formats = help_result.stdout.strip().split('\n')
            
            epub_support = 'epub' in input_formats
            pdf_support = 'pdf' in output_formats
            
            print(f"📚 EPUB 輸入支援: {'✅' if epub_support else '❌'}")
            print(f"📄 PDF 輸出支援: {'✅' if pdf_support else '❌'}")
            
            return epub_support and pdf_support
        else:
            print("❌ Pandoc 測試失敗")
            return False
            
    except Exception as e:
        print(f"❌ Pandoc 測試例外: {e}")
        return False


def create_test_epub():
    """建立測試 EPUB 檔案"""
    print("\n📚 建立測試 EPUB 檔案")
    print("-" * 30)
    
    epub_path = "test_one_click.epub"
    
    # 完整的 EPUB 結構
    files = {
        'mimetype': 'application/epub+zip',
        'META-INF/container.xml': '''<?xml version="1.0"?>
<container version="1.0" xmlns="urn:oasis:names:tc:opendocument:xmlns:container">
  <rootfiles>
    <rootfile full-path="OEBPS/content.opf" media-type="application/oebps-package+xml"/>
  </rootfiles>
</container>''',
        'OEBPS/content.opf': '''<?xml version="1.0" encoding="UTF-8"?>
<package xmlns="http://www.idpf.org/2007/opf" unique-identifier="BookId" version="2.0">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <dc:title>一鍵轉換測試電子書</dc:title>
    <dc:creator>電子書轉檔工具</dc:creator>
    <dc:language>zh-TW</dc:language>
    <dc:identifier id="BookId">one-click-test-001</dc:identifier>
  </metadata>
  <manifest>
    <item id="chapter1" href="chapter1.html" media-type="application/xhtml+xml"/>
    <item id="chapter2" href="chapter2.html" media-type="application/xhtml+xml"/>
    <item id="toc" href="toc.ncx" media-type="application/x-dtbncx+xml"/>
  </manifest>
  <spine toc="toc">
    <itemref idref="chapter1"/>
    <itemref idref="chapter2"/>
  </spine>
</package>''',
        'OEBPS/toc.ncx': '''<?xml version="1.0" encoding="UTF-8"?>
<ncx xmlns="http://www.daisy.org/z3986/2005/ncx/" version="2005-1">
  <head>
    <meta name="dtb:uid" content="one-click-test-001"/>
    <meta name="dtb:depth" content="1"/>
  </head>
  <docTitle>
    <text>一鍵轉換測試電子書</text>
  </docTitle>
  <navMap>
    <navPoint id="chapter1">
      <navLabel><text>第一章</text></navLabel>
      <content src="chapter1.html"/>
    </navPoint>
    <navPoint id="chapter2">
      <navLabel><text>第二章</text></navLabel>
      <content src="chapter2.html"/>
    </navPoint>
  </navMap>
</ncx>''',
        'OEBPS/chapter1.html': '''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>第一章</title>
    <meta charset="UTF-8"/>
</head>
<body>
    <h1>第一章：一鍵轉換功能</h1>
    
    <p>歡迎使用電子書轉檔工具的一鍵轉換功能！</p>
    
    <h2>功能特色</h2>
    <ul>
        <li><strong>完全自動化</strong>：無需手動步驟</li>
        <li><strong>高品質輸出</strong>：使用 Pandoc 引擎</li>
        <li><strong>快速轉換</strong>：一次點擊完成</li>
        <li><strong>格式保持</strong>：保留原始排版</li>
    </ul>
    
    <h2>支援格式</h2>
    <p>現在支援以下轉換：</p>
    <ol>
        <li>EPUB → PDF（一鍵完成）</li>
        <li>EPUB → HTML</li>
        <li>EPUB → TXT</li>
        <li>TXT ↔ HTML ↔ Markdown</li>
    </ol>
    
    <p>這是一個重大突破，讓電子書轉換變得前所未有的簡單！</p>
</body>
</html>''',
        'OEBPS/chapter2.html': '''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>第二章</title>
    <meta charset="UTF-8"/>
</head>
<body>
    <h1>第二章：使用指南</h1>
    
    <h2>簡單三步驟</h2>
    <ol>
        <li><strong>選擇檔案</strong>：點擊「瀏覽」選擇您的 EPUB 檔案</li>
        <li><strong>選擇格式</strong>：從下拉選單選擇「PDF」</li>
        <li><strong>開始轉檔</strong>：點擊「開始轉檔」按鈕</li>
    </ol>
    
    <h2>轉換過程</h2>
    <p>程式會自動：</p>
    <ul>
        <li>解析 EPUB 檔案結構</li>
        <li>提取文字和格式資訊</li>
        <li>使用 Pandoc 生成高品質 PDF</li>
        <li>保存到與原檔案相同的目錄</li>
    </ul>
    
    <h2>品質保證</h2>
    <p>使用 Pandoc 引擎確保：</p>
    <ul>
        <li>✅ 文字完整保留</li>
        <li>✅ 格式正確轉換</li>
        <li>✅ 圖片適當處理</li>
        <li>✅ 章節結構清晰</li>
    </ul>
    
    <p><em>感謝使用電子書轉檔工具！</em></p>
</body>
</html>'''
    }
    
    try:
        with zipfile.ZipFile(epub_path, 'w', zipfile.ZIP_DEFLATED) as epub_zip:
            for file_path, content in files.items():
                epub_zip.writestr(file_path, content.encode('utf-8'))
        
        print(f"✅ 建立測試 EPUB: {epub_path}")
        return epub_path
        
    except Exception as e:
        print(f"❌ 建立 EPUB 失敗: {e}")
        return None


def test_pandoc_conversion(epub_file):
    """測試 Pandoc 直接轉換"""
    print(f"\n🔄 測試 Pandoc 直接轉換")
    print("-" * 30)
    
    output_file = "test_one_click_output.pdf"
    
    try:
        print("正在執行 Pandoc 轉換...")
        
        # 使用 Pandoc 直接轉換
        cmd = [
            'pandoc',
            epub_file,
            '-o', output_file,
            '--pdf-engine=wkhtmltopdf',  # 嘗試使用 wkhtmltopdf
            '--metadata', 'title=一鍵轉換測試',
            '--metadata', 'author=電子書轉檔工具'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            if Path(output_file).exists():
                file_size = Path(output_file).stat().st_size
                print(f"✅ Pandoc 轉換成功！")
                print(f"   輸出檔案: {output_file}")
                print(f"   檔案大小: {file_size} bytes")
                return True
            else:
                print("❌ 轉換報告成功但檔案未生成")
                return False
        else:
            print(f"❌ Pandoc 轉換失敗:")
            print(f"   錯誤訊息: {result.stderr}")
            
            # 嘗試不使用 PDF 引擎
            print("\n🔄 嘗試基本 PDF 轉換...")
            cmd_basic = ['pandoc', epub_file, '-o', output_file]
            result_basic = subprocess.run(cmd_basic, capture_output=True, text=True, timeout=60)
            
            if result_basic.returncode == 0 and Path(output_file).exists():
                file_size = Path(output_file).stat().st_size
                print(f"✅ 基本 PDF 轉換成功！")
                print(f"   檔案大小: {file_size} bytes")
                return True
            else:
                print(f"❌ 基本轉換也失敗: {result_basic.stderr}")
                return False
            
    except Exception as e:
        print(f"❌ 轉換例外: {e}")
        return False


def test_program_integration():
    """測試程式整合"""
    print(f"\n🔧 測試程式整合")
    print("-" * 30)
    
    try:
        from main import EbookConverter, ConversionStatus
        
        converter = EbookConverter()
        engines = converter.available_engines
        
        print("可用引擎:")
        for name, available in engines.items():
            status = "✅" if available else "❌"
            print(f"  {name}: {status}")
        
        pandoc_available = engines.get('pandoc', False)
        print(f"\nPandoc 整合: {'✅ 成功' if pandoc_available else '❌ 失敗'}")
        
        return pandoc_available
        
    except Exception as e:
        print(f"❌ 程式整合測試失敗: {e}")
        return False


def cleanup_test_files():
    """清理測試檔案"""
    print(f"\n🗑️ 清理測試檔案")
    print("-" * 30)
    
    test_files = [
        'test_one_click.epub',
        'test_one_click_output.pdf'
    ]
    
    cleaned = 0
    for file in test_files:
        if Path(file).exists():
            Path(file).unlink()
            cleaned += 1
            print(f"  清理: {file}")
    
    print(f"清理了 {cleaned} 個檔案")


def main():
    """主程式"""
    print("🚀 一鍵 EPUB → PDF 轉換測試")
    print("=" * 50)
    print("測試 Pandoc 是否能實現完全自動化轉換")
    print()
    
    # 設定 Pandoc PATH
    if not setup_pandoc_path():
        print("❌ 無法設定 Pandoc PATH")
        return
    
    # 測試 Pandoc 功能
    if not test_pandoc():
        print("❌ Pandoc 功能測試失敗")
        return
    
    # 建立測試檔案
    epub_file = create_test_epub()
    if not epub_file:
        return
    
    # 測試直接轉換
    pandoc_success = test_pandoc_conversion(epub_file)
    
    # 測試程式整合
    integration_success = test_program_integration()
    
    # 總結
    print(f"\n📊 測試結果總結:")
    print("=" * 30)
    print(f"Pandoc 直接轉換: {'✅ 成功' if pandoc_success else '❌ 失敗'}")
    print(f"程式整合: {'✅ 成功' if integration_success else '❌ 失敗'}")
    
    if pandoc_success and integration_success:
        print(f"\n🎉 完美！一鍵 EPUB → PDF 轉換已實現")
        print(f"\n💡 現在您可以:")
        print("1. 啟動程式: python main.py")
        print("2. 選擇 EPUB 檔案")
        print("3. 選擇 PDF 格式")
        print("4. 點擊轉檔 - 一次完成！")
        print(f"\n🌟 無需任何手動步驟，完全自動化！")
    elif pandoc_success:
        print(f"\n⚠️ Pandoc 可用但程式整合需要調整")
        print("建議重新啟動程式以重新檢測 Pandoc")
    else:
        print(f"\n❌ 一鍵轉換尚未完全實現")
        print("可能需要安裝額外的 PDF 引擎")
    
    cleanup_test_files()


if __name__ == '__main__':
    main()
