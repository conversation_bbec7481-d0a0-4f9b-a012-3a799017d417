"""
診斷轉檔內容缺漏問題
檢查 EPUB 解析和文字提取過程
"""

import zipfile
from pathlib import Path
import xml.etree.ElementTree as ET
from bs4 import BeautifulSoup
import re


def create_comprehensive_test_epub():
    """建立包含豐富內容的測試 EPUB"""
    print("📚 建立完整內容測試 EPUB...")
    
    epub_path = "content_test.epub"
    
    files = {
        'mimetype': 'application/epub+zip',
        'META-INF/container.xml': '''<?xml version="1.0"?>
<container version="1.0" xmlns="urn:oasis:names:tc:opendocument:xmlns:container">
  <rootfiles>
    <rootfile full-path="content.opf" media-type="application/oebps-package+xml"/>
  </rootfiles>
</container>''',
        'content.opf': '''<?xml version="1.0" encoding="UTF-8"?>
<package xmlns="http://www.idpf.org/2007/opf" unique-identifier="BookId" version="2.0">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <dc:title>內容完整性測試電子書</dc:title>
    <dc:creator>診斷工具</dc:creator>
    <dc:language>zh-TW</dc:language>
  </metadata>
  <manifest>
    <item id="chapter1" href="chapter1.html" media-type="application/xhtml+xml"/>
    <item id="chapter2" href="chapter2.html" media-type="application/xhtml+xml"/>
    <item id="chapter3" href="chapter3.html" media-type="application/xhtml+xml"/>
  </manifest>
  <spine>
    <itemref idref="chapter1"/>
    <itemref idref="chapter2"/>
    <itemref idref="chapter3"/>
  </spine>
</package>''',
        'chapter1.html': '''<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>第一章</title>
    <meta charset="UTF-8"/>
</head>
<body>
    <h1>第一章：開始</h1>
    
    <p>這是第一章的第一段文字。這段文字應該完整出現在轉檔結果中。</p>
    
    <p>第二段包含<strong>粗體文字</strong>和<em>斜體文字</em>，還有<u>底線文字</u>。</p>
    
    <h2>1.1 小節標題</h2>
    <p>小節內容包含數字：123456789，英文：ABCDEFGHIJKLMNOPQRSTUVWXYZ，中文：一二三四五六七八九十。</p>
    
    <h2>1.2 列表測試</h2>
    <ul>
        <li>無序列表項目一</li>
        <li>無序列表項目二</li>
        <li>無序列表項目三</li>
    </ul>
    
    <ol>
        <li>有序列表項目一</li>
        <li>有序列表項目二</li>
        <li>有序列表項目三</li>
    </ol>
    
    <p>第一章結束。總共應該有約 200 個字符的內容。</p>
</body>
</html>''',
        'chapter2.html': '''<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>第二章</title>
    <meta charset="UTF-8"/>
</head>
<body>
    <h1>第二章：發展</h1>
    
    <p>第二章開始了。這裡有更多的內容來測試解析的完整性。</p>
    
    <blockquote>
        <p>這是一個引用段落。引用內容應該被正確提取。</p>
    </blockquote>
    
    <h2>2.1 表格測試</h2>
    <table>
        <tr>
            <th>標題1</th>
            <th>標題2</th>
            <th>標題3</th>
        </tr>
        <tr>
            <td>內容1</td>
            <td>內容2</td>
            <td>內容3</td>
        </tr>
        <tr>
            <td>資料A</td>
            <td>資料B</td>
            <td>資料C</td>
        </tr>
    </table>
    
    <h2>2.2 特殊字符測試</h2>
    <p>特殊符號：©®™€£¥§¶†‡•…‰‹›""''–—</p>
    <p>數學符號：±×÷≠≤≥∞∑∏∫√∂∆∇</p>
    
    <p>第二章包含表格和特殊字符，總計約 300 個字符。</p>
</body>
</html>''',
        'chapter3.html': '''<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>第三章</title>
    <meta charset="UTF-8"/>
</head>
<body>
    <h1>第三章：結論</h1>
    
    <p>最後一章的內容。這裡測試長段落的處理。</p>
    
    <p>這是一個很長的段落，用來測試長文字的處理能力。段落中包含各種標點符號，如逗號、句號、問號、驚嘆號等。同時也包含括號（像這樣），以及引號「像這樣」和『像這樣』。這個段落的目的是確保長文字不會被截斷或遺漏。</p>
    
    <h2>3.1 程式碼測試</h2>
    <pre><code>
def hello_world():
    print("Hello, World!")
    return True
    </code></pre>
    
    <h2>3.2 混合內容</h2>
    <p>混合內容包括：</p>
    <ul>
        <li>中文：電腦軟體網路資訊技術</li>
        <li>English: Computer Software Network Information Technology</li>
        <li>數字：1234567890</li>
        <li>符號：!@#$%^&*()_+-=[]{}|;:,.<>?</li>
    </ul>
    
    <h2>3.3 最終統計</h2>
    <p><strong>內容統計：</strong></p>
    <ul>
        <li>總章節數：3 章</li>
        <li>總段落數：約 15 段</li>
        <li>總字符數：約 800+ 字符</li>
        <li>包含：標題、段落、列表、表格、程式碼、特殊字符</li>
    </ul>
    
    <p>如果轉檔結果包含所有這些內容，表示解析完整。如果缺少任何部分，則需要修正解析邏輯。</p>
    
    <p><em>測試結束 - 這是最後一段文字。</em></p>
</body>
</html>'''
    }
    
    try:
        with zipfile.ZipFile(epub_path, 'w', zipfile.ZIP_DEFLATED) as epub_zip:
            for file_path, content in files.items():
                epub_zip.writestr(file_path, content.encode('utf-8'))
        
        print(f"✅ 建立測試 EPUB: {epub_path}")
        return epub_path
        
    except Exception as e:
        print(f"❌ 建立 EPUB 失敗: {e}")
        return None


def analyze_epub_structure(epub_path):
    """分析 EPUB 結構"""
    print(f"\n🔍 分析 EPUB 結構: {epub_path}")
    print("-" * 50)
    
    try:
        with zipfile.ZipFile(epub_path, 'r') as epub_zip:
            # 列出所有檔案
            file_list = epub_zip.namelist()
            print(f"📁 EPUB 包含 {len(file_list)} 個檔案:")
            for file in file_list:
                print(f"  • {file}")
            
            # 解析 container.xml
            container_content = epub_zip.read('META-INF/container.xml').decode('utf-8')
            print(f"\n📄 Container.xml 內容:")
            print(f"  長度: {len(container_content)} 字符")
            
            # 解析 content.opf
            opf_files = [f for f in file_list if f.endswith('.opf')]
            if opf_files:
                opf_content = epub_zip.read(opf_files[0]).decode('utf-8')
                print(f"\n📄 OPF 檔案內容:")
                print(f"  檔案: {opf_files[0]}")
                print(f"  長度: {len(opf_content)} 字符")
                
                # 解析章節檔案
                root = ET.fromstring(opf_content)
                namespaces = {'opf': 'http://www.idpf.org/2007/opf'}
                
                manifest_items = root.findall('.//opf:item', namespaces)
                spine_items = root.findall('.//opf:itemref', namespaces)
                
                print(f"  Manifest 項目: {len(manifest_items)}")
                print(f"  Spine 項目: {len(spine_items)}")
                
                # 分析每個 HTML 檔案
                html_files = [item.get('href') for item in manifest_items 
                             if item.get('media-type') == 'application/xhtml+xml']
                
                print(f"\n📚 HTML 章節檔案:")
                total_chars = 0
                for html_file in html_files:
                    if html_file in file_list:
                        html_content = epub_zip.read(html_file).decode('utf-8')
                        char_count = len(html_content)
                        total_chars += char_count
                        print(f"  • {html_file}: {char_count} 字符")
                
                print(f"\n📊 總計: {total_chars} 字符")
                return html_files, total_chars
            
    except Exception as e:
        print(f"❌ 分析失敗: {e}")
        return [], 0


def test_current_extraction(epub_path):
    """測試當前的文字提取邏輯"""
    print(f"\n🧪 測試當前文字提取邏輯")
    print("-" * 50)
    
    try:
        from main import EbookConverter
        
        converter = EbookConverter()
        
        # 使用當前的 EPUB 文字提取方法
        extracted_text = converter._extract_text_from_epub(epub_path)
        
        print(f"📝 提取結果:")
        print(f"  提取文字長度: {len(extracted_text)} 字符")
        print(f"  前 200 字符: {extracted_text[:200]}...")
        print(f"  後 200 字符: ...{extracted_text[-200:]}")
        
        # 檢查關鍵內容
        key_content = [
            "第一章：開始",
            "第二章：發展", 
            "第三章：結論",
            "無序列表項目",
            "表格測試",
            "程式碼測試",
            "最終統計",
            "測試結束"
        ]
        
        print(f"\n🔍 關鍵內容檢查:")
        missing_content = []
        for content in key_content:
            if content in extracted_text:
                print(f"  ✅ 找到: {content}")
            else:
                print(f"  ❌ 缺失: {content}")
                missing_content.append(content)
        
        if missing_content:
            print(f"\n⚠️ 發現 {len(missing_content)} 項缺失內容")
            return False, len(extracted_text), missing_content
        else:
            print(f"\n✅ 所有關鍵內容都已提取")
            return True, len(extracted_text), []
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False, 0, []


def test_improved_extraction(epub_path):
    """測試改進的文字提取邏輯"""
    print(f"\n🔧 測試改進的文字提取邏輯")
    print("-" * 50)
    
    try:
        extracted_text = ""
        
        with zipfile.ZipFile(epub_path, 'r') as epub_zip:
            # 解析 OPF 檔案
            opf_files = [f for f in epub_zip.namelist() if f.endswith('.opf')]
            if not opf_files:
                print("❌ 未找到 OPF 檔案")
                return False, 0, []
            
            opf_content = epub_zip.read(opf_files[0]).decode('utf-8')
            root = ET.fromstring(opf_content)
            namespaces = {'opf': 'http://www.idpf.org/2007/opf'}
            
            # 獲取 spine 順序
            spine_items = root.findall('.//opf:itemref', namespaces)
            manifest_items = root.findall('.//opf:item', namespaces)
            
            # 建立 ID 到 href 的映射
            id_to_href = {item.get('id'): item.get('href') for item in manifest_items}
            
            print(f"📚 按 spine 順序處理章節:")
            
            for spine_item in spine_items:
                idref = spine_item.get('idref')
                if idref in id_to_href:
                    href = id_to_href[idref]
                    
                    try:
                        html_content = epub_zip.read(href).decode('utf-8')
                        
                        # 使用 BeautifulSoup 解析 HTML
                        soup = BeautifulSoup(html_content, 'html.parser')
                        
                        # 移除 script 和 style 標籤
                        for script in soup(["script", "style"]):
                            script.decompose()
                        
                        # 提取文字，保留結構
                        text = soup.get_text(separator='\n', strip=True)
                        
                        # 清理多餘的空行
                        lines = [line.strip() for line in text.split('\n') if line.strip()]
                        chapter_text = '\n'.join(lines)
                        
                        extracted_text += f"\n=== {href} ===\n{chapter_text}\n"
                        
                        print(f"  ✅ {href}: {len(chapter_text)} 字符")
                        
                    except Exception as e:
                        print(f"  ❌ {href}: 處理失敗 - {e}")
        
        print(f"\n📝 改進提取結果:")
        print(f"  總文字長度: {len(extracted_text)} 字符")
        print(f"  前 300 字符: {extracted_text[:300]}...")
        
        # 檢查關鍵內容
        key_content = [
            "第一章：開始",
            "第二章：發展", 
            "第三章：結論",
            "無序列表項目",
            "表格測試",
            "程式碼測試",
            "最終統計",
            "測試結束"
        ]
        
        print(f"\n🔍 關鍵內容檢查:")
        missing_content = []
        for content in key_content:
            if content in extracted_text:
                print(f"  ✅ 找到: {content}")
            else:
                print(f"  ❌ 缺失: {content}")
                missing_content.append(content)
        
        if missing_content:
            print(f"\n⚠️ 仍有 {len(missing_content)} 項缺失內容")
            return False, len(extracted_text), missing_content
        else:
            print(f"\n🎉 改進後所有內容都已提取！")
            return True, len(extracted_text), []
            
    except Exception as e:
        print(f"❌ 改進測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False, 0, []


def main():
    """主程式"""
    print("🔍 轉檔內容缺漏診斷工具")
    print("=" * 60)
    print("檢查 EPUB 解析和文字提取的完整性")
    print()
    
    # 建立測試檔案
    epub_file = create_comprehensive_test_epub()
    if not epub_file:
        return
    
    try:
        # 分析 EPUB 結構
        html_files, total_chars = analyze_epub_structure(epub_file)
        
        # 測試當前提取邏輯
        current_success, current_chars, current_missing = test_current_extraction(epub_file)
        
        # 測試改進提取邏輯
        improved_success, improved_chars, improved_missing = test_improved_extraction(epub_file)
        
        # 總結報告
        print(f"\n📊 診斷報告:")
        print("=" * 40)
        print(f"原始 EPUB 內容: {total_chars} 字符")
        print(f"當前提取結果: {current_chars} 字符 ({'✅ 完整' if current_success else '❌ 不完整'})")
        print(f"改進提取結果: {improved_chars} 字符 ({'✅ 完整' if improved_success else '❌ 不完整'})")
        
        if current_missing:
            print(f"\n⚠️ 當前方法缺失內容:")
            for item in current_missing:
                print(f"  • {item}")
        
        if improved_missing:
            print(f"\n⚠️ 改進方法仍缺失:")
            for item in improved_missing:
                print(f"  • {item}")
        
        # 提供解決方案
        if not current_success and improved_success:
            print(f"\n💡 解決方案:")
            print("改進的提取邏輯可以解決內容缺漏問題")
            print("建議更新主程式的文字提取方法")
        elif not improved_success:
            print(f"\n⚠️ 需要進一步調查:")
            print("可能需要更深入的 EPUB 解析邏輯")
        else:
            print(f"\n✅ 當前邏輯已經正常")
            print("可能是特定檔案的問題")
        
    finally:
        # 清理測試檔案
        if Path(epub_file).exists():
            Path(epub_file).unlink()
            print(f"\n🗑️ 清理測試檔案: {epub_file}")


if __name__ == '__main__':
    main()
