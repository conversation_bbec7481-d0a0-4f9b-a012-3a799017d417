"""
測試修正後的內容提取功能
驗證是否解決了內容缺漏問題
"""

from main import EbookConverter, ConversionStatus
from pathlib import Path
import zipfile
import time


def create_comprehensive_test_epub():
    """建立包含豐富內容的測試 EPUB"""
    print("📚 建立完整內容測試 EPUB...")
    
    epub_path = "content_extraction_test.epub"
    
    files = {
        'mimetype': 'application/epub+zip',
        'META-INF/container.xml': '''<?xml version="1.0"?>
<container version="1.0" xmlns="urn:oasis:names:tc:opendocument:xmlns:container">
  <rootfiles>
    <rootfile full-path="content.opf" media-type="application/oebps-package+xml"/>
  </rootfiles>
</container>''',
        'content.opf': '''<?xml version="1.0" encoding="UTF-8"?>
<package xmlns="http://www.idpf.org/2007/opf" unique-identifier="BookId" version="2.0">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <dc:title>內容完整性測試電子書</dc:title>
    <dc:creator>測試工具</dc:creator>
    <dc:language>zh-TW</dc:language>
  </metadata>
  <manifest>
    <item id="chapter1" href="chapter1.html" media-type="application/xhtml+xml"/>
    <item id="chapter2" href="chapter2.html" media-type="application/xhtml+xml"/>
    <item id="chapter3" href="chapter3.html" media-type="application/xhtml+xml"/>
  </manifest>
  <spine>
    <itemref idref="chapter1"/>
    <itemref idref="chapter2"/>
    <itemref idref="chapter3"/>
  </spine>
</package>''',
        'chapter1.html': '''<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>第一章</title>
    <meta charset="UTF-8"/>
</head>
<body>
    <h1>第一章：開始的故事</h1>
    
    <p>這是第一章的開頭段落。這段文字應該完整出現在轉檔結果中，不應該有任何缺漏。</p>
    
    <p>第二段包含<strong>粗體文字</strong>和<em>斜體文字</em>，還有<u>底線文字</u>。這些格式化文字都應該被正確提取。</p>
    
    <h2>1.1 重要小節</h2>
    <p>這個小節包含重要內容：數字測試 123456789，英文測試 ABCDEFGHIJKLMNOPQRSTUVWXYZ，中文測試 一二三四五六七八九十。</p>
    
    <h2>1.2 列表內容測試</h2>
    <ul>
        <li>第一個無序列表項目 - 重要內容A</li>
        <li>第二個無序列表項目 - 重要內容B</li>
        <li>第三個無序列表項目 - 重要內容C</li>
    </ul>
    
    <ol>
        <li>第一個有序列表項目 - 步驟一</li>
        <li>第二個有序列表項目 - 步驟二</li>
        <li>第三個有序列表項目 - 步驟三</li>
    </ol>
    
    <p>第一章結束段落。這裡應該包含大約 300 個字符的完整內容，用於測試提取的完整性。</p>
</body>
</html>''',
        'chapter2.html': '''<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>第二章</title>
    <meta charset="UTF-8"/>
</head>
<body>
    <h1>第二章：發展的過程</h1>
    
    <p>第二章開始了更複雜的內容測試。這裡有更多的文字來驗證解析的完整性和準確性。</p>
    
    <blockquote>
        <p>這是一個重要的引用段落。引用的內容應該被完整提取，不能有任何遺漏。這段引用包含了關鍵信息。</p>
    </blockquote>
    
    <h2>2.1 表格數據測試</h2>
    <table>
        <tr>
            <th>項目名稱</th>
            <th>數量</th>
            <th>備註</th>
        </tr>
        <tr>
            <td>重要數據A</td>
            <td>100</td>
            <td>第一組數據</td>
        </tr>
        <tr>
            <td>重要數據B</td>
            <td>200</td>
            <td>第二組數據</td>
        </tr>
        <tr>
            <td>重要數據C</td>
            <td>300</td>
            <td>第三組數據</td>
        </tr>
    </table>
    
    <h2>2.2 特殊字符和符號測試</h2>
    <p>特殊符號測試：©®™€£¥§¶†‡•…‰‹›""''–—</p>
    <p>數學符號測試：±×÷≠≤≥∞∑∏∫√∂∆∇</p>
    <p>中文標點測試：，。！？；：（）【】《》〈〉「」『』</p>
    
    <p>第二章包含表格、引用和特殊字符，總計約 400 個字符的豐富內容。</p>
</body>
</html>''',
        'chapter3.html': '''<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>第三章</title>
    <meta charset="UTF-8"/>
</head>
<body>
    <h1>第三章：完整的結論</h1>
    
    <p>最後一章包含最複雜的內容測試。這裡驗證長段落和複雜結構的處理能力。</p>
    
    <p>這是一個非常長的段落，專門用來測試長文字的處理能力。段落中包含各種標點符號，如逗號、句號、問號、驚嘆號等。同時也包含括號（像這樣的內容），以及各種引號「中文引號」和『中文書名號』。這個段落的目的是確保長文字不會被截斷、分割或遺漏，所有內容都應該完整地出現在最終的轉檔結果中。</p>
    
    <h2>3.1 程式碼和預格式化文字測試</h2>
    <pre><code>
function testFunction() {
    console.log("這是程式碼測試");
    var data = "重要的程式碼內容";
    return data;
}
    </code></pre>
    
    <h2>3.2 混合內容綜合測試</h2>
    <p>混合內容包括：</p>
    <ul>
        <li>繁體中文：電腦軟體網路資訊技術系統</li>
        <li>簡體中文：电脑软件网络信息技术系统</li>
        <li>英文內容：Computer Software Network Information Technology System</li>
        <li>數字內容：1234567890 以及 ①②③④⑤⑥⑦⑧⑨⑩</li>
        <li>符號內容：!@#$%^&*()_+-=[]{}|;:,.<>?</li>
    </ul>
    
    <h2>3.3 最終統計和驗證</h2>
    <p><strong>內容統計資訊：</strong></p>
    <ul>
        <li>總章節數量：3 章完整內容</li>
        <li>總段落數量：約 20 個重要段落</li>
        <li>總字符數量：超過 1000 個字符</li>
        <li>包含元素：標題、段落、列表、表格、引用、程式碼、特殊字符</li>
        <li>測試重點：完整性、順序性、格式保留</li>
    </ul>
    
    <p>如果轉檔結果包含所有這些詳細內容，並且順序正確，表示內容提取功能已經完全修正。如果缺少任何部分，則需要進一步調整解析邏輯。</p>
    
    <p><em>內容完整性測試結束 - 這是電子書的最後一段文字，應該完整出現。</em></p>
</body>
</html>'''
    }
    
    try:
        with zipfile.ZipFile(epub_path, 'w', zipfile.ZIP_DEFLATED) as epub_zip:
            for file_path, content in files.items():
                epub_zip.writestr(file_path, content.encode('utf-8'))
        
        print(f"✅ 建立測試 EPUB: {epub_path}")
        return epub_path
        
    except Exception as e:
        print(f"❌ 建立 EPUB 失敗: {e}")
        return None


def test_content_extraction():
    """測試內容提取功能"""
    print("\n🧪 測試修正後的內容提取功能")
    print("-" * 50)
    
    # 建立測試檔案
    epub_file = create_comprehensive_test_epub()
    if not epub_file:
        return False
    
    # 測試轉換為不同格式
    formats = ['txt', 'html', 'pdf']
    results = {}
    
    converter = EbookConverter()
    
    for format_ext in formats:
        print(f"\n📋 測試 EPUB → {format_ext.upper()} 轉換")
        
        output_file = f"content_test_output.{format_ext}"
        
        result = {"completed": False, "success": False, "message": ""}
        
        def callback(status: ConversionStatus, message: str):
            print(f"  📊 {status.value}: {message}")
            if status in [ConversionStatus.SUCCESS, ConversionStatus.FAILED]:
                result["completed"] = True
                result["success"] = (status == ConversionStatus.SUCCESS)
                result["message"] = message
        
        # 開始轉換
        converter.convert_ebook_async(epub_file, output_file, callback)
        
        # 等待完成
        timeout = 30
        start_time = time.time()
        
        while not result["completed"] and (time.time() - start_time) < timeout:
            time.sleep(0.1)
        
        # 檢查結果
        if result["completed"] and result["success"]:
            if Path(output_file).exists():
                file_size = Path(output_file).stat().st_size
                print(f"  ✅ 轉換成功，檔案大小: {file_size} bytes")
                
                # 檢查內容完整性
                try:
                    with open(output_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 檢查關鍵內容
                    key_content = [
                        "第一章：開始的故事",
                        "第二章：發展的過程", 
                        "第三章：完整的結論",
                        "重要內容A",
                        "重要數據A",
                        "程式碼測試",
                        "內容統計資訊",
                        "內容完整性測試結束"
                    ]
                    
                    missing_content = []
                    for key in key_content:
                        if key not in content:
                            missing_content.append(key)
                    
                    if missing_content:
                        print(f"  ⚠️ 缺失內容: {len(missing_content)} 項")
                        for item in missing_content[:3]:  # 只顯示前3項
                            print(f"    • {item}")
                        results[format_ext] = False
                    else:
                        print(f"  🎉 內容完整！所有關鍵內容都已提取")
                        results[format_ext] = True
                        
                except Exception as e:
                    print(f"  ❌ 內容檢查失敗: {e}")
                    results[format_ext] = False
            else:
                print(f"  ❌ 檔案未生成")
                results[format_ext] = False
        else:
            print(f"  ❌ 轉換失敗: {result.get('message', '未知錯誤')}")
            results[format_ext] = False
    
    return results


def cleanup_test_files():
    """清理測試檔案"""
    print(f"\n🗑️ 清理測試檔案...")
    
    test_files = [
        'content_extraction_test.epub',
        'content_test_output.txt',
        'content_test_output.html',
        'content_test_output.pdf'
    ]
    
    cleaned = 0
    for file in test_files:
        if Path(file).exists():
            Path(file).unlink()
            cleaned += 1
            print(f"  清理: {file}")
    
    print(f"清理了 {cleaned} 個檔案")


def main():
    """主程式"""
    print("🔍 內容提取修正驗證工具")
    print("=" * 60)
    print("測試修正後的 EPUB 內容提取是否解決了缺漏問題")
    print()
    
    try:
        # 測試內容提取
        results = test_content_extraction()
        
        if results:
            # 總結報告
            print(f"\n📊 測試結果總結:")
            print("=" * 40)
            
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            
            for format_ext, success in results.items():
                status = "✅ 完整" if success else "❌ 缺漏"
                print(f"  {format_ext.upper()} 格式: {status}")
            
            print(f"\n🎯 總計: {success_count}/{total_count} 個格式內容完整")
            
            if success_count == total_count:
                print(f"\n🎉 完美！內容缺漏問題已完全解決")
                print("💡 現在您可以:")
                print("  • 轉檔時獲得完整內容")
                print("  • 保持正確的章節順序")
                print("  • 提取所有文字元素")
                print("  • 處理複雜的 EPUB 結構")
            elif success_count > 0:
                print(f"\n⚠️ 部分格式已修正，仍需改進")
                print("建議重新啟動程式或檢查特定格式處理")
            else:
                print(f"\n❌ 內容缺漏問題仍然存在")
                print("需要進一步調查和修正")
        
        cleanup_test_files()
        
    except Exception as e:
        print(f"💥 測試過程中發生例外: {e}")
        cleanup_test_files()


if __name__ == '__main__':
    main()
