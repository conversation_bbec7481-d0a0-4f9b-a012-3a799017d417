"""
簡單測試內容短少修正
"""

import zipfile
from pathlib import Path


def create_test_epub_with_content():
    """建立包含豐富內容的測試 EPUB"""
    epub_path = "content_fix_test.epub"
    
    files = {
        'mimetype': 'application/epub+zip',
        'META-INF/container.xml': '''<?xml version="1.0"?>
<container version="1.0" xmlns="urn:oasis:names:tc:opendocument:xmlns:container">
  <rootfiles>
    <rootfile full-path="content.opf" media-type="application/oebps-package+xml"/>
  </rootfiles>
</container>''',
        'content.opf': '''<?xml version="1.0" encoding="UTF-8"?>
<package xmlns="http://www.idpf.org/2007/opf" unique-identifier="BookId" version="2.0">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <dc:title>內容修正測試</dc:title>
    <dc:creator>測試工具</dc:creator>
  </metadata>
  <manifest>
    <item id="ch1" href="chapter1.html" media-type="application/xhtml+xml"/>
    <item id="ch2" href="chapter2.html" media-type="application/xhtml+xml"/>
    <item id="ch3" href="chapter3.html" media-type="application/xhtml+xml"/>
  </manifest>
  <spine>
    <itemref idref="ch1"/>
    <itemref idref="ch2"/>
    <itemref idref="ch3"/>
  </spine>
</package>''',
        'chapter1.html': '''<!DOCTYPE html>
<html>
<head><title>第一章</title></head>
<body>
    <h1>第一章：重要開始</h1>
    <p>這是第一章的重要內容。這段文字必須完整出現在轉檔結果中。</p>
    <p>第一章包含多個段落，每個段落都有重要資訊。</p>
    <h2>1.1 重要小節</h2>
    <p>小節內容包含關鍵資訊：數據A、數據B、數據C。</p>
    <ul>
        <li>重要列表項目一：關鍵資訊A</li>
        <li>重要列表項目二：關鍵資訊B</li>
        <li>重要列表項目三：關鍵資訊C</li>
    </ul>
    <p>第一章結束段落，包含總結性資訊。</p>
</body>
</html>''',
        'chapter2.html': '''<!DOCTYPE html>
<html>
<head><title>第二章</title></head>
<body>
    <h1>第二章：核心內容</h1>
    <p>第二章開始，包含更多重要內容。</p>
    <blockquote>
        <p>這是重要的引用內容，必須被完整提取。</p>
    </blockquote>
    <h2>2.1 數據表格</h2>
    <table>
        <tr><th>項目</th><th>數值</th><th>說明</th></tr>
        <tr><td>數據1</td><td>100</td><td>重要數據</td></tr>
        <tr><td>數據2</td><td>200</td><td>關鍵數據</td></tr>
    </table>
    <h2>2.2 詳細說明</h2>
    <p>這裡有詳細的說明文字，包含技術細節和重要資訊。</p>
    <ol>
        <li>步驟一：執行初始化</li>
        <li>步驟二：處理數據</li>
        <li>步驟三：生成結果</li>
    </ol>
    <p>第二章結束，包含重要的總結資訊。</p>
</body>
</html>''',
        'chapter3.html': '''<!DOCTYPE html>
<html>
<head><title>第三章</title></head>
<body>
    <h1>第三章：最終總結</h1>
    <p>最後一章包含總結性內容和重要結論。</p>
    <h2>3.1 重要結論</h2>
    <p>經過前面章節的分析，我們得出以下重要結論：</p>
    <ul>
        <li>結論A：系統運行正常</li>
        <li>結論B：數據處理完整</li>
        <li>結論C：結果符合預期</li>
    </ul>
    <h2>3.2 技術細節</h2>
    <pre><code>
// 重要程式碼範例
function processData(input) {
    return input.map(item => item * 2);
}
    </code></pre>
    <h2>3.3 最終說明</h2>
    <p>這是最後的說明段落，包含所有重要的總結資訊。如果這段文字出現在轉檔結果中，表示內容提取是完整的。</p>
    <p><strong>測試完成標記：內容提取測試結束</strong></p>
</body>
</html>'''
    }
    
    with zipfile.ZipFile(epub_path, 'w', zipfile.ZIP_DEFLATED) as epub_zip:
        for file_path, content in files.items():
            epub_zip.writestr(file_path, content.encode('utf-8'))
    
    return epub_path


def test_content_extraction():
    """測試內容提取"""
    print("🧪 內容短少修正測試")
    print("=" * 50)
    
    # 建立測試檔案
    epub_file = create_test_epub_with_content()
    print(f"✅ 建立測試 EPUB: {epub_file}")
    
    # 計算原始內容
    original_content = ""
    with zipfile.ZipFile(epub_file, 'r') as epub_zip:
        for html_file in ['chapter1.html', 'chapter2.html', 'chapter3.html']:
            content = epub_zip.read(html_file).decode('utf-8')
            original_content += content
    
    print(f"📄 原始 HTML 總長度: {len(original_content)} 字符")
    
    # 測試轉換
    try:
        from main import EbookConverter, ConversionStatus
        
        converter = EbookConverter()
        output_file = "content_fix_test_output.txt"
        
        def callback(status, message):
            print(f"📊 {status.value}: {message}")
        
        # 執行轉換
        success = converter._extract_epub_text(epub_file, output_file, 'txt', callback)
        
        if success and Path(output_file).exists():
            with open(output_file, 'r', encoding='utf-8') as f:
                extracted_content = f.read()
            
            print(f"📝 提取內容長度: {len(extracted_content)} 字符")
            
            # 檢查關鍵內容
            key_items = [
                "第一章：重要開始",
                "第二章：核心內容",
                "第三章：最終總結",
                "重要列表項目一",
                "重要的引用內容",
                "數據1",
                "步驟一：執行初始化",
                "結論A：系統運行正常",
                "processData",
                "測試完成標記：內容提取測試結束"
            ]
            
            print(f"\n🔍 關鍵內容檢查 (共 {len(key_items)} 項):")
            found_items = []
            missing_items = []
            
            for item in key_items:
                if item in extracted_content:
                    print(f"  ✅ {item}")
                    found_items.append(item)
                else:
                    print(f"  ❌ {item}")
                    missing_items.append(item)
            
            # 計算完整度
            completeness = (len(found_items) / len(key_items)) * 100
            
            print(f"\n📊 內容完整度分析:")
            print(f"  找到項目: {len(found_items)}/{len(key_items)}")
            print(f"  完整度: {completeness:.1f}%")
            
            if completeness >= 90:
                print(f"  🎉 內容提取優秀！")
                result = "優秀"
            elif completeness >= 70:
                print(f"  ✅ 內容提取良好")
                result = "良好"
            elif completeness >= 50:
                print(f"  ⚠️ 內容提取一般")
                result = "一般"
            else:
                print(f"  ❌ 內容提取不足")
                result = "不足"
            
            if missing_items:
                print(f"\n⚠️ 缺失的重要內容:")
                for item in missing_items[:5]:  # 只顯示前5項
                    print(f"    • {item}")
            
            # 顯示部分提取內容
            print(f"\n📖 提取內容預覽:")
            print("-" * 30)
            preview = extracted_content[:300] + "..." if len(extracted_content) > 300 else extracted_content
            print(preview)
            
            return result, completeness
            
        else:
            print("❌ 轉換失敗")
            return "失敗", 0
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return "錯誤", 0
    
    finally:
        # 清理測試檔案
        for file in [epub_file, "content_fix_test_output.txt"]:
            if Path(file).exists():
                Path(file).unlink()


def main():
    """主程式"""
    print("🔍 內容短少問題修正驗證")
    print("=" * 60)
    print("測試增強的內容提取是否解決了短少問題")
    print()
    
    result, completeness = test_content_extraction()
    
    print(f"\n📊 最終測試結果:")
    print("=" * 30)
    print(f"測試結果: {result}")
    print(f"完整度: {completeness:.1f}%")
    
    if result in ["優秀", "良好"]:
        print(f"\n🎉 內容短少問題已顯著改善！")
        print("💡 建議:")
        print("  • 重新啟動電子書轉檔工具")
        print("  • 重新轉檔您的 EPUB 檔案")
        print("  • 檢查轉檔結果的完整性")
    elif result == "一般":
        print(f"\n⚠️ 內容提取有改善但仍需優化")
        print("💡 建議:")
        print("  • 檢查原始 EPUB 檔案結構")
        print("  • 嘗試不同的輸出格式")
        print("  • 考慮使用專業轉檔工具")
    else:
        print(f"\n❌ 內容短少問題仍然存在")
        print("💡 建議:")
        print("  • 檢查 EPUB 檔案是否損壞")
        print("  • 嘗試其他轉檔工具")
        print("  • 聯繫技術支援")


if __name__ == '__main__':
    main()
