"""
調試 ReportLab PDF 生成問題
"""

import traceback
from pathlib import Path


def test_reportlab_basic():
    """測試基本 ReportLab 功能"""
    print("🧪 測試基本 ReportLab 功能")
    print("-" * 40)
    
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.enums import TA_CENTER
        
        print("✅ ReportLab 導入成功")
        
        # 建立測試 PDF
        output_path = "debug_test.pdf"
        doc = SimpleDocTemplate(output_path, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # 添加標題
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER
        )
        
        title = Paragraph("ReportLab 調試測試", title_style)
        story.append(title)
        story.append(Spacer(1, 20))
        
        # 添加內容
        content = Paragraph("這是一個 ReportLab 調試測試檔案。", styles['Normal'])
        story.append(content)
        
        # 生成 PDF
        doc.build(story)
        
        if Path(output_path).exists():
            file_size = Path(output_path).stat().st_size
            print(f"✅ PDF 生成成功，檔案大小: {file_size} bytes")
            Path(output_path).unlink()  # 清理
            return True
        else:
            print("❌ PDF 檔案未生成")
            return False
            
    except Exception as e:
        print(f"❌ ReportLab 測試失敗: {e}")
        print("詳細錯誤:")
        traceback.print_exc()
        return False


def test_epub_to_pdf_debug():
    """調試 EPUB → PDF 轉換"""
    print("\n🔍 調試 EPUB → PDF 轉換")
    print("-" * 40)
    
    try:
        from main import EbookConverter, ConversionStatus
        import zipfile
        
        # 建立測試 EPUB
        epub_path = "debug_test.epub"
        
        files = {
            'mimetype': 'application/epub+zip',
            'META-INF/container.xml': '''<?xml version="1.0"?>
<container version="1.0" xmlns="urn:oasis:names:tc:opendocument:xmlns:container">
  <rootfiles>
    <rootfile full-path="content.opf" media-type="application/oebps-package+xml"/>
  </rootfiles>
</container>''',
            'content.opf': '''<?xml version="1.0" encoding="UTF-8"?>
<package xmlns="http://www.idpf.org/2007/opf" unique-identifier="BookId" version="2.0">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <dc:title>調試測試</dc:title>
    <dc:creator>ReportLab 調試</dc:creator>
  </metadata>
  <manifest>
    <item id="chapter1" href="chapter1.html" media-type="application/xhtml+xml"/>
  </manifest>
  <spine>
    <itemref idref="chapter1"/>
  </spine>
</package>''',
            'chapter1.html': '''<!DOCTYPE html>
<html>
<head>
    <title>調試測試</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>ReportLab 調試測試</h1>
    <p>這是一個調試測試檔案。</p>
    <p>測試 ReportLab PDF 生成功能。</p>
</body>
</html>'''
        }
        
        with zipfile.ZipFile(epub_path, 'w', zipfile.ZIP_DEFLATED) as epub_zip:
            for file_path, content in files.items():
                epub_zip.writestr(file_path, content.encode('utf-8'))
        
        print(f"✅ 建立測試 EPUB: {epub_path}")
        
        # 測試轉換
        output_path = "debug_output.pdf"
        converter = EbookConverter()
        
        # 直接調用內部方法進行調試
        print("🔄 開始調試轉換...")
        
        def debug_callback(status: ConversionStatus, message: str):
            print(f"  📊 {status.value}: {message}")
        
        # 調用 EPUB → PDF 轉換方法
        result = converter._convert_epub_to_pdf(epub_path, output_path, debug_callback)
        
        print(f"轉換結果: {result}")
        
        if Path(output_path).exists():
            file_size = Path(output_path).stat().st_size
            print(f"✅ PDF 生成成功，檔案大小: {file_size} bytes")
            success = True
        else:
            print("❌ PDF 檔案未生成")
            success = False
        
        # 清理
        for file in [epub_path, output_path]:
            if Path(file).exists():
                Path(file).unlink()
        
        return success
        
    except Exception as e:
        print(f"❌ 調試失敗: {e}")
        print("詳細錯誤:")
        traceback.print_exc()
        return False


def test_html_to_pdf_direct():
    """直接測試 HTML → PDF 轉換"""
    print("\n🎯 直接測試 HTML → PDF 轉換")
    print("-" * 40)
    
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.enums import TA_CENTER
        
        # 建立測試 HTML
        html_content = """<!DOCTYPE html>
<html>
<head>
    <title>HTML 測試</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>HTML → PDF 測試</h1>
    <p>這是一個 HTML 轉 PDF 的測試。</p>
    <ul>
        <li>項目 1</li>
        <li>項目 2</li>
        <li>項目 3</li>
    </ul>
    <p><strong>測試完成！</strong></p>
</body>
</html>"""
        
        html_path = "debug_test.html"
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ 建立測試 HTML: {html_path}")
        
        # 提取文字內容（模擬程式邏輯）
        def extract_text_from_html(html_content):
            import re
            # 移除 script 和 style 標籤
            html_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
            html_content = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
            # 移除所有 HTML 標籤
            text = re.sub(r'<[^>]+>', '', html_content)
            # 清理空白字符
            text = re.sub(r'\s+', ' ', text)
            return text.strip()
        
        text_content = extract_text_from_html(html_content)
        print(f"📝 提取的文字內容: {text_content[:100]}...")
        
        # 生成 PDF
        output_path = "debug_html_to_pdf.pdf"
        doc = SimpleDocTemplate(output_path, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # 標題
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER
        )
        
        title = Paragraph("HTML → PDF 測試", title_style)
        story.append(title)
        story.append(Spacer(1, 20))
        
        # 內容
        lines = text_content.split('\n')
        for line in lines:
            line = line.strip()
            if line:
                # 處理特殊字符
                line = line.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
                para = Paragraph(line, styles['Normal'])
                story.append(para)
                story.append(Spacer(1, 6))
        
        # 生成 PDF
        doc.build(story)
        
        if Path(output_path).exists():
            file_size = Path(output_path).stat().st_size
            print(f"✅ HTML → PDF 轉換成功，檔案大小: {file_size} bytes")
            success = True
        else:
            print("❌ PDF 檔案未生成")
            success = False
        
        # 清理
        for file in [html_path, output_path]:
            if Path(file).exists():
                Path(file).unlink()
        
        return success
        
    except Exception as e:
        print(f"❌ HTML → PDF 測試失敗: {e}")
        print("詳細錯誤:")
        traceback.print_exc()
        return False


def main():
    """主程式"""
    print("🔍 ReportLab 調試工具")
    print("=" * 50)
    
    tests = [
        ("基本 ReportLab 功能", test_reportlab_basic),
        ("HTML → PDF 直接轉換", test_html_to_pdf_direct),
        ("EPUB → PDF 調試", test_epub_to_pdf_debug)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 測試: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"💥 測試例外: {e}")
            results.append((test_name, False))
    
    # 總結
    print(f"\n📊 調試結果總結:")
    print("=" * 30)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總計: {passed}/{len(results)} 個測試通過")
    
    if passed == len(results):
        print("\n🎉 ReportLab 功能完全正常！")
        print("問題可能在程式邏輯或錯誤處理")
    elif passed > 0:
        print("\n⚠️ ReportLab 部分功能正常")
        print("需要進一步調試程式整合")
    else:
        print("\n❌ ReportLab 功能異常")
        print("需要檢查 ReportLab 安裝")


if __name__ == '__main__':
    main()
