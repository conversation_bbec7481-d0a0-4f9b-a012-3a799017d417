"""
測試 EPUB 轉換功能
驗證內建 EPUB 文字提取是否正常工作
"""

from main import EbookConverter, ConversionStatus
from pathlib import Path
import time
import zipfile


def create_sample_epub():
    """建立一個簡單的測試 EPUB 檔案"""
    print("📚 建立測試 EPUB 檔案...")
    
    epub_path = "test_sample.epub"
    
    # EPUB 檔案結構
    files = {
        'mimetype': 'application/epub+zip',
        'META-INF/container.xml': '''<?xml version="1.0"?>
<container version="1.0" xmlns="urn:oasis:names:tc:opendocument:xmlns:container">
  <rootfiles>
    <rootfile full-path="OEBPS/content.opf" media-type="application/oebps-package+xml"/>
  </rootfiles>
</container>''',
        'OEBPS/content.opf': '''<?xml version="1.0" encoding="UTF-8"?>
<package xmlns="http://www.idpf.org/2007/opf" unique-identifier="BookId" version="2.0">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <dc:title>測試電子書</dc:title>
    <dc:creator>電子書轉檔工具</dc:creator>
    <dc:language>zh-TW</dc:language>
    <dc:identifier id="BookId">test-ebook-001</dc:identifier>
  </metadata>
  <manifest>
    <item id="chapter1" href="chapter1.html" media-type="application/xhtml+xml"/>
    <item id="chapter2" href="chapter2.html" media-type="application/xhtml+xml"/>
  </manifest>
  <spine>
    <itemref idref="chapter1"/>
    <itemref idref="chapter2"/>
  </spine>
</package>''',
        'OEBPS/chapter1.html': '''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>第一章</title>
</head>
<body>
    <h1>第一章：電子書轉檔工具介紹</h1>
    <p>這是一個功能強大的電子書轉檔工具。</p>
    <p>主要特色包括：</p>
    <ul>
        <li>支援多種格式</li>
        <li>自動輸出路徑</li>
        <li>多引擎支援</li>
        <li>內建轉換器</li>
    </ul>
</body>
</html>''',
        'OEBPS/chapter2.html': '''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>第二章</title>
</head>
<body>
    <h1>第二章：使用方式</h1>
    <p>使用電子書轉檔工具非常簡單：</p>
    <ol>
        <li>選擇輸入檔案</li>
        <li>選擇輸出格式</li>
        <li>開始轉檔</li>
    </ol>
    <p>感謝使用我們的工具！</p>
</body>
</html>'''
    }
    
    try:
        with zipfile.ZipFile(epub_path, 'w', zipfile.ZIP_DEFLATED) as epub_zip:
            for file_path, content in files.items():
                epub_zip.writestr(file_path, content.encode('utf-8'))
        
        print(f"  ✅ 建立測試 EPUB: {epub_path}")
        return epub_path
        
    except Exception as e:
        print(f"  ❌ 建立 EPUB 失敗: {e}")
        return None


def test_epub_to_txt():
    """測試 EPUB → TXT 轉換"""
    print("\n🔄 測試 EPUB → TXT 轉換")
    print("-" * 30)
    
    epub_file = create_sample_epub()
    if not epub_file:
        return False
    
    output_file = "test_epub_output.txt"
    
    converter = EbookConverter()
    result = {"completed": False, "success": False, "message": ""}
    
    def callback(status: ConversionStatus, message: str):
        print(f"  📊 {status.value}: {message}")
        if status in [ConversionStatus.SUCCESS, ConversionStatus.FAILED]:
            result["completed"] = True
            result["success"] = (status == ConversionStatus.SUCCESS)
            result["message"] = message
    
    # 開始轉換
    converter.convert_ebook_async(epub_file, output_file, callback)
    
    # 等待完成
    timeout = 15
    start_time = time.time()
    
    while not result["completed"] and (time.time() - start_time) < timeout:
        time.sleep(0.1)
    
    if result["completed"] and result["success"]:
        if Path(output_file).exists():
            file_size = Path(output_file).stat().st_size
            print(f"  ✅ 轉換成功，檔案大小: {file_size} bytes")
            
            # 顯示部分內容
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"  📝 輸出內容預覽:")
                print("  " + "-" * 20)
                preview = content[:300] + "..." if len(content) > 300 else content
                for line in preview.split('\n')[:10]:
                    print(f"  {line}")
                print("  " + "-" * 20)
            
            return True
        else:
            print(f"  ❌ 轉換報告成功但檔案未生成")
            return False
    else:
        print(f"  ❌ 轉換失敗: {result.get('message', '未知錯誤')}")
        return False


def test_epub_to_html():
    """測試 EPUB → HTML 轉換"""
    print("\n🔄 測試 EPUB → HTML 轉換")
    print("-" * 30)
    
    epub_file = "test_sample.epub"
    if not Path(epub_file).exists():
        epub_file = create_sample_epub()
        if not epub_file:
            return False
    
    output_file = "test_epub_output.html"
    
    converter = EbookConverter()
    result = {"completed": False, "success": False}
    
    def callback(status: ConversionStatus, message: str):
        print(f"  📊 {status.value}: {message}")
        if status in [ConversionStatus.SUCCESS, ConversionStatus.FAILED]:
            result["completed"] = True
            result["success"] = (status == ConversionStatus.SUCCESS)
    
    converter.convert_ebook_async(epub_file, output_file, callback)
    
    # 等待完成
    timeout = 15
    start_time = time.time()
    
    while not result["completed"] and (time.time() - start_time) < timeout:
        time.sleep(0.1)
    
    if result["completed"] and result["success"]:
        if Path(output_file).exists():
            file_size = Path(output_file).stat().st_size
            print(f"  ✅ 轉換成功，檔案大小: {file_size} bytes")
            return True
        else:
            print(f"  ❌ 轉換報告成功但檔案未生成")
            return False
    else:
        print(f"  ❌ 轉換失敗")
        return False


def cleanup_test_files():
    """清理測試檔案"""
    print(f"\n🗑️ 清理測試檔案...")
    
    test_files = [
        'test_sample.epub',
        'test_epub_output.txt',
        'test_epub_output.html'
    ]
    
    cleaned = 0
    for file in test_files:
        if Path(file).exists():
            Path(file).unlink()
            cleaned += 1
    
    print(f"  清理了 {cleaned} 個檔案")


def main():
    """主程式"""
    print("🧪 EPUB 轉換功能測試")
    print("=" * 40)
    
    try:
        # 檢查引擎狀態
        from main import EbookConverter
        converter = EbookConverter()
        engines = converter.available_engines
        
        print("🔍 可用引擎:")
        for name, available in engines.items():
            status = "✅" if available else "❌"
            print(f"  {name}: {status}")
        
        if not engines.get('builtin', False):
            print("\n❌ 內建轉換器不可用，無法進行測試")
            return
        
        # 執行測試
        test1_result = test_epub_to_txt()
        test2_result = test_epub_to_html()
        
        # 總結
        print(f"\n📊 測試結果:")
        print(f"  EPUB → TXT: {'✅ 通過' if test1_result else '❌ 失敗'}")
        print(f"  EPUB → HTML: {'✅ 通過' if test2_result else '❌ 失敗'}")
        
        if test1_result or test2_result:
            print("\n🎉 EPUB 文字提取功能正常！")
            print("\n💡 使用建議:")
            print("  • 可以將 EPUB 轉為 TXT 或 HTML")
            print("  • HTML 檔案可以用瀏覽器開啟並列印為 PDF")
            print("  • 建議安裝 Pandoc 以獲得直接的 PDF 輸出")
        else:
            print("\n❌ EPUB 轉換功能異常")
        
        cleanup_test_files()
        
    except Exception as e:
        print(f"💥 測試過程中發生例外: {e}")
        cleanup_test_files()


if __name__ == '__main__':
    main()
