"""
自動輸出路徑功能演示
展示修改後的電子書轉檔工具如何自動生成輸出檔案路徑
"""

from pathlib import Path
import tkinter as tk
from tkinter import ttk


class AutoOutputDemo:
    """自動輸出路徑演示類別"""
    
    def __init__(self):
        self.supported_formats = ['epub', 'pdf', 'mobi', 'azw3', 'txt', 'html', 'docx']
        self.setup_demo()
    
    def setup_demo(self):
        """設定演示介面"""
        self.root = tk.Tk()
        self.root.title("自動輸出路徑功能演示")
        self.root.geometry("600x400")
        
        # 標題
        title_label = tk.Label(
            self.root, 
            text="📁 自動輸出路徑功能演示", 
            font=("Arial", 16, "bold"),
            fg="blue"
        )
        title_label.pack(pady=10)
        
        # 說明文字
        description = tk.Label(
            self.root,
            text="此演示展示如何根據輸入檔案自動生成輸出檔案路徑",
            font=("Arial", 10),
            fg="gray"
        )
        description.pack(pady=5)
        
        # 輸入檔案模擬
        input_frame = tk.Frame(self.root)
        input_frame.pack(pady=10, padx=20, fill="x")
        
        tk.Label(input_frame, text="輸入檔案路徑:", font=("Arial", 10, "bold")).pack(anchor="w")
        self.input_entry = tk.Entry(input_frame, width=70, font=("Arial", 9))
        self.input_entry.pack(fill="x", pady=5)
        self.input_entry.bind('<KeyRelease>', self.on_input_change)
        
        # 格式選擇
        format_frame = tk.Frame(self.root)
        format_frame.pack(pady=10, padx=20, fill="x")
        
        tk.Label(format_frame, text="輸出格式:", font=("Arial", 10, "bold")).pack(anchor="w")
        self.format_var = tk.StringVar(value=self.supported_formats[0])
        self.format_combo = ttk.Combobox(
            format_frame, 
            textvariable=self.format_var,
            values=self.supported_formats,
            state="readonly",
            width=15
        )
        self.format_combo.pack(anchor="w", pady=5)
        self.format_combo.bind('<<ComboboxSelected>>', self.on_format_change)
        
        # 輸出檔案顯示
        output_frame = tk.Frame(self.root)
        output_frame.pack(pady=10, padx=20, fill="x")
        
        tk.Label(output_frame, text="自動生成的輸出路徑:", font=("Arial", 10, "bold")).pack(anchor="w")
        self.output_entry = tk.Entry(output_frame, width=70, font=("Arial", 9), state="readonly", bg="#f0f0f0")
        self.output_entry.pack(fill="x", pady=5)
        
        # 範例按鈕
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=20)
        
        examples = [
            ("📖 EPUB 範例", "C:/Books/小說.epub"),
            ("📄 PDF 範例", "D:/Documents/報告.pdf"),
            ("📱 MOBI 範例", "/home/<USER>/電子書.mobi"),
            ("📝 TXT 範例", "E:/Text/文章.txt")
        ]
        
        for i, (text, path) in enumerate(examples):
            btn = tk.Button(
                button_frame,
                text=text,
                command=lambda p=path: self.set_example(p),
                width=15,
                bg="#e3f2fd",
                relief="raised"
            )
            btn.grid(row=i//2, column=i%2, padx=5, pady=5)
        
        # 功能說明
        info_frame = tk.Frame(self.root)
        info_frame.pack(pady=20, padx=20, fill="both", expand=True)
        
        tk.Label(info_frame, text="✨ 功能特色:", font=("Arial", 12, "bold"), fg="green").pack(anchor="w")
        
        features = [
            "• 自動偵測輸入檔案的目錄位置",
            "• 根據選擇的輸出格式自動生成副檔名",
            "• 在原檔名後加上 '_converted' 避免覆蓋原檔案",
            "• 即時更新輸出路徑預覽",
            "• 簡化使用者操作流程"
        ]
        
        for feature in features:
            tk.Label(info_frame, text=feature, font=("Arial", 9), anchor="w").pack(anchor="w", pady=2)
    
    def on_input_change(self, event=None):
        """當輸入路徑改變時更新輸出路徑"""
        self.update_output_path()
    
    def on_format_change(self, event=None):
        """當格式改變時更新輸出路徑"""
        self.update_output_path()
    
    def update_output_path(self):
        """更新輸出路徑"""
        input_path = self.input_entry.get().strip()
        if not input_path:
            self.set_output_path("")
            return
        
        try:
            input_file = Path(input_path)
            output_format = self.format_var.get()
            
            # 生成輸出檔案名稱
            output_filename = f"{input_file.stem}_converted.{output_format}"
            output_path = input_file.parent / output_filename
            
            self.set_output_path(str(output_path))
            
        except Exception:
            self.set_output_path("無效的輸入路徑")
    
    def set_output_path(self, path):
        """設定輸出路徑顯示"""
        self.output_entry.config(state="normal")
        self.output_entry.delete(0, tk.END)
        self.output_entry.insert(0, path)
        self.output_entry.config(state="readonly")
    
    def set_example(self, example_path):
        """設定範例路徑"""
        self.input_entry.delete(0, tk.END)
        self.input_entry.insert(0, example_path)
        self.update_output_path()
    
    def run(self):
        """執行演示"""
        # 設定初始範例
        self.set_example("C:/Books/我的電子書.epub")
        
        print("🚀 自動輸出路徑功能演示啟動")
        print("=" * 40)
        print("請在介面中嘗試:")
        print("1. 修改輸入檔案路徑")
        print("2. 切換不同的輸出格式")
        print("3. 點擊範例按鈕查看效果")
        print("4. 觀察輸出路徑如何自動更新")
        
        self.root.mainloop()


def demonstrate_path_generation():
    """演示路徑生成邏輯"""
    print("\n📁 路徑生成邏輯演示")
    print("-" * 30)
    
    test_cases = [
        ("C:/Books/小說.epub", "pdf"),
        ("D:/Documents/報告.pdf", "epub"),
        ("/home/<USER>/電子書.mobi", "txt"),
        ("E:/Text/文章.txt", "azw3"),
        ("./local/檔案.docx", "html")
    ]
    
    for input_path, output_format in test_cases:
        input_file = Path(input_path)
        output_filename = f"{input_file.stem}_converted.{output_format}"
        output_path = input_file.parent / output_filename
        
        print(f"輸入: {input_path}")
        print(f"格式: {output_format}")
        print(f"輸出: {output_path}")
        print()


def main():
    """主程式"""
    print("🎯 電子書轉檔工具 - 自動輸出路徑功能")
    print("=" * 50)
    
    # 先展示邏輯演示
    demonstrate_path_generation()
    
    # 啟動 GUI 演示
    demo = AutoOutputDemo()
    demo.run()


if __name__ == '__main__':
    main()
