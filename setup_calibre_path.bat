@echo off
echo === Calibre 環境變數設定工具 ===
echo.

REM 檢查常見的 Calibre 安裝路徑
set CALIBRE_PATH=

if exist "C:\Program Files\Calibre2\ebook-convert.exe" (
    set CALIBRE_PATH=C:\Program Files\Calibre2
    echo 找到 Calibre 安裝於: %CALIBRE_PATH%
    goto :found
)

if exist "C:\Program Files (x86)\Calibre2\ebook-convert.exe" (
    set CALIBRE_PATH=C:\Program Files ^(x86^)\Calibre2
    echo 找到 Calibre 安裝於: %CALIBRE_PATH%
    goto :found
)

if exist "%LOCALAPPDATA%\Programs\Calibre\ebook-convert.exe" (
    set CALIBRE_PATH=%LOCALAPPDATA%\Programs\Calibre
    echo 找到 Calibre 安裝於: %CALIBRE_PATH%
    goto :found
)

echo 未找到 Calibre 安裝！
echo 請先安裝 Calibre: https://calibre-ebook.com/download_windows
echo 或手動指定 Calibre 安裝路徑
pause
exit /b 1

:found
echo.
echo 正在檢查環境變數...

REM 檢查是否已在 PATH 中
echo %PATH% | findstr /i "%CALIBRE_PATH%" >nul
if %errorlevel% == 0 (
    echo Calibre 路徑已存在於 PATH 中
    goto :test
)

echo 正在添加 Calibre 到 PATH...
setx PATH "%PATH%;%CALIBRE_PATH%"
if %errorlevel% == 0 (
    echo ✓ 成功添加到環境變數
    echo 請重新啟動命令提示字元或 PowerShell 以使變更生效
) else (
    echo ✗ 添加環境變數失敗
)

:test
echo.
echo 正在測試 ebook-convert...
"%CALIBRE_PATH%\ebook-convert.exe" --version
if %errorlevel% == 0 (
    echo ✓ ebook-convert 可正常使用
) else (
    echo ✗ ebook-convert 執行失敗
)

echo.
echo === 使用示例 ===
echo 轉換 EPUB 到 PDF: ebook-convert input.epub output.pdf
echo 轉換 MOBI 到 EPUB: ebook-convert input.mobi output.epub
echo 查看幫助: ebook-convert --help
echo.
pause
