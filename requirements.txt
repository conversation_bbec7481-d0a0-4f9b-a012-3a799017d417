# 電子書轉檔工具 - 優化版本
# Python 依賴需求檔案

# 核心依賴 (Python 內建模組)
# tkinter - GUI 框架 (Python 內建)
# subprocess - 執行外部命令 (Python 內建)
# threading - 多執行緒支援 (Python 內建)
# pathlib - 路徑處理 (Python 內建)
# typing - 型別提示 (Python 內建)
# enum - 枚舉類型 (Python 內建)
# logging - 日誌記錄 (Python 內建)

# 外部依賴
# 目前無需額外的 Python 套件

# 系統依賴
# Calibre - 電子書轉檔引擎
#   下載位置: https://calibre-ebook.com/
#   安裝後需將 ebook-convert 加入系統 PATH

# 開發依賴 (可選)
# unittest - 單元測試 (Python 內建)

# Python 版本需求
# Python >= 3.7

# 注意事項:
# 1. 本專案主要使用 Python 內建模組，無需額外安裝 pip 套件
# 2. 唯一的外部依賴是 Calibre，需要單獨安裝
# 3. 可使用提供的 install_calibre.ps1 或 setup_calibre_path.bat 自動安裝 Calibre