"""
電子書轉檔工具測試檔案
測試核心功能和驗證邏輯
"""

import unittest
from unittest.mock import patch, MagicMock
from pathlib import Path
import tempfile
import os

# 匯入要測試的模組
from main import EbookConverter, AppConstants, ConversionStatus


class TestEbookConverter(unittest.TestCase):
    """測試 EbookConverter 類別"""
    
    def setUp(self):
        """測試前準備"""
        self.converter = EbookConverter()
        
        # 建立臨時測試檔案
        self.temp_dir = tempfile.mkdtemp()
        self.test_input_file = Path(self.temp_dir) / "test.epub"
        self.test_input_file.touch()  # 建立空檔案
        
        self.test_output_file = Path(self.temp_dir) / "output.pdf"
    
    def tearDown(self):
        """測試後清理"""
        # 清理臨時檔案
        if self.test_input_file.exists():
            self.test_input_file.unlink()
        if self.test_output_file.exists():
            self.test_output_file.unlink()
        os.rmdir(self.temp_dir)
    
    def test_validate_input_file_valid(self):
        """測試有效輸入檔案驗證"""
        result = self.converter.validate_input_file(str(self.test_input_file))
        self.assertTrue(result)
    
    def test_validate_input_file_empty_path(self):
        """測試空路徑驗證"""
        result = self.converter.validate_input_file("")
        self.assertFalse(result)
    
    def test_validate_input_file_nonexistent(self):
        """測試不存在的檔案"""
        result = self.converter.validate_input_file("/nonexistent/file.epub")
        self.assertFalse(result)
    
    def test_validate_input_file_unsupported_format(self):
        """測試不支援的格式"""
        unsupported_file = Path(self.temp_dir) / "test.xyz"
        unsupported_file.touch()
        
        result = self.converter.validate_input_file(str(unsupported_file))
        self.assertFalse(result)
        
        unsupported_file.unlink()
    
    def test_validate_output_path_valid(self):
        """測試有效輸出路徑"""
        result = self.converter.validate_output_path(str(self.test_output_file))
        self.assertTrue(result)
    
    def test_validate_output_path_empty(self):
        """測試空輸出路徑"""
        result = self.converter.validate_output_path("")
        self.assertFalse(result)
    
    def test_validate_output_path_invalid_directory(self):
        """測試無效目錄"""
        invalid_path = "/nonexistent/directory/output.pdf"
        result = self.converter.validate_output_path(invalid_path)
        self.assertFalse(result)
    
    @patch('subprocess.run')
    def test_check_calibre_available_success(self, mock_run):
        """測試 Calibre 可用性檢查 - 成功"""
        mock_run.return_value.returncode = 0
        result = self.converter.check_calibre_available()
        self.assertTrue(result)
    
    @patch('subprocess.run')
    def test_check_calibre_available_failure(self, mock_run):
        """測試 Calibre 可用性檢查 - 失敗"""
        mock_run.side_effect = FileNotFoundError()
        result = self.converter.check_calibre_available()
        self.assertFalse(result)


class TestAppConstants(unittest.TestCase):
    """測試應用程式常數"""
    
    def test_supported_formats_not_empty(self):
        """測試支援格式列表不為空"""
        self.assertGreater(len(AppConstants.SUPPORTED_FORMATS), 0)
    
    def test_supported_formats_contains_common_formats(self):
        """測試包含常見格式"""
        common_formats = ['epub', 'pdf', 'mobi']
        for fmt in common_formats:
            self.assertIn(fmt, AppConstants.SUPPORTED_FORMATS)
    
    def test_messages_not_empty(self):
        """測試訊息字典不為空"""
        self.assertGreater(len(AppConstants.MESSAGES), 0)
    
    def test_required_messages_exist(self):
        """測試必要訊息存在"""
        required_keys = [
            'select_input', 'invalid_format', 'file_not_found',
            'conversion_success', 'conversion_failed', 'calibre_not_found'
        ]
        for key in required_keys:
            self.assertIn(key, AppConstants.MESSAGES)


class TestConversionStatus(unittest.TestCase):
    """測試轉檔狀態枚舉"""
    
    def test_status_values_not_empty(self):
        """測試狀態值不為空"""
        for status in ConversionStatus:
            self.assertIsNotNone(status.value)
            self.assertNotEqual(status.value.strip(), "")
    
    def test_all_required_statuses_exist(self):
        """測試所有必要狀態存在"""
        required_statuses = ['IDLE', 'CONVERTING', 'SUCCESS', 'FAILED']
        existing_statuses = [status.name for status in ConversionStatus]
        
        for status in required_statuses:
            self.assertIn(status, existing_statuses)


def run_tests():
    """執行所有測試"""
    print("🧪 開始執行電子書轉檔工具測試...")
    
    # 建立測試套件
    test_suite = unittest.TestSuite()
    
    # 加入測試類別
    test_classes = [TestEbookConverter, TestAppConstants, TestConversionStatus]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 執行測試
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 顯示結果摘要
    print(f"\n📊 測試結果摘要:")
    print(f"   執行測試: {result.testsRun}")
    print(f"   失敗: {len(result.failures)}")
    print(f"   錯誤: {len(result.errors)}")
    
    if result.wasSuccessful():
        print("✅ 所有測試通過！")
        return True
    else:
        print("❌ 部分測試失敗")
        return False


if __name__ == '__main__':
    run_tests()
