"""
測試中文 PDF 亂碼修正
驗證字體和編碼問題是否解決
"""

from main import EbookConverter, ConversionStatus
from pathlib import Path
import zipfile
import time


def create_chinese_test_epub():
    """建立包含中文內容的測試 EPUB"""
    print("📚 建立中文測試 EPUB...")
    
    epub_path = "chinese_test.epub"
    
    # 包含各種中文內容的 EPUB
    files = {
        'mimetype': 'application/epub+zip',
        'META-INF/container.xml': '''<?xml version="1.0"?>
<container version="1.0" xmlns="urn:oasis:names:tc:opendocument:xmlns:container">
  <rootfiles>
    <rootfile full-path="content.opf" media-type="application/oebps-package+xml"/>
  </rootfiles>
</container>''',
        'content.opf': '''<?xml version="1.0" encoding="UTF-8"?>
<package xmlns="http://www.idpf.org/2007/opf" unique-identifier="BookId" version="2.0">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <dc:title>中文亂碼測試電子書</dc:title>
    <dc:creator>電子書轉檔工具</dc:creator>
    <dc:language>zh-TW</dc:language>
  </metadata>
  <manifest>
    <item id="chapter1" href="chapter1.html" media-type="application/xhtml+xml"/>
  </manifest>
  <spine>
    <itemref idref="chapter1"/>
  </spine>
</package>''',
        'chapter1.html': '''<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>中文測試章節</title>
    <meta charset="UTF-8"/>
</head>
<body>
    <h1>第一章：中文字體測試</h1>
    
    <h2>繁體中文測試</h2>
    <p>這是一個測試繁體中文顯示的段落。包含常用字：電腦、軟體、網路、資訊、技術。</p>
    
    <h2>簡體中文測試</h2>
    <p>这是一个测试简体中文显示的段落。包含常用字：电脑、软件、网络、信息、技术。</p>
    
    <h2>特殊符號測試</h2>
    <p>測試各種符號：「」『』""''—…※○●◎★☆△▲□■◇◆</p>
    
    <h2>數字和英文混合</h2>
    <p>測試 123 ABC 中英數混合：Windows 10、macOS 11、Ubuntu 20.04</p>
    
    <h2>標點符號測試</h2>
    <p>測試標點：，。！？；：（）【】《》〈〉</p>
    
    <h2>長文測試</h2>
    <p>這是一段較長的中文文字，用來測試在 PDF 中的顯示效果。電子書轉檔工具是一個功能強大的軟體，
    能夠將各種格式的電子書檔案進行轉換。它支援 EPUB、PDF、TXT、HTML、Markdown 等多種格式，
    並且提供了友善的使用者介面，讓使用者能夠輕鬆完成轉檔作業。</p>
    
    <h2>列表測試</h2>
    <ul>
        <li>支援多種輸入格式：EPUB、TXT、HTML、Markdown</li>
        <li>支援多種輸出格式：PDF、HTML、TXT、Markdown</li>
        <li>自動偵測檔案編碼</li>
        <li>智慧字體選擇</li>
        <li>完全自動化轉換</li>
    </ul>
    
    <h2>編號列表測試</h2>
    <ol>
        <li>選擇輸入檔案</li>
        <li>選擇輸出格式</li>
        <li>設定轉換參數</li>
        <li>開始轉檔</li>
        <li>檢查轉換結果</li>
    </ol>
    
    <p><strong>測試完成！如果您能正確看到所有中文字符，表示亂碼問題已解決。</strong></p>
</body>
</html>'''
    }
    
    try:
        with zipfile.ZipFile(epub_path, 'w', zipfile.ZIP_DEFLATED) as epub_zip:
            for file_path, content in files.items():
                epub_zip.writestr(file_path, content.encode('utf-8'))
        
        print(f"✅ 建立中文測試 EPUB: {epub_path}")
        return epub_path
        
    except Exception as e:
        print(f"❌ 建立 EPUB 失敗: {e}")
        return None


def test_chinese_pdf_conversion():
    """測試中文 PDF 轉換"""
    print("\n🔄 測試中文 PDF 轉換")
    print("-" * 50)
    
    # 建立測試檔案
    epub_file = create_chinese_test_epub()
    if not epub_file:
        return False
    
    output_file = "chinese_test_output.pdf"
    
    # 檢查引擎狀態
    converter = EbookConverter()
    engines = converter.available_engines
    
    print("🔍 可用引擎:")
    for name, available in engines.items():
        status = "✅" if available else "❌"
        print(f"  {name}: {status}")
    
    # 執行轉換
    print(f"\n🚀 開始中文 PDF 轉換: {epub_file} → {output_file}")
    
    result = {"completed": False, "success": False, "message": ""}
    
    def callback(status: ConversionStatus, message: str):
        print(f"  📊 {status.value}: {message}")
        if status in [ConversionStatus.SUCCESS, ConversionStatus.FAILED]:
            result["completed"] = True
            result["success"] = (status == ConversionStatus.SUCCESS)
            result["message"] = message
    
    # 開始轉換
    converter.convert_ebook_async(epub_file, output_file, callback)
    
    # 等待完成
    timeout = 30
    start_time = time.time()
    
    while not result["completed"] and (time.time() - start_time) < timeout:
        time.sleep(0.1)
    
    # 檢查結果
    if result["completed"] and result["success"]:
        if Path(output_file).exists():
            file_size = Path(output_file).stat().st_size
            print(f"\n🎉 中文 PDF 轉換成功！")
            print(f"   輸出檔案: {output_file}")
            print(f"   檔案大小: {file_size} bytes")
            
            # 檢查是否使用了 ReportLab
            if "ReportLab" in result["message"]:
                print(f"   使用引擎: ReportLab (支援中文字體)")
                return True
            else:
                print(f"   使用引擎: 其他")
                return True
        else:
            print(f"\n❌ 轉換報告成功但檔案未生成")
            return False
    else:
        print(f"\n❌ 轉換失敗: {result.get('message', '未知錯誤')}")
        return False


def test_font_registration():
    """測試字體註冊功能"""
    print("\n🔤 測試字體註冊功能")
    print("-" * 50)
    
    try:
        from main import EbookConverter
        
        converter = EbookConverter()
        
        # 測試字體註冊方法
        print("正在測試字體註冊...")
        converter._register_chinese_fonts()
        
        # 檢查字體是否註冊成功
        try:
            from reportlab.pdfbase import pdfmetrics
            font = pdfmetrics.getFont('ChineseFont')
            print("✅ 中文字體註冊成功")
            return True
        except:
            print("⚠️ 中文字體註冊失敗，將使用默認字體")
            return False
            
    except Exception as e:
        print(f"❌ 字體註冊測試失敗: {e}")
        return False


def test_text_cleaning():
    """測試文字清理功能"""
    print("\n🧹 測試文字清理功能")
    print("-" * 50)
    
    try:
        from main import EbookConverter
        
        converter = EbookConverter()
        
        # 測試各種問題文字
        test_texts = [
            "正常中文文字",
            "包含&lt;HTML&gt;標籤的文字",
            "包含"特殊引號"的文字",
            "包含—長破折號—的文字",
            "包含…省略號的文字",
            "包含\u00a0不間斷空格的文字",
            "混合English和中文的text",
            "數字123和符號!@#的混合"
        ]
        
        print("測試文字清理功能:")
        all_success = True
        
        for i, text in enumerate(test_texts, 1):
            try:
                cleaned = converter._clean_text_for_pdf(text)
                safe = converter._make_text_safe_for_pdf(cleaned)
                print(f"  {i}. 原文: {text[:30]}...")
                print(f"     清理後: {safe[:30]}...")
                print(f"     ✅ 成功")
            except Exception as e:
                print(f"  {i}. ❌ 失敗: {e}")
                all_success = False
        
        return all_success
        
    except Exception as e:
        print(f"❌ 文字清理測試失敗: {e}")
        return False


def cleanup_test_files():
    """清理測試檔案"""
    print(f"\n🗑️ 清理測試檔案...")
    
    test_files = [
        'chinese_test.epub',
        'chinese_test_output.pdf'
    ]
    
    cleaned = 0
    for file in test_files:
        if Path(file).exists():
            Path(file).unlink()
            cleaned += 1
            print(f"  清理: {file}")
    
    print(f"清理了 {cleaned} 個檔案")


def main():
    """主程式"""
    print("🔤 中文 PDF 亂碼修正測試")
    print("=" * 60)
    print("測試字體註冊、編碼處理和中文顯示")
    print()
    
    tests = [
        ("字體註冊", test_font_registration),
        ("文字清理", test_text_cleaning),
        ("中文 PDF 轉換", test_chinese_pdf_conversion)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 測試: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"💥 測試例外: {e}")
            results.append((test_name, False))
    
    # 總結
    print(f"\n📊 測試結果總結:")
    print("=" * 30)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總計: {passed}/{len(results)} 個測試通過")
    
    if passed == len(results):
        print("\n🎉 完美！中文亂碼問題已完全解決")
        print("\n💡 現在您可以:")
        print("1. 轉換包含中文的 EPUB 檔案")
        print("2. 生成正確顯示中文的 PDF")
        print("3. 支援繁體、簡體中文和特殊符號")
        print("4. 自動處理編碼和字體問題")
    elif passed >= 2:
        print("\n⚠️ 大部分功能正常，可能有小問題")
        print("建議檢查字體安裝或重新啟動程式")
    else:
        print("\n❌ 中文支援仍有問題")
        print("可能需要手動安裝中文字體或檢查系統設定")
    
    cleanup_test_files()


if __name__ == '__main__':
    main()
