"""
測試修正後的轉換功能
驗證內建轉換器是否能正確處理支援的格式
"""

from main import EbookConverter, ConversionStatus
from pathlib import Path
import time


def create_test_files():
    """建立測試檔案"""
    print("📝 建立測試檔案...")
    
    # TXT 檔案
    txt_content = """電子書轉檔工具測試

第一章：功能介紹
這是一個多引擎電子書轉檔工具。

主要特色：
- 支援多種格式
- 自動輸出路徑
- 內建轉換器

第二章：使用方式
1. 選擇輸入檔案
2. 選擇輸出格式
3. 開始轉檔

結語：
感謝使用！"""
    
    with open('test.txt', 'w', encoding='utf-8') as f:
        f.write(txt_content)
    print("  ✅ 建立 test.txt")
    
    # HTML 檔案
    html_content = """<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>測試 HTML</title>
</head>
<body>
    <h1>電子書轉檔工具測試</h1>
    <h2>功能介紹</h2>
    <p>這是一個 HTML 測試檔案。</p>
    <ul>
        <li>支援 HTML 轉 TXT</li>
        <li>支援 TXT 轉 HTML</li>
        <li>支援 Markdown 格式</li>
    </ul>
</body>
</html>"""
    
    with open('test.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    print("  ✅ 建立 test.html")
    
    # Markdown 檔案
    md_content = """# 電子書轉檔工具測試

## 功能介紹
這是一個 Markdown 測試檔案。

### 主要特色
- 支援多種格式
- 自動輸出路徑
- 內建轉換器

## 使用方式
1. 選擇輸入檔案
2. 選擇輸出格式
3. 開始轉檔

> 感謝使用電子書轉檔工具！"""
    
    with open('test.md', 'w', encoding='utf-8') as f:
        f.write(md_content)
    print("  ✅ 建立 test.md")


def test_conversion(input_file: str, output_file: str, description: str):
    """測試單一轉換"""
    print(f"\n🔄 測試: {description}")
    print(f"   {input_file} → {output_file}")
    
    if not Path(input_file).exists():
        print(f"  ❌ 輸入檔案不存在: {input_file}")
        return False
    
    converter = EbookConverter()
    result = {"completed": False, "success": False, "message": ""}
    
    def callback(status: ConversionStatus, message: str):
        print(f"  📊 {status.value}: {message}")
        if status in [ConversionStatus.SUCCESS, ConversionStatus.FAILED]:
            result["completed"] = True
            result["success"] = (status == ConversionStatus.SUCCESS)
            result["message"] = message
    
    # 開始轉換
    converter.convert_ebook_async(input_file, output_file, callback)
    
    # 等待完成
    timeout = 10
    start_time = time.time()
    
    while not result["completed"] and (time.time() - start_time) < timeout:
        time.sleep(0.1)
    
    if result["completed"] and result["success"]:
        if Path(output_file).exists():
            file_size = Path(output_file).stat().st_size
            print(f"  ✅ 轉換成功，檔案大小: {file_size} bytes")
            return True
        else:
            print(f"  ❌ 轉換報告成功但檔案未生成")
            return False
    else:
        print(f"  ❌ 轉換失敗: {result.get('message', '未知錯誤')}")
        return False


def run_all_tests():
    """執行所有測試"""
    print("🧪 執行修正後的轉換測試")
    print("=" * 40)
    
    # 建立測試檔案
    create_test_files()
    
    # 測試案例
    test_cases = [
        ('test.txt', 'test_txt_to_html.html', 'TXT → HTML'),
        ('test.html', 'test_html_to_txt.txt', 'HTML → TXT'),
        ('test.txt', 'test_txt_to_md.md', 'TXT → Markdown'),
        ('test.md', 'test_md_to_html.html', 'Markdown → HTML'),
        ('test.md', 'test_md_to_txt.txt', 'Markdown → TXT'),
    ]
    
    results = []
    
    for input_file, output_file, description in test_cases:
        success = test_conversion(input_file, output_file, description)
        results.append((description, success))
    
    # 總結
    print(f"\n📊 測試結果總結:")
    print("-" * 30)
    
    passed = 0
    for description, success in results:
        status = "✅ 通過" if success else "❌ 失敗"
        print(f"  {description}: {status}")
        if success:
            passed += 1
    
    print(f"\n🎯 總計: {passed}/{len(results)} 個測試通過")
    
    if passed == len(results):
        print("🎉 所有測試通過！內建轉換器功能完全正常")
    elif passed > 0:
        print("⚠️ 部分測試通過，基本功能可用")
    else:
        print("❌ 所有測試失敗，需要檢查程式碼")
    
    return passed > 0


def cleanup_test_files():
    """清理測試檔案"""
    print(f"\n🗑️ 清理測試檔案...")
    
    test_files = [
        'test.txt', 'test.html', 'test.md',
        'test_txt_to_html.html', 'test_html_to_txt.txt',
        'test_txt_to_md.md', 'test_md_to_html.html', 'test_md_to_txt.txt'
    ]
    
    cleaned = 0
    for file in test_files:
        if Path(file).exists():
            Path(file).unlink()
            cleaned += 1
    
    print(f"  清理了 {cleaned} 個檔案")


def main():
    """主程式"""
    try:
        success = run_all_tests()
        
        if success:
            print(f"\n💡 現在您可以使用以下格式轉換:")
            print("  • TXT ↔ HTML")
            print("  • TXT ↔ Markdown")
            print("  • HTML ↔ TXT")
            print("  • Markdown ↔ HTML")
            print(f"\n🚀 啟動主程式: python main.py")
        else:
            print(f"\n❌ 測試失敗，請檢查程式碼或聯繫技術支援")
        
        cleanup_test_files()
        
    except Exception as e:
        print(f"💥 測試過程中發生例外: {e}")
        cleanup_test_files()


if __name__ == '__main__':
    main()
