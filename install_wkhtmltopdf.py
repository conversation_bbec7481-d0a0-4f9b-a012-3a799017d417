"""
安裝 wkhtmltopdf 實現完全自動化 EPUB → PDF
"""

import subprocess
import sys
import os
import urllib.request
import zipfile
from pathlib import Path
import shutil


def install_wkhtmltopdf():
    """安裝 wkhtmltopdf"""
    print("🌐 安裝 wkhtmltopdf - 實現一次轉檔完成")
    print("=" * 50)
    
    # 檢查是否已安裝
    if test_wkhtmltopdf():
        print("✅ wkhtmltopdf 已經安裝")
        return True
    
    # 方法 1: winget
    if install_via_winget():
        return True
    
    # 方法 2: 可攜版
    if install_portable():
        return True
    
    # 方法 3: 手動指引
    show_manual_instructions()
    return False


def test_wkhtmltopdf():
    """測試 wkhtmltopdf"""
    try:
        result = subprocess.run(['wkhtmltopdf', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ wkhtmltopdf 版本: {version_line}")
            return True
        return False
    except:
        return False


def install_via_winget():
    """使用 winget 安裝"""
    print("📦 嘗試使用 winget 安裝...")
    
    try:
        result = subprocess.run([
            'winget', 'install', '--id=wkhtmltopdf.wkhtmltopdf', '-e', '--accept-source-agreements'
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ winget 安裝成功")
            return test_wkhtmltopdf()
        else:
            print(f"❌ winget 安裝失敗: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ winget 安裝例外: {e}")
        return False


def install_portable():
    """安裝可攜版 wkhtmltopdf"""
    print("📥 下載 wkhtmltopdf 可攜版...")
    
    try:
        # wkhtmltopdf 下載 URL
        download_url = "https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6-1/wkhtmltox-0.12.6-1.msvc2015-win64.exe"
        
        print("正在下載安裝檔...")
        installer_path = "wkhtmltopdf_installer.exe"
        
        urllib.request.urlretrieve(download_url, installer_path)
        print("✅ 下載完成")
        
        print("請執行下載的安裝檔完成安裝:")
        print(f"檔案位置: {Path(installer_path).absolute()}")
        
        # 嘗試自動執行安裝檔
        try:
            subprocess.run([installer_path], check=False)
        except:
            pass
        
        return False  # 需要手動完成安裝
        
    except Exception as e:
        print(f"❌ 下載失敗: {e}")
        return False


def show_manual_instructions():
    """顯示手動安裝指引"""
    print("\n🔧 手動安裝 wkhtmltopdf:")
    print("-" * 30)
    print("1. 前往官方網站:")
    print("   https://wkhtmltopdf.org/downloads.html")
    print()
    print("2. 下載 Windows 版本:")
    print("   選擇 'Windows (MSVC 2015) 64-bit'")
    print()
    print("3. 執行安裝檔並完成安裝")
    print()
    print("4. 重新啟動命令提示字元")
    print()
    print("5. 測試安裝: wkhtmltopdf --version")


def test_complete_conversion():
    """測試完整轉換流程"""
    print("\n🧪 測試完整 EPUB → PDF 轉換")
    print("-" * 40)
    
    if not test_wkhtmltopdf():
        print("❌ wkhtmltopdf 不可用，無法測試")
        return False
    
    # 設定 Pandoc PATH
    pandoc_exe = Path("pandoc-3.1.8/pandoc.exe")
    if pandoc_exe.exists():
        pandoc_dir = str(pandoc_exe.parent.absolute())
        current_path = os.environ.get('PATH', '')
        if pandoc_dir not in current_path:
            os.environ['PATH'] = f"{pandoc_dir};{current_path}"
    
    # 建立測試 EPUB
    epub_content = create_test_epub()
    if not epub_content:
        return False
    
    # 測試轉換
    try:
        print("正在測試 Pandoc + wkhtmltopdf 轉換...")
        
        cmd = [
            'pandoc',
            'test_complete.epub',
            '-o', 'test_complete.pdf',
            '--pdf-engine=wkhtmltopdf'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0 and Path('test_complete.pdf').exists():
            file_size = Path('test_complete.pdf').stat().st_size
            print(f"🎉 完整轉換成功！")
            print(f"   PDF 檔案大小: {file_size} bytes")
            
            # 清理測試檔案
            Path('test_complete.epub').unlink()
            Path('test_complete.pdf').unlink()
            
            return True
        else:
            print(f"❌ 轉換失敗: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 測試例外: {e}")
        return False


def create_test_epub():
    """建立測試 EPUB"""
    try:
        import zipfile
        
        epub_path = "test_complete.epub"
        
        files = {
            'mimetype': 'application/epub+zip',
            'META-INF/container.xml': '''<?xml version="1.0"?>
<container version="1.0" xmlns="urn:oasis:names:tc:opendocument:xmlns:container">
  <rootfiles>
    <rootfile full-path="content.opf" media-type="application/oebps-package+xml"/>
  </rootfiles>
</container>''',
            'content.opf': '''<?xml version="1.0" encoding="UTF-8"?>
<package xmlns="http://www.idpf.org/2007/opf" unique-identifier="BookId" version="2.0">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <dc:title>完整轉換測試</dc:title>
    <dc:creator>電子書轉檔工具</dc:creator>
  </metadata>
  <manifest>
    <item id="chapter1" href="chapter1.html" media-type="application/xhtml+xml"/>
  </manifest>
  <spine>
    <itemref idref="chapter1"/>
  </spine>
</package>''',
            'chapter1.html': '''<!DOCTYPE html>
<html>
<head>
    <title>完整轉換測試</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>🎉 一次轉檔完成測試</h1>
    <p>這是一個測試 EPUB 檔案，用來驗證完全自動化的 EPUB → PDF 轉換。</p>
    <h2>功能特色</h2>
    <ul>
        <li>✅ 無需手動步驟</li>
        <li>✅ 一鍵完成轉換</li>
        <li>✅ 高品質 PDF 輸出</li>
    </ul>
    <p><strong>測試成功！</strong></p>
</body>
</html>'''
        }
        
        with zipfile.ZipFile(epub_path, 'w', zipfile.ZIP_DEFLATED) as epub_zip:
            for file_path, content in files.items():
                epub_zip.writestr(file_path, content.encode('utf-8'))
        
        print(f"✅ 建立測試 EPUB: {epub_path}")
        return True
        
    except Exception as e:
        print(f"❌ 建立測試 EPUB 失敗: {e}")
        return False


def main():
    """主程式"""
    print("🚀 wkhtmltopdf 安裝 - 實現一次轉檔完成")
    print("=" * 60)
    
    # 安裝 wkhtmltopdf
    if install_wkhtmltopdf():
        print("\n🎉 wkhtmltopdf 安裝成功！")
        
        # 測試完整轉換
        if test_complete_conversion():
            print("\n🌟 完美！一次轉檔完成功能已實現")
            print("\n💡 現在您可以:")
            print("1. 啟動程式: python main.py")
            print("2. 選擇 EPUB 檔案")
            print("3. 選擇 PDF 格式")
            print("4. 點擊轉檔 - 一次完成！")
            print("\n🎯 無需任何手動步驟，完全自動化！")
        else:
            print("\n⚠️ wkhtmltopdf 已安裝但轉換測試失敗")
            print("可能需要重新啟動命令提示字元")
    else:
        print("\n❌ wkhtmltopdf 安裝失敗")
        print("請按照手動安裝指引完成安裝")


if __name__ == '__main__':
    main()
