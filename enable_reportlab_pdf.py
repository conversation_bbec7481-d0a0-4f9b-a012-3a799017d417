"""
啟用 ReportLab 完全自動化 PDF 生成
修改程式使其能夠一次轉檔完成
"""

import subprocess
import sys
from pathlib import Path


def install_reportlab_dependencies():
    """安裝 ReportLab 相關依賴"""
    print("📚 安裝 ReportLab 完整依賴")
    print("-" * 40)
    
    libraries = [
        'reportlab',
        'Pillow',
        'pypdf',
        'html2text',
        'beautifulsoup4'
    ]
    
    success_count = 0
    
    for lib in libraries:
        try:
            print(f"📥 正在安裝 {lib}...")
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', lib, '--upgrade', '--quiet'
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print(f"✅ {lib} 安裝成功")
                success_count += 1
            else:
                print(f"❌ {lib} 安裝失敗")
        except Exception as e:
            print(f"❌ {lib} 安裝例外: {e}")
    
    print(f"\n📊 成功安裝: {success_count}/{len(libraries)} 個庫")
    return success_count > 0


def test_reportlab_pdf():
    """測試 ReportLab PDF 生成"""
    print("\n🧪 測試 ReportLab PDF 生成")
    print("-" * 40)
    
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet
        
        # 建立測試 PDF
        doc = SimpleDocTemplate("test_reportlab.pdf", pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # 添加內容
        title = Paragraph("ReportLab PDF 測試", styles['Title'])
        story.append(title)
        story.append(Spacer(1, 12))
        
        content = Paragraph("這是一個 ReportLab 生成的 PDF 測試檔案。", styles['Normal'])
        story.append(content)
        
        # 生成 PDF
        doc.build(story)
        
        if Path("test_reportlab.pdf").exists():
            file_size = Path("test_reportlab.pdf").stat().st_size
            print(f"✅ ReportLab PDF 生成成功")
            print(f"   檔案大小: {file_size} bytes")
            
            # 清理測試檔案
            Path("test_reportlab.pdf").unlink()
            return True
        else:
            print("❌ PDF 檔案未生成")
            return False
            
    except ImportError as e:
        print(f"❌ ReportLab 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ PDF 生成失敗: {e}")
        return False


def modify_main_program():
    """修改主程式以優先使用 ReportLab"""
    print("\n🔧 修改主程式優先使用 ReportLab")
    print("-" * 40)
    
    try:
        # 讀取 main.py
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查是否已經修改過
        if 'ReportLab 優先' in content:
            print("✅ 主程式已經修改過")
            return True
        
        # 找到 ReportLab 轉換部分並修改優先級
        old_pattern = '# 方法 2: 使用 reportlab (如果 weasyprint 失敗)'
        new_pattern = '# 方法 1: 使用 reportlab (ReportLab 優先)'
        
        if old_pattern in content:
            content = content.replace(old_pattern, new_pattern)
            
            # 調整 WeasyPrint 為備用方案
            content = content.replace(
                '# 方法 1: 使用 weasyprint',
                '# 方法 2: 使用 weasyprint (如果 ReportLab 失敗)'
            )
            
            # 寫回檔案
            with open('main.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ 主程式修改完成")
            return True
        else:
            print("⚠️ 找不到 ReportLab 轉換代碼")
            return False
            
    except Exception as e:
        print(f"❌ 修改主程式失敗: {e}")
        return False


def test_epub_to_pdf_conversion():
    """測試 EPUB → PDF 轉換"""
    print("\n🔄 測試 EPUB → PDF 轉換")
    print("-" * 40)
    
    try:
        from main import EbookConverter, ConversionStatus
        import zipfile
        import time
        
        # 建立測試 EPUB
        epub_path = "test_reportlab_epub.epub"
        
        files = {
            'mimetype': 'application/epub+zip',
            'META-INF/container.xml': '''<?xml version="1.0"?>
<container version="1.0" xmlns="urn:oasis:names:tc:opendocument:xmlns:container">
  <rootfiles>
    <rootfile full-path="content.opf" media-type="application/oebps-package+xml"/>
  </rootfiles>
</container>''',
            'content.opf': '''<?xml version="1.0" encoding="UTF-8"?>
<package xmlns="http://www.idpf.org/2007/opf" unique-identifier="BookId" version="2.0">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <dc:title>ReportLab 轉換測試</dc:title>
    <dc:creator>電子書轉檔工具</dc:creator>
  </metadata>
  <manifest>
    <item id="chapter1" href="chapter1.html" media-type="application/xhtml+xml"/>
  </manifest>
  <spine>
    <itemref idref="chapter1"/>
  </spine>
</package>''',
            'chapter1.html': '''<!DOCTYPE html>
<html>
<head>
    <title>ReportLab 測試</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>ReportLab 自動化 PDF 生成測試</h1>
    <p>這是一個測試 EPUB 檔案，用來驗證 ReportLab 的自動化 PDF 生成功能。</p>
    <h2>功能特色</h2>
    <ul>
        <li>完全自動化</li>
        <li>無需手動步驟</li>
        <li>一次轉檔完成</li>
    </ul>
    <p><strong>ReportLab 測試成功！</strong></p>
</body>
</html>'''
        }
        
        with zipfile.ZipFile(epub_path, 'w', zipfile.ZIP_DEFLATED) as epub_zip:
            for file_path, content in files.items():
                epub_zip.writestr(file_path, content.encode('utf-8'))
        
        print(f"✅ 建立測試 EPUB: {epub_path}")
        
        # 執行轉換
        output_path = "test_reportlab_output.pdf"
        converter = EbookConverter()
        
        result = {"completed": False, "success": False, "message": ""}
        
        def callback(status: ConversionStatus, message: str):
            print(f"  📊 {status.value}: {message}")
            if status in [ConversionStatus.SUCCESS, ConversionStatus.FAILED]:
                result["completed"] = True
                result["success"] = (status == ConversionStatus.SUCCESS)
                result["message"] = message
        
        print("🚀 開始 EPUB → PDF 轉換...")
        converter.convert_ebook_async(epub_path, output_path, callback)
        
        # 等待完成
        timeout = 30
        start_time = time.time()
        
        while not result["completed"] and (time.time() - start_time) < timeout:
            time.sleep(0.1)
        
        # 檢查結果
        success = False
        if result["completed"] and result["success"]:
            if Path(output_path).exists():
                file_size = Path(output_path).stat().st_size
                print(f"🎉 EPUB → PDF 轉換成功！")
                print(f"   檔案大小: {file_size} bytes")
                success = True
            else:
                print("❌ 轉換報告成功但檔案未生成")
        else:
            print(f"❌ 轉換失敗: {result.get('message', '未知錯誤')}")
        
        # 清理測試檔案
        for file in [epub_path, output_path]:
            if Path(file).exists():
                Path(file).unlink()
        
        return success
        
    except Exception as e:
        print(f"❌ 轉換測試失敗: {e}")
        return False


def main():
    """主程式"""
    print("🚀 啟用 ReportLab 完全自動化 PDF 生成")
    print("=" * 60)
    print("目標：實現一次轉檔完成，無需手動步驟")
    print()
    
    # 安裝依賴
    if not install_reportlab_dependencies():
        print("❌ 依賴安裝失敗")
        return
    
    # 測試 ReportLab
    if not test_reportlab_pdf():
        print("❌ ReportLab 測試失敗")
        return
    
    # 修改主程式
    if not modify_main_program():
        print("❌ 主程式修改失敗")
        return
    
    # 測試完整轉換
    if test_epub_to_pdf_conversion():
        print("\n🎉 完美！ReportLab 自動化 PDF 生成已實現")
        print("\n💡 現在您可以:")
        print("1. 啟動程式: python main.py")
        print("2. 選擇 EPUB 檔案")
        print("3. 選擇 PDF 格式")
        print("4. 點擊轉檔 - 一次完成！")
        print("\n🌟 使用 ReportLab 引擎，完全自動化！")
    else:
        print("\n⚠️ 轉換測試失敗")
        print("可能需要重新啟動程式")


if __name__ == '__main__':
    main()
