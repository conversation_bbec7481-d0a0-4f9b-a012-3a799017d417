<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>EPUB 內容提取</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        h1 { color: #333; }
        .epub-info { background: #f5f5f5; padding: 15px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="epub-info">
        <h1>EPUB 內容提取</h1>
        <p><strong>來源:</strong> final_test.epub</p>
    </div>
    <pre>電子書檔案: final_test.epub

=== chapter1.html ===
一次轉檔完成 🎉 一次轉檔完成測試 功能驗證 這個測試驗證以下功能： ✅ EPUB 檔案解析 ✅ 文字內容提取 ✅ ReportLab PDF 生成 ✅ 完全自動化流程 轉換流程 選擇 EPUB 檔案 選擇 PDF 輸出格式 點擊「開始轉檔」 等待完成 - 無需手動步驟！ 技術特色 使用 ReportLab 引擎： 純 Python 實現 無需外部依賴 高品質 PDF 輸出 完全自動化 如果您看到這個 PDF，表示一次轉檔功能已經成功實現！
</pre>
</body>
</html>