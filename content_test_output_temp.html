<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>EPUB 內容提取</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        h1 { color: #333; }
        .epub-info { background: #f5f5f5; padding: 15px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="epub-info">
        <h1>EPUB 內容提取</h1>
        <p><strong>來源:</strong> content_extraction_test.epub</p>
    </div>
    <pre>電子書檔案: content_extraction_test.epub

=== 第 1 章：chapter1.html ===

第一章：開始的故事


1.1 重要小節


1.2 列表內容測試

這是第一章的開頭段落。這段文字應該完整出現在轉檔結果中，不應該有任何缺漏。
第二段包含粗體文字和斜體文字，還有底線文字。這些格式化文字都應該被正確提取。
這個小節包含重要內容：數字測試 123456789，英文測試 ABCDEFGHIJKLMNOPQRSTUVWXYZ，中文測試 一二三四五六七八九十。
第一章結束段落。這裡應該包含大約 300 個字符的完整內容，用於測試提取的完整性。
• 第一個無序列表項目 - 重要內容A
• 第二個無序列表項目 - 重要內容B
• 第三個無序列表項目 - 重要內容C
• 第一個有序列表項目 - 步驟一
• 第二個有序列表項目 - 步驟二
• 第三個有序列表項目 - 步驟三

=== 第 2 章：chapter2.html ===

第二章：發展的過程


2.1 表格數據測試


2.2 特殊字符和符號測試

第二章開始了更複雜的內容測試。這裡有更多的文字來驗證解析的完整性和準確性。
這是一個重要的引用段落。引用的內容應該被完整提取，不能有任何遺漏。這段引用包含了關鍵信息。
特殊符號測試：©®™€£¥§¶†‡•…‰‹›""''–—
數學符號測試：±×÷≠≤≥∞∑∏∫√∂∆∇
中文標點測試：，。！？；：（）【】《》〈〉「」『』
第二章包含表格、引用和特殊字符，總計約 400 個字符的豐富內容。
項目名稱 | 數量 | 備註
重要數據A | 100 | 第一組數據
重要數據B | 200 | 第二組數據
重要數據C | 300 | 第三組數據

=== 第 3 章：chapter3.html ===

第三章：完整的結論


3.1 程式碼和預格式化文字測試


3.2 混合內容綜合測試


3.3 最終統計和驗證

最後一章包含最複雜的內容測試。這裡驗證長段落和複雜結構的處理能力。
這是一個非常長的段落，專門用來測試長文字的處理能力。段落中包含各種標點符號，如逗號、句號、問號、驚嘆號等。同時也包含括號（像這樣的內容），以及各種引號「中文引號」和『中文書名號』。這個段落的目的是確保長文字不會被截斷、分割或遺漏，所有內容都應該完整地出現在最終的轉檔結果中。
混合內容包括：
內容統計資訊：
如果轉檔結果包含所有這些詳細內容，並且順序正確，表示內容提取功能已經完全修正。如果缺少任何部分，則需要進一步調整解析邏輯。
內容完整性測試結束 - 這是電子書的最後一段文字，應該完整出現。
• 繁體中文：電腦軟體網路資訊技術系統
• 簡體中文：电脑软件网络信息技术系统
• 英文內容：Computer Software Network Information Technology System
• 數字內容：1234567890 以及 ①②③④⑤⑥⑦⑧⑨⑩
• 符號內容：!@#$%^&*()_+-=[]{}|;:,.<>?
• 總章節數量：3 章完整內容
• 總段落數量：約 20 個重要段落
• 總字符數量：超過 1000 個字符
• 包含元素：標題、段落、列表、表格、引用、程式碼、特殊字符
• 測試重點：完整性、順序性、格式保留
</pre>
</body>
</html>