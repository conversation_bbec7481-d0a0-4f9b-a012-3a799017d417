pypdf-5.6.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pypdf-5.6.0.dist-info/METADATA,sha256=wAk9iWJyepiNUzjpVg16p6kg-n2xNLwDAbpsp3M_ez0,7166
pypdf-5.6.0.dist-info/RECORD,,
pypdf-5.6.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pypdf-5.6.0.dist-info/WHEEL,sha256=G2gURzTEtmeR8nrdXUJfNiB3VYVxigPQ-bEQujpNiNs,82
pypdf-5.6.0.dist-info/licenses/LICENSE,sha256=qXrCMOXzPvEKU2eoUOsB-R8aCwZONHQsd5TSKUVX9SQ,1605
pypdf/__init__.py,sha256=WYkiisiLw4TrsrobuzUkEFGwAUbPF8V8ei_HJSdEJNY,1302
pypdf/__pycache__/__init__.cpython-311.pyc,,
pypdf/__pycache__/_cmap.cpython-311.pyc,,
pypdf/__pycache__/_doc_common.cpython-311.pyc,,
pypdf/__pycache__/_encryption.cpython-311.pyc,,
pypdf/__pycache__/_merger.cpython-311.pyc,,
pypdf/__pycache__/_page.cpython-311.pyc,,
pypdf/__pycache__/_page_labels.cpython-311.pyc,,
pypdf/__pycache__/_protocols.cpython-311.pyc,,
pypdf/__pycache__/_reader.cpython-311.pyc,,
pypdf/__pycache__/_utils.cpython-311.pyc,,
pypdf/__pycache__/_version.cpython-311.pyc,,
pypdf/__pycache__/_writer.cpython-311.pyc,,
pypdf/__pycache__/_xobj_image_helpers.cpython-311.pyc,,
pypdf/__pycache__/constants.cpython-311.pyc,,
pypdf/__pycache__/errors.cpython-311.pyc,,
pypdf/__pycache__/filters.cpython-311.pyc,,
pypdf/__pycache__/pagerange.cpython-311.pyc,,
pypdf/__pycache__/papersizes.cpython-311.pyc,,
pypdf/__pycache__/types.cpython-311.pyc,,
pypdf/__pycache__/xmp.cpython-311.pyc,,
pypdf/_cmap.py,sha256=ySAnt6Js6pPwGReA7RaD2_hLl5napNvgjq42WclG_AA,18632
pypdf/_codecs/__init__.py,sha256=WXMkzlMCDlmG5U6ixQk8MrYxaQeJxEfig5DTaGlklLk,1676
pypdf/_codecs/__pycache__/__init__.cpython-311.pyc,,
pypdf/_codecs/__pycache__/_codecs.cpython-311.pyc,,
pypdf/_codecs/__pycache__/adobe_glyphs.cpython-311.pyc,,
pypdf/_codecs/__pycache__/pdfdoc.cpython-311.pyc,,
pypdf/_codecs/__pycache__/std.cpython-311.pyc,,
pypdf/_codecs/__pycache__/symbol.cpython-311.pyc,,
pypdf/_codecs/__pycache__/zapfding.cpython-311.pyc,,
pypdf/_codecs/_codecs.py,sha256=NphEEEpJCdj4ZR7B1xtsXKprm1GBVfYUXphnmm1viuI,9942
pypdf/_codecs/adobe_glyphs.py,sha256=t3cDFPDqwIz1w9B0gdVzjdc8eEK9AuRjk5f7laEw_fY,447213
pypdf/_codecs/pdfdoc.py,sha256=xfSvMFYsvxuaSQ0Uu9vZDKaB0Wu85h1uCiB1i9rAcUU,4269
pypdf/_codecs/std.py,sha256=DyQMuEpAGEpS9uy1jWf4cnj-kqShPOAij5sI7Q1YD8E,2630
pypdf/_codecs/symbol.py,sha256=nIaGQIlhWCJiPMHrwUlmGHH-_fOXyEKvguRmuKXcGAk,3734
pypdf/_codecs/zapfding.py,sha256=PQxjxRC616d41xF3exVxP1W8nM4QrZfjO3lmtLxpE_s,3742
pypdf/_crypt_providers/__init__.py,sha256=K3Z6AuXhXVeXgLet-Tukq2gt9H66OgdupsvxIS1CmkI,3054
pypdf/_crypt_providers/__pycache__/__init__.cpython-311.pyc,,
pypdf/_crypt_providers/__pycache__/_base.cpython-311.pyc,,
pypdf/_crypt_providers/__pycache__/_cryptography.cpython-311.pyc,,
pypdf/_crypt_providers/__pycache__/_fallback.cpython-311.pyc,,
pypdf/_crypt_providers/__pycache__/_pycryptodome.cpython-311.pyc,,
pypdf/_crypt_providers/_base.py,sha256=_f53Mj6vivhEZMQ4vNxN5G0IOgFY-n5_leke0c_qiNU,1711
pypdf/_crypt_providers/_cryptography.py,sha256=zT3WmbPzesvgHRkGcKAldqJ24MY3BwZViVbSc55Zxhw,4557
pypdf/_crypt_providers/_fallback.py,sha256=vsYoowR1YCAV_q-HrdIZhkUcrCb6HvRBNMYm03QtCU8,3334
pypdf/_crypt_providers/_pycryptodome.py,sha256=U1aQZ9iYBrZo-hKCjJUhGOPhwEFToiitowQ316TNrrA,3381
pypdf/_doc_common.py,sha256=f2jHZO7ZOdWHuaP7urb4zdQN3ANyMG3l5KyiJgsmFvc,52197
pypdf/_encryption.py,sha256=pPg7fIfqdL96Tc6RVoBytEVjMrmZFecr_6l7dbtDFrE,48775
pypdf/_merger.py,sha256=YfSQKDiiQz2WtCmVZjxP_nv2pR2shiBf2tDiAb41c7s,1744
pypdf/_page.py,sha256=E8MytgWerPEGpHTA7ZKDwv5S-_gtTX2WE8k1m7Q4EK4,101111
pypdf/_page_labels.py,sha256=cAPZXzXQ24LpoahlTNxVtuj8Rr4AczOKc0SlyVVXjmY,8532
pypdf/_protocols.py,sha256=noE1y2fVE-z1wq-FkQzaS5exa8ovOFTUXqdQSvqi57c,2142
pypdf/_reader.py,sha256=PWcWMy7zrMMYm3cIJRKuN1DnoTCG78dZTUhuyix9TYg,51552
pypdf/_text_extraction/__init__.py,sha256=7b2O18fKdcyhcWfziYBsHp-HgznyGJaHGbEcd88q580,8538
pypdf/_text_extraction/__pycache__/__init__.cpython-311.pyc,,
pypdf/_text_extraction/_layout_mode/__init__.py,sha256=k1tN46gDX1zhAatD8oTGMuCJUp-pgbHjyQ8H6axXRgU,338
pypdf/_text_extraction/_layout_mode/__pycache__/__init__.cpython-311.pyc,,
pypdf/_text_extraction/_layout_mode/__pycache__/_fixed_width_page.cpython-311.pyc,,
pypdf/_text_extraction/_layout_mode/__pycache__/_font.cpython-311.pyc,,
pypdf/_text_extraction/_layout_mode/__pycache__/_font_widths.cpython-311.pyc,,
pypdf/_text_extraction/_layout_mode/__pycache__/_text_state_manager.cpython-311.pyc,,
pypdf/_text_extraction/_layout_mode/__pycache__/_text_state_params.cpython-311.pyc,,
pypdf/_text_extraction/_layout_mode/_fixed_width_page.py,sha256=6dzYcLL7iT_mpTksWvaw6ZLmRp0xMwbU5A9p8HpOnQw,14881
pypdf/_text_extraction/_layout_mode/_font.py,sha256=Z334fpftHVERut1c3rjWvB49BdcaA_hFq3ZuWL5wcm0,7047
pypdf/_text_extraction/_layout_mode/_font_widths.py,sha256=Hfgsd2ftGw8Ajl7IcwNIlfLYnum-ekaadfwErcUdWtI,4265
pypdf/_text_extraction/_layout_mode/_text_state_manager.py,sha256=nH548Ri6A2jwIE0pwviWHSfadX9xNl-mfOQ539QARkI,8196
pypdf/_text_extraction/_layout_mode/_text_state_params.py,sha256=b8DSoJ2easCZW_JvMl84WFFIANKGhLD1zjMVAlqScyU,5318
pypdf/_utils.py,sha256=jMCpWO53--KiY1r7aLfPLaX0Ne4Ucmd-pXnxP8b6azA,19278
pypdf/_version.py,sha256=9eGec3AYz2CNznnfEwKYRiJ65G9gDWtXmb587ljDycg,22
pypdf/_writer.py,sha256=prdcor3c6wYo5MV2MWJoqbqLwJDT0aldvLH1urdA7Tw,132688
pypdf/_xobj_image_helpers.py,sha256=G6BfIsJaw7RvAcWUdNY2oTtWKVEUKGiCqnC2f531BaA,13896
pypdf/annotations/__init__.py,sha256=f2k_-jAn39CCB27KxQ_e93GinnzkAHbUnnSeGJl1jyE,990
pypdf/annotations/__pycache__/__init__.cpython-311.pyc,,
pypdf/annotations/__pycache__/_base.cpython-311.pyc,,
pypdf/annotations/__pycache__/_markup_annotations.cpython-311.pyc,,
pypdf/annotations/__pycache__/_non_markup_annotations.cpython-311.pyc,,
pypdf/annotations/_base.py,sha256=7rQJyOMPtKkd_Yp2CXGT6KN17W3WOj8Albx6ehMki3w,916
pypdf/annotations/_markup_annotations.py,sha256=akoAcQUA945XGgjXRnX6hX1xczTUeqrvciK7KiNArt4,10100
pypdf/annotations/_non_markup_annotations.py,sha256=qX51TJMTRUyWz1ogIK-cXXGK7k5oKhgYQhemA_sVxGE,3622
pypdf/constants.py,sha256=Dqx5GJ6uqHxZCFqugD1AOvRv2MVUJLrCXmoQLopbz5U,21039
pypdf/errors.py,sha256=x0J5mTIbp5YcXA1pdYa5DO83uAhXP5NCO0Ankf4DsUY,1740
pypdf/filters.py,sha256=fhw3EdohDKjS-HUaMw4j4SexusGX3TNrnUxFlXSwraQ,32186
pypdf/generic/__init__.py,sha256=nnLmD7bnhSJu1qZ774pj0eE7lmeRuYDEUcpa52-Mk5A,7168
pypdf/generic/__pycache__/__init__.cpython-311.pyc,,
pypdf/generic/__pycache__/_base.cpython-311.pyc,,
pypdf/generic/__pycache__/_data_structures.cpython-311.pyc,,
pypdf/generic/__pycache__/_files.cpython-311.pyc,,
pypdf/generic/__pycache__/_fit.cpython-311.pyc,,
pypdf/generic/__pycache__/_image_inline.cpython-311.pyc,,
pypdf/generic/__pycache__/_outline.cpython-311.pyc,,
pypdf/generic/__pycache__/_rectangle.cpython-311.pyc,,
pypdf/generic/__pycache__/_utils.cpython-311.pyc,,
pypdf/generic/__pycache__/_viewerpref.cpython-311.pyc,,
pypdf/generic/_base.py,sha256=9zpmwvZg2ySbAVRw8q7EjKz2_hnRbJFZKn_VOpQ2aTI,31053
pypdf/generic/_data_structures.py,sha256=oUDZRaxlWceBpgAWQrM_Uxzidi6u1eoFvzbfFMzb-Fc,63625
pypdf/generic/_files.py,sha256=UcyL_mCDpVh_dRuxxH8bENWA76rYt5eFw0emFcOE79Y,5655
pypdf/generic/_fit.py,sha256=lLkLgW0AQ36sVG4py-HXV__EPQYkLA1bNLoCwGJ_ijI,5511
pypdf/generic/_image_inline.py,sha256=B9jqOt_navgmjekp7BqNpu72SMccdHjNCo4_Jtez15w,11478
pypdf/generic/_outline.py,sha256=qKbMX42OWfqnopIiE6BUy6EvdTLGe3ZtjaiWN85JpaY,1094
pypdf/generic/_rectangle.py,sha256=5KJRbQESqdzrYvJOFcwfp0_v_bhCDVj9r4yMyGXSGyc,3808
pypdf/generic/_utils.py,sha256=p_5ZERuQggQGNrUVYHf3OxNn_tkIb0tYOhBo095_N5I,7277
pypdf/generic/_viewerpref.py,sha256=8grg_CQQPsV5BHnxjwTYyeIWAsu0v5KDWgRFEIDe_VQ,6768
pypdf/pagerange.py,sha256=rJQB1NGTDDt30DoSYiu4cy4ugXJtHe4K69Boz1MMzdM,6994
pypdf/papersizes.py,sha256=6Tz5sfNN_3JOUapY83U-lakohnpXYA0hSEQNmOVLFL8,1413
pypdf/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pypdf/types.py,sha256=6B6pMncEhcqFfq-iKs5IBPg6guWXffU6YHpeYzCJH-s,1963
pypdf/xmp.py,sha256=Yb2CSHnnpermUfsB2oysL3QK9Hf78-1adkzXyNsG_0c,14329
