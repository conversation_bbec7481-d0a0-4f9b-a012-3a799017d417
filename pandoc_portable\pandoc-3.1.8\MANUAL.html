<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <meta name="author" content="<PERSON>" />
  <meta name="dcterms.date" content="2023-09-08" />
  <title>Pandoc User’s Guide</title>
  <style>
    html {
      color: #1a1a1a;
      background-color: #fdfdfd;
    }
    body {
      margin: 0 auto;
      max-width: 36em;
      padding-left: 50px;
      padding-right: 50px;
      padding-top: 50px;
      padding-bottom: 50px;
      hyphens: auto;
      overflow-wrap: break-word;
      text-rendering: optimizeLegibility;
      font-kerning: normal;
    }
    @media (max-width: 600px) {
      body {
        font-size: 0.9em;
        padding: 12px;
      }
      h1 {
        font-size: 1.8em;
      }
    }
    @media print {
      html {
        background-color: white;
      }
      body {
        background-color: transparent;
        color: black;
        font-size: 12pt;
      }
      p, h2, h3 {
        orphans: 3;
        widows: 3;
      }
      h2, h3, h4 {
        page-break-after: avoid;
      }
    }
    p {
      margin: 1em 0;
    }
    a {
      color: #1a1a1a;
    }
    a:visited {
      color: #1a1a1a;
    }
    img {
      max-width: 100%;
    }
    svg {
      height: auto;
      max-width: 100%;
    }
    h1, h2, h3, h4, h5, h6 {
      margin-top: 1.4em;
    }
    h5, h6 {
      font-size: 1em;
      font-style: italic;
    }
    h6 {
      font-weight: normal;
    }
    ol, ul {
      padding-left: 1.7em;
      margin-top: 1em;
    }
    li > ol, li > ul {
      margin-top: 0;
    }
    blockquote {
      margin: 1em 0 1em 1.7em;
      padding-left: 1em;
      border-left: 2px solid #e6e6e6;
      color: #606060;
    }
    code {
      font-family: Menlo, Monaco, Consolas, 'Lucida Console', monospace;
      font-size: 85%;
      margin: 0;
      hyphens: manual;
    }
    pre {
      margin: 1em 0;
      overflow: auto;
    }
    pre code {
      padding: 0;
      overflow: visible;
      overflow-wrap: normal;
    }
    .sourceCode {
     background-color: transparent;
     overflow: visible;
    }
    hr {
      background-color: #1a1a1a;
      border: none;
      height: 1px;
      margin: 1em 0;
    }
    table {
      margin: 1em 0;
      border-collapse: collapse;
      width: 100%;
      overflow-x: auto;
      display: block;
      font-variant-numeric: lining-nums tabular-nums;
    }
    table caption {
      margin-bottom: 0.75em;
    }
    tbody {
      margin-top: 0.5em;
      border-top: 1px solid #1a1a1a;
      border-bottom: 1px solid #1a1a1a;
    }
    th {
      border-top: 1px solid #1a1a1a;
      padding: 0.25em 0.5em 0.25em 0.5em;
    }
    td {
      padding: 0.125em 0.5em 0.25em 0.5em;
    }
    header {
      margin-bottom: 4em;
      text-align: center;
    }
    #TOC li {
      list-style: none;
    }
    #TOC ul {
      padding-left: 1.3em;
    }
    #TOC > ul {
      padding-left: 0;
    }
    #TOC a:not(:hover) {
      text-decoration: none;
    }
    code{white-space: pre-wrap;}
    span.smallcaps{font-variant: small-caps;}
    div.columns{display: flex; gap: min(4vw, 1.5em);}
    div.column{flex: auto; overflow-x: auto;}
    div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
    /* The extra [class] is a hack that increases specificity enough to
       override a similar rule in reveal.js */
    ul.task-list[class]{list-style: none;}
    ul.task-list li input[type="checkbox"] {
      font-size: inherit;
      width: 0.8em;
      margin: 0 0.8em 0.2em -1.6em;
      vertical-align: middle;
    }
    .display.math{display: block; text-align: center; margin: 0.5rem auto;}
    /* CSS for syntax highlighting */
    pre > code.sourceCode { white-space: pre; position: relative; }
    pre > code.sourceCode > span { line-height: 1.25; }
    pre > code.sourceCode > span:empty { height: 1.2em; }
    .sourceCode { overflow: visible; }
    code.sourceCode > span { color: inherit; text-decoration: inherit; }
    div.sourceCode { margin: 1em 0; }
    pre.sourceCode { margin: 0; }
    @media screen {
    div.sourceCode { overflow: auto; }
    }
    @media print {
    pre > code.sourceCode { white-space: pre-wrap; }
    pre > code.sourceCode > span { text-indent: -5em; padding-left: 5em; }
    }
    pre.numberSource code
      { counter-reset: source-line 0; }
    pre.numberSource code > span
      { position: relative; left: -4em; counter-increment: source-line; }
    pre.numberSource code > span > a:first-child::before
      { content: counter(source-line);
        position: relative; left: -1em; text-align: right; vertical-align: baseline;
        border: none; display: inline-block;
        -webkit-touch-callout: none; -webkit-user-select: none;
        -khtml-user-select: none; -moz-user-select: none;
        -ms-user-select: none; user-select: none;
        padding: 0 4px; width: 4em;
        color: #aaaaaa;
      }
    pre.numberSource { margin-left: 3em; border-left: 1px solid #aaaaaa;  padding-left: 4px; }
    div.sourceCode
      {   }
    @media screen {
    pre > code.sourceCode > span > a:first-child::before { text-decoration: underline; }
    }
    code span.al { color: #ff0000; font-weight: bold; } /* Alert */
    code span.an { color: #60a0b0; font-weight: bold; font-style: italic; } /* Annotation */
    code span.at { color: #7d9029; } /* Attribute */
    code span.bn { color: #40a070; } /* BaseN */
    code span.bu { color: #008000; } /* BuiltIn */
    code span.cf { color: #007020; font-weight: bold; } /* ControlFlow */
    code span.ch { color: #4070a0; } /* Char */
    code span.cn { color: #880000; } /* Constant */
    code span.co { color: #60a0b0; font-style: italic; } /* Comment */
    code span.cv { color: #60a0b0; font-weight: bold; font-style: italic; } /* CommentVar */
    code span.do { color: #ba2121; font-style: italic; } /* Documentation */
    code span.dt { color: #902000; } /* DataType */
    code span.dv { color: #40a070; } /* DecVal */
    code span.er { color: #ff0000; font-weight: bold; } /* Error */
    code span.ex { } /* Extension */
    code span.fl { color: #40a070; } /* Float */
    code span.fu { color: #06287e; } /* Function */
    code span.im { color: #008000; font-weight: bold; } /* Import */
    code span.in { color: #60a0b0; font-weight: bold; font-style: italic; } /* Information */
    code span.kw { color: #007020; font-weight: bold; } /* Keyword */
    code span.op { color: #666666; } /* Operator */
    code span.ot { color: #007020; } /* Other */
    code span.pp { color: #bc7a00; } /* Preprocessor */
    code span.sc { color: #4070a0; } /* SpecialChar */
    code span.ss { color: #bb6688; } /* SpecialString */
    code span.st { color: #4070a0; } /* String */
    code span.va { color: #19177c; } /* Variable */
    code span.vs { color: #4070a0; } /* VerbatimString */
    code span.wa { color: #60a0b0; font-weight: bold; font-style: italic; } /* Warning */
  </style>
  <!--[if lt IE 9]>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5shiv/3.7.3/html5shiv-printshiv.min.js"></script>
  <![endif]-->
</head>
<body>
<header id="title-block-header">
<h1 class="title">Pandoc User’s Guide</h1>
<p class="author">John MacFarlane</p>
<p class="date">September 08, 2023</p>
</header>
<nav id="TOC" role="doc-toc">
<ul>
<li><a href="#synopsis" id="toc-synopsis">Synopsis</a></li>
<li><a href="#description" id="toc-description">Description</a>
<ul>
<li><a href="#using-pandoc" id="toc-using-pandoc">Using pandoc</a></li>
<li><a href="#specifying-formats" id="toc-specifying-formats">Specifying
formats</a></li>
<li><a href="#character-encoding" id="toc-character-encoding">Character
encoding</a></li>
<li><a href="#creating-a-pdf" id="toc-creating-a-pdf">Creating a
PDF</a></li>
<li><a href="#reading-from-the-web"
id="toc-reading-from-the-web">Reading from the Web</a></li>
</ul></li>
<li><a href="#options" id="toc-options">Options</a>
<ul>
<li><a href="#general-options" id="toc-general-options">General
options</a></li>
<li><a href="#reader-options" id="toc-reader-options">Reader
options</a></li>
<li><a href="#general-writer-options"
id="toc-general-writer-options">General writer options</a></li>
<li><a href="#options-affecting-specific-writers"
id="toc-options-affecting-specific-writers">Options affecting specific
writers</a></li>
<li><a href="#citation-rendering" id="toc-citation-rendering">Citation
rendering</a></li>
<li><a href="#math-rendering-in-html"
id="toc-math-rendering-in-html">Math rendering in HTML</a></li>
<li><a href="#options-for-wrapper-scripts"
id="toc-options-for-wrapper-scripts">Options for wrapper
scripts</a></li>
</ul></li>
<li><a href="#exit-codes" id="toc-exit-codes">Exit codes</a></li>
<li><a href="#defaults-files" id="toc-defaults-files">Defaults files</a>
<ul>
<li><a href="#general-options-1" id="toc-general-options-1">General
options</a></li>
<li><a href="#reader-options-1" id="toc-reader-options-1">Reader
options</a></li>
<li><a href="#general-writer-options-1"
id="toc-general-writer-options-1">General writer options</a></li>
<li><a href="#options-affecting-specific-writers-1"
id="toc-options-affecting-specific-writers-1">Options affecting specific
writers</a></li>
<li><a href="#citation-rendering-1"
id="toc-citation-rendering-1">Citation rendering</a></li>
<li><a href="#math-rendering-in-html-1"
id="toc-math-rendering-in-html-1">Math rendering in HTML</a></li>
<li><a href="#options-for-wrapper-scripts-1"
id="toc-options-for-wrapper-scripts-1">Options for wrapper
scripts</a></li>
</ul></li>
<li><a href="#templates" id="toc-templates">Templates</a>
<ul>
<li><a href="#template-syntax" id="toc-template-syntax">Template
syntax</a>
<ul>
<li><a href="#comments" id="toc-comments">Comments</a></li>
<li><a href="#delimiters" id="toc-delimiters">Delimiters</a></li>
<li><a href="#interpolated-variables"
id="toc-interpolated-variables">Interpolated variables</a></li>
<li><a href="#conditionals" id="toc-conditionals">Conditionals</a></li>
<li><a href="#for-loops" id="toc-for-loops">For loops</a></li>
<li><a href="#partials" id="toc-partials">Partials</a></li>
<li><a href="#nesting" id="toc-nesting">Nesting</a></li>
<li><a href="#breakable-spaces" id="toc-breakable-spaces">Breakable
spaces</a></li>
<li><a href="#pipes" id="toc-pipes">Pipes</a></li>
</ul></li>
<li><a href="#variables" id="toc-variables">Variables</a>
<ul>
<li><a href="#metadata-variables" id="toc-metadata-variables">Metadata
variables</a></li>
<li><a href="#language-variables" id="toc-language-variables">Language
variables</a></li>
<li><a href="#variables-for-html" id="toc-variables-for-html">Variables
for HTML</a></li>
<li><a href="#variables-for-html-math"
id="toc-variables-for-html-math">Variables for HTML math</a></li>
<li><a href="#variables-for-html-slides"
id="toc-variables-for-html-slides">Variables for HTML slides</a></li>
<li><a href="#variables-for-beamer-slides"
id="toc-variables-for-beamer-slides">Variables for Beamer
slides</a></li>
<li><a href="#variables-for-powerpoint"
id="toc-variables-for-powerpoint">Variables for PowerPoint</a></li>
<li><a href="#variables-for-latex"
id="toc-variables-for-latex">Variables for LaTeX</a></li>
<li><a href="#variables-for-context"
id="toc-variables-for-context">Variables for ConTeXt</a></li>
<li><a href="#variables-for-wkhtmltopdf"
id="toc-variables-for-wkhtmltopdf">Variables for
<code>wkhtmltopdf</code></a></li>
<li><a href="#variables-for-man-pages"
id="toc-variables-for-man-pages">Variables for man pages</a></li>
<li><a href="#variables-for-typst"
id="toc-variables-for-typst">Variables for Typst</a></li>
<li><a href="#variables-for-ms" id="toc-variables-for-ms">Variables for
ms</a></li>
<li><a href="#variables-set-automatically"
id="toc-variables-set-automatically">Variables set
automatically</a></li>
</ul></li>
</ul></li>
<li><a href="#extensions" id="toc-extensions">Extensions</a>
<ul>
<li><a href="#typography" id="toc-typography">Typography</a></li>
<li><a href="#headings-and-sections"
id="toc-headings-and-sections">Headings and sections</a></li>
<li><a href="#math-input" id="toc-math-input">Math Input</a></li>
<li><a href="#raw-htmltex" id="toc-raw-htmltex">Raw HTML/TeX</a></li>
<li><a href="#literate-haskell-support"
id="toc-literate-haskell-support">Literate Haskell support</a></li>
<li><a href="#other-extensions" id="toc-other-extensions">Other
extensions</a></li>
</ul></li>
<li><a href="#pandocs-markdown" id="toc-pandocs-markdown">Pandoc’s
Markdown</a>
<ul>
<li><a href="#philosophy" id="toc-philosophy">Philosophy</a></li>
<li><a href="#paragraphs" id="toc-paragraphs">Paragraphs</a></li>
<li><a href="#headings" id="toc-headings">Headings</a>
<ul>
<li><a href="#setext-style-headings"
id="toc-setext-style-headings">Setext-style headings</a></li>
<li><a href="#atx-style-headings" id="toc-atx-style-headings">ATX-style
headings</a></li>
<li><a href="#heading-identifiers" id="toc-heading-identifiers">Heading
identifiers</a></li>
</ul></li>
<li><a href="#block-quotations" id="toc-block-quotations">Block
quotations</a></li>
<li><a href="#verbatim-code-blocks"
id="toc-verbatim-code-blocks">Verbatim (code) blocks</a>
<ul>
<li><a href="#indented-code-blocks"
id="toc-indented-code-blocks">Indented code blocks</a></li>
<li><a href="#fenced-code-blocks" id="toc-fenced-code-blocks">Fenced
code blocks</a></li>
</ul></li>
<li><a href="#line-blocks" id="toc-line-blocks">Line blocks</a></li>
<li><a href="#lists" id="toc-lists">Lists</a>
<ul>
<li><a href="#bullet-lists" id="toc-bullet-lists">Bullet lists</a></li>
<li><a href="#block-content-in-list-items"
id="toc-block-content-in-list-items">Block content in list
items</a></li>
<li><a href="#ordered-lists" id="toc-ordered-lists">Ordered
lists</a></li>
<li><a href="#definition-lists" id="toc-definition-lists">Definition
lists</a></li>
<li><a href="#numbered-example-lists"
id="toc-numbered-example-lists">Numbered example lists</a></li>
<li><a href="#ending-a-list" id="toc-ending-a-list">Ending a
list</a></li>
</ul></li>
<li><a href="#horizontal-rules" id="toc-horizontal-rules">Horizontal
rules</a></li>
<li><a href="#tables" id="toc-tables">Tables</a></li>
<li><a href="#metadata-blocks" id="toc-metadata-blocks">Metadata
blocks</a></li>
<li><a href="#backslash-escapes" id="toc-backslash-escapes">Backslash
escapes</a></li>
<li><a href="#inline-formatting" id="toc-inline-formatting">Inline
formatting</a>
<ul>
<li><a href="#emphasis" id="toc-emphasis">Emphasis</a></li>
<li><a href="#strikeout" id="toc-strikeout">Strikeout</a></li>
<li><a href="#superscripts-and-subscripts"
id="toc-superscripts-and-subscripts">Superscripts and
subscripts</a></li>
<li><a href="#verbatim" id="toc-verbatim">Verbatim</a></li>
<li><a href="#underline" id="toc-underline">Underline</a></li>
<li><a href="#small-caps" id="toc-small-caps">Small caps</a></li>
<li><a href="#highlighting" id="toc-highlighting">Highlighting</a></li>
</ul></li>
<li><a href="#math" id="toc-math">Math</a></li>
<li><a href="#raw-html" id="toc-raw-html">Raw HTML</a>
<ul>
<li><a href="#generic-raw-attribute"
id="toc-generic-raw-attribute">Generic raw attribute</a></li>
</ul></li>
<li><a href="#latex-macros" id="toc-latex-macros">LaTeX macros</a></li>
<li><a href="#links-1" id="toc-links-1">Links</a>
<ul>
<li><a href="#automatic-links" id="toc-automatic-links">Automatic
links</a></li>
<li><a href="#inline-links" id="toc-inline-links">Inline links</a></li>
<li><a href="#reference-links" id="toc-reference-links">Reference
links</a></li>
<li><a href="#internal-links" id="toc-internal-links">Internal
links</a></li>
</ul></li>
<li><a href="#images" id="toc-images">Images</a></li>
<li><a href="#divs-and-spans" id="toc-divs-and-spans">Divs and
Spans</a></li>
<li><a href="#footnotes" id="toc-footnotes">Footnotes</a></li>
<li><a href="#citation-syntax" id="toc-citation-syntax">Citation
syntax</a></li>
<li><a href="#non-default-extensions"
id="toc-non-default-extensions">Non-default extensions</a></li>
<li><a href="#markdown-variants" id="toc-markdown-variants">Markdown
variants</a></li>
</ul></li>
<li><a href="#citations" id="toc-citations">Citations</a>
<ul>
<li><a href="#specifying-bibliographic-data"
id="toc-specifying-bibliographic-data">Specifying bibliographic data</a>
<ul>
<li><a href="#capitalization-in-titles"
id="toc-capitalization-in-titles">Capitalization in titles</a></li>
<li><a href="#conference-papers-published-vs.-unpublished"
id="toc-conference-papers-published-vs.-unpublished">Conference Papers,
Published vs. Unpublished</a></li>
</ul></li>
<li><a href="#specifying-a-citation-style"
id="toc-specifying-a-citation-style">Specifying a citation
style</a></li>
<li><a href="#citations-in-note-styles"
id="toc-citations-in-note-styles">Citations in note styles</a></li>
<li><a href="#placement-of-the-bibliography"
id="toc-placement-of-the-bibliography">Placement of the
bibliography</a></li>
<li><a href="#including-uncited-items-in-the-bibliography"
id="toc-including-uncited-items-in-the-bibliography">Including uncited
items in the bibliography</a></li>
<li><a href="#other-relevant-metadata-fields"
id="toc-other-relevant-metadata-fields">Other relevant metadata
fields</a></li>
</ul></li>
<li><a href="#slide-shows" id="toc-slide-shows">Slide shows</a>
<ul>
<li><a href="#structuring-the-slide-show"
id="toc-structuring-the-slide-show">Structuring the slide show</a>
<ul>
<li><a href="#powerpoint-layout-choice"
id="toc-powerpoint-layout-choice">PowerPoint layout choice</a></li>
</ul></li>
<li><a href="#incremental-lists" id="toc-incremental-lists">Incremental
lists</a></li>
<li><a href="#inserting-pauses" id="toc-inserting-pauses">Inserting
pauses</a></li>
<li><a href="#styling-the-slides" id="toc-styling-the-slides">Styling
the slides</a></li>
<li><a href="#speaker-notes" id="toc-speaker-notes">Speaker
notes</a></li>
<li><a href="#columns" id="toc-columns">Columns</a>
<ul>
<li><a href="#additional-columns-attributes-in-beamer"
id="toc-additional-columns-attributes-in-beamer">Additional columns
attributes in beamer</a></li>
</ul></li>
<li><a href="#frame-attributes-in-beamer"
id="toc-frame-attributes-in-beamer">Frame attributes in beamer</a></li>
<li><a href="#background-in-reveal.js-beamer-and-pptx"
id="toc-background-in-reveal.js-beamer-and-pptx">Background in
reveal.js, beamer, and pptx</a>
<ul>
<li><a href="#on-all-slides-beamer-reveal.js-pptx"
id="toc-on-all-slides-beamer-reveal.js-pptx">On all slides (beamer,
reveal.js, pptx)</a></li>
<li><a href="#on-individual-slides-reveal.js-pptx"
id="toc-on-individual-slides-reveal.js-pptx">On individual slides
(reveal.js, pptx)</a></li>
<li><a href="#on-the-title-slide-reveal.js-pptx"
id="toc-on-the-title-slide-reveal.js-pptx">On the title slide
(reveal.js, pptx)</a></li>
<li><a href="#example-reveal.js" id="toc-example-reveal.js">Example
(reveal.js)</a></li>
</ul></li>
</ul></li>
<li><a href="#epubs" id="toc-epubs">EPUBs</a>
<ul>
<li><a href="#epub-metadata" id="toc-epub-metadata">EPUB
Metadata</a></li>
<li><a href="#the-epubtype-attribute"
id="toc-the-epubtype-attribute">The <code>epub:type</code>
attribute</a></li>
<li><a href="#linked-media" id="toc-linked-media">Linked media</a></li>
<li><a href="#epub-styling" id="toc-epub-styling">EPUB styling</a></li>
</ul></li>
<li><a href="#chunked-html" id="toc-chunked-html">Chunked HTML</a></li>
<li><a href="#jupyter-notebooks" id="toc-jupyter-notebooks">Jupyter
notebooks</a></li>
<li><a href="#syntax-highlighting" id="toc-syntax-highlighting">Syntax
highlighting</a></li>
<li><a href="#custom-styles" id="toc-custom-styles">Custom Styles</a>
<ul>
<li><a href="#output" id="toc-output">Output</a></li>
<li><a href="#input" id="toc-input">Input</a></li>
</ul></li>
<li><a href="#custom-readers-and-writers"
id="toc-custom-readers-and-writers">Custom readers and writers</a></li>
<li><a href="#reproducible-builds"
id="toc-reproducible-builds">Reproducible builds</a></li>
<li><a href="#accessible-pdfs-and-pdf-archiving-standards"
id="toc-accessible-pdfs-and-pdf-archiving-standards">Accessible PDFs and
PDF archiving standards</a>
<ul>
<li><a href="#context" id="toc-context">ConTeXt</a></li>
<li><a href="#weasyprint" id="toc-weasyprint">WeasyPrint</a></li>
<li><a href="#prince-xml" id="toc-prince-xml">Prince XML</a></li>
<li><a href="#word-processors" id="toc-word-processors">Word
Processors</a></li>
</ul></li>
<li><a href="#running-pandoc-as-a-web-server"
id="toc-running-pandoc-as-a-web-server">Running pandoc as a web
server</a></li>
<li><a href="#running-pandoc-as-a-lua-interpreter"
id="toc-running-pandoc-as-a-lua-interpreter">Running pandoc as a Lua
interpreter</a></li>
<li><a href="#a-note-on-security" id="toc-a-note-on-security">A note on
security</a></li>
<li><a href="#authors" id="toc-authors">Authors</a></li>
</ul>
</nav>
<h1 id="synopsis">Synopsis</h1>
<p><code>pandoc</code> [<em>options</em>] [<em>input-file</em>]…</p>
<h1 id="description">Description</h1>
<p>Pandoc is a <a href="https://www.haskell.org">Haskell</a> library for
converting from one markup format to another, and a command-line tool
that uses this library.</p>
<p>Pandoc can convert between numerous markup and word processing
formats, including, but not limited to, various flavors of <a
href="https://daringfireball.net/projects/markdown/">Markdown</a>, <a
href="https://www.w3.org/html/">HTML</a>, <a
href="https://www.latex-project.org/">LaTeX</a> and <a
href="https://en.wikipedia.org/wiki/Office_Open_XML">Word docx</a>. For
the full lists of input and output formats, see the <code>--from</code>
and <code>--to</code> <a href="#general-options">options below</a>.
Pandoc can also produce <a href="https://www.adobe.com/pdf/">PDF</a>
output: see <a href="#creating-a-pdf">creating a PDF</a>, below.</p>
<p>Pandoc’s enhanced version of Markdown includes syntax for <a
href="#tables">tables</a>, <a href="#definition-lists">definition
lists</a>, <a href="#metadata-blocks">metadata blocks</a>, <a
href="#footnotes">footnotes</a>, <a href="#citations">citations</a>, <a
href="#math">math</a>, and much more. See below under <a
href="#pandocs-markdown">Pandoc’s Markdown</a>.</p>
<p>Pandoc has a modular design: it consists of a set of readers, which
parse text in a given format and produce a native representation of the
document (an <em>abstract syntax tree</em> or AST), and a set of
writers, which convert this native representation into a target format.
Thus, adding an input or output format requires only adding a reader or
writer. Users can also run custom <a
href="https://pandoc.org/filters.html">pandoc filters</a> to modify the
intermediate AST.</p>
<p>Because pandoc’s intermediate representation of a document is less
expressive than many of the formats it converts between, one should not
expect perfect conversions between every format and every other. Pandoc
attempts to preserve the structural elements of a document, but not
formatting details such as margin size. And some document elements, such
as complex tables, may not fit into pandoc’s simple document model.
While conversions from pandoc’s Markdown to all formats aspire to be
perfect, conversions from formats more expressive than pandoc’s Markdown
can be expected to be lossy.</p>
<h2 id="using-pandoc">Using pandoc</h2>
<p>If no <em>input-files</em> are specified, input is read from
<em>stdin</em>. Output goes to <em>stdout</em> by default. For output to
a file, use the <code>-o</code> option:</p>
<pre><code>pandoc -o output.html input.txt</code></pre>
<p>By default, pandoc produces a document fragment. To produce a
standalone document (e.g. a valid HTML file including
<code>&lt;head&gt;</code> and <code>&lt;body&gt;</code>), use the
<code>-s</code> or <code>--standalone</code> flag:</p>
<pre><code>pandoc -s -o output.html input.txt</code></pre>
<p>For more information on how standalone documents are produced, see <a
href="#templates">Templates</a> below.</p>
<p>If multiple input files are given, pandoc will concatenate them all
(with blank lines between them) before parsing. (Use
<code>--file-scope</code> to parse files individually.)</p>
<h2 id="specifying-formats">Specifying formats</h2>
<p>The format of the input and output can be specified explicitly using
command-line options. The input format can be specified using the
<code>-f/--from</code> option, the output format using the
<code>-t/--to</code> option. Thus, to convert <code>hello.txt</code>
from Markdown to LaTeX, you could type:</p>
<pre><code>pandoc -f markdown -t latex hello.txt</code></pre>
<p>To convert <code>hello.html</code> from HTML to Markdown:</p>
<pre><code>pandoc -f html -t markdown hello.html</code></pre>
<p>Supported input and output formats are listed below under <a
href="#options">Options</a> (see <code>-f</code> for input formats and
<code>-t</code> for output formats). You can also use
<code>pandoc --list-input-formats</code> and
<code>pandoc --list-output-formats</code> to print lists of supported
formats.</p>
<p>If the input or output format is not specified explicitly, pandoc
will attempt to guess it from the extensions of the filenames. Thus, for
example,</p>
<pre><code>pandoc -o hello.tex hello.txt</code></pre>
<p>will convert <code>hello.txt</code> from Markdown to LaTeX. If no
output file is specified (so that output goes to <em>stdout</em>), or if
the output file’s extension is unknown, the output format will default
to HTML. If no input file is specified (so that input comes from
<em>stdin</em>), or if the input files’ extensions are unknown, the
input format will be assumed to be Markdown.</p>
<h2 id="character-encoding">Character encoding</h2>
<p>Pandoc uses the UTF-8 character encoding for both input and output.
If your local character encoding is not UTF-8, you should pipe input and
output through <a
href="https://www.gnu.org/software/libiconv/"><code>iconv</code></a>:</p>
<pre><code>iconv -t utf-8 input.txt | pandoc | iconv -f utf-8</code></pre>
<p>Note that in some output formats (such as HTML, LaTeX, ConTeXt, RTF,
OPML, DocBook, and Texinfo), information about the character encoding is
included in the document header, which will only be included if you use
the <code>-s/--standalone</code> option.</p>
<h2 id="creating-a-pdf">Creating a PDF</h2>
<p>To produce a PDF, specify an output file with a <code>.pdf</code>
extension:</p>
<pre><code>pandoc test.txt -o test.pdf</code></pre>
<p>By default, pandoc will use LaTeX to create the PDF, which requires
that a LaTeX engine be installed (see <code>--pdf-engine</code> below).
Alternatively, pandoc can use ConTeXt, roff ms, or HTML as an
intermediate format. To do this, specify an output file with a
<code>.pdf</code> extension, as before, but add the
<code>--pdf-engine</code> option or <code>-t context</code>,
<code>-t html</code>, or <code>-t ms</code> to the command line. The
tool used to generate the PDF from the intermediate format may be
specified using <code>--pdf-engine</code>.</p>
<p>You can control the PDF style using variables, depending on the
intermediate format used: see <a href="#variables-for-latex">variables
for LaTeX</a>, <a href="#variables-for-context">variables for
ConTeXt</a>, <a href="#variables-for-wkhtmltopdf">variables for
<code>wkhtmltopdf</code></a>, <a href="#variables-for-ms">variables for
ms</a>. When HTML is used as an intermediate format, the output can be
styled using <code>--css</code>.</p>
<p>To debug the PDF creation, it can be useful to look at the
intermediate representation: instead of <code>-o test.pdf</code>, use
for example <code>-s -o test.tex</code> to output the generated LaTeX.
You can then test it with <code>pdflatex test.tex</code>.</p>
<p>When using LaTeX, the following packages need to be available (they
are included with all recent versions of <a
href="https://www.tug.org/texlive/">TeX Live</a>): <a
href="https://ctan.org/pkg/amsfonts"><code>amsfonts</code></a>, <a
href="https://ctan.org/pkg/amsmath"><code>amsmath</code></a>, <a
href="https://ctan.org/pkg/lm"><code>lm</code></a>, <a
href="https://ctan.org/pkg/unicode-math"><code>unicode-math</code></a>,
<a href="https://ctan.org/pkg/iftex"><code>iftex</code></a>, <a
href="https://ctan.org/pkg/listings"><code>listings</code></a> (if the
<code>--listings</code> option is used), <a
href="https://ctan.org/pkg/fancyvrb"><code>fancyvrb</code></a>, <a
href="https://ctan.org/pkg/longtable"><code>longtable</code></a>, <a
href="https://ctan.org/pkg/booktabs"><code>booktabs</code></a>, <a
href="https://ctan.org/pkg/graphicx"><code>graphicx</code></a> (if the
document contains images), <a
href="https://ctan.org/pkg/hyperref"><code>hyperref</code></a>, <a
href="https://ctan.org/pkg/xcolor"><code>xcolor</code></a>, <a
href="https://ctan.org/pkg/soul"><code>soul</code></a>, <a
href="https://ctan.org/pkg/geometry"><code>geometry</code></a> (with the
<code>geometry</code> variable set), <a
href="https://ctan.org/pkg/setspace"><code>setspace</code></a> (with
<code>linestretch</code>), and <a
href="https://ctan.org/pkg/babel"><code>babel</code></a> (with
<code>lang</code>). If <code>CJKmainfont</code> is set, <a
href="https://ctan.org/pkg/xecjk"><code>xeCJK</code></a> is needed. The
use of <code>xelatex</code> or <code>lualatex</code> as the PDF engine
requires <a
href="https://ctan.org/pkg/fontspec"><code>fontspec</code></a>.
<code>lualatex</code> uses <a
href="https://ctan.org/pkg/selnolig"><code>selnolig</code></a>.
<code>xelatex</code> uses <a
href="https://ctan.org/pkg/bidi"><code>bidi</code></a> (with the
<code>dir</code> variable set). If the <code>mathspec</code> variable is
set, <code>xelatex</code> will use <a
href="https://ctan.org/pkg/mathspec"><code>mathspec</code></a> instead
of <a
href="https://ctan.org/pkg/unicode-math"><code>unicode-math</code></a>.
The <a href="https://ctan.org/pkg/upquote"><code>upquote</code></a> and
<a href="https://ctan.org/pkg/microtype"><code>microtype</code></a>
packages are used if available, and <a
href="https://ctan.org/pkg/csquotes"><code>csquotes</code></a> will be
used for <a href="#typography">typography</a> if the
<code>csquotes</code> variable or metadata field is set to a true value.
The <a href="https://ctan.org/pkg/natbib"><code>natbib</code></a>, <a
href="https://ctan.org/pkg/biblatex"><code>biblatex</code></a>, <a
href="https://ctan.org/pkg/bibtex"><code>bibtex</code></a>, and <a
href="https://ctan.org/pkg/biber"><code>biber</code></a> packages can
optionally be used for <a href="#citation-rendering">citation
rendering</a>. The following packages will be used to improve output
quality if present, but pandoc does not require them to be present: <a
href="https://ctan.org/pkg/upquote"><code>upquote</code></a> (for
straight quotes in verbatim environments), <a
href="https://ctan.org/pkg/microtype"><code>microtype</code></a> (for
better spacing adjustments), <a
href="https://ctan.org/pkg/parskip"><code>parskip</code></a> (for better
inter-paragraph spaces), <a
href="https://ctan.org/pkg/xurl"><code>xurl</code></a> (for better line
breaks in URLs), <a
href="https://ctan.org/pkg/bookmark"><code>bookmark</code></a> (for
better PDF bookmarks), and <a
href="https://ctan.org/pkg/footnotehyper"><code>footnotehyper</code></a>
or <a href="https://ctan.org/pkg/footnote"><code>footnote</code></a> (to
allow footnotes in tables).</p>
<h2 id="reading-from-the-web">Reading from the Web</h2>
<p>Instead of an input file, an absolute URI may be given. In this case
pandoc will fetch the content using HTTP:</p>
<pre><code>pandoc -f html -t markdown https://www.fsf.org</code></pre>
<p>It is possible to supply a custom User-Agent string or other header
when requesting a document from a URL:</p>
<pre><code>pandoc -f html -t markdown --request-header User-Agent:&quot;Mozilla/5.0&quot; \
  https://www.fsf.org</code></pre>
<h1 id="options">Options</h1>
<h2 class="options" id="general-options">General options</h2>
<dl>
<dt><code>-f</code> <em>FORMAT</em>, <code>-r</code> <em>FORMAT</em>,
<code>--from=</code><em>FORMAT</em>,
<code>--read=</code><em>FORMAT</em></dt>
<dd>
<p>Specify input format. <em>FORMAT</em> can be:</p>
<div id="input-formats">
<ul>
<li><code>bibtex</code> (<a
href="https://ctan.org/pkg/bibtex">BibTeX</a> bibliography)</li>
<li><code>biblatex</code> (<a
href="https://ctan.org/pkg/biblatex">BibLaTeX</a> bibliography)</li>
<li><code>commonmark</code> (<a
href="https://commonmark.org">CommonMark</a> Markdown)</li>
<li><code>commonmark_x</code> (<a
href="https://commonmark.org">CommonMark</a> Markdown with
extensions)</li>
<li><code>creole</code> (<a
href="http://www.wikicreole.org/wiki/Creole1.0">Creole 1.0</a>)</li>
<li><code>csljson</code> (<a
href="https://citeproc-js.readthedocs.io/en/latest/csl-json/markup.html">CSL
JSON</a> bibliography)</li>
<li><code>csv</code> (<a
href="https://tools.ietf.org/html/rfc4180">CSV</a> table)</li>
<li><code>tsv</code> (<a
href="https://www.iana.org/assignments/media-types/text/tab-separated-values">TSV</a>
table)</li>
<li><code>docbook</code> (<a
href="https://docbook.org">DocBook</a>)</li>
<li><code>docx</code> (<a
href="https://en.wikipedia.org/wiki/Office_Open_XML">Word docx</a>)</li>
<li><code>dokuwiki</code> (<a
href="https://www.dokuwiki.org/dokuwiki">DokuWiki markup</a>)</li>
<li><code>endnotexml</code> (<a
href="https://support.clarivate.com/Endnote/s/article/EndNote-XML-Document-Type-Definition">EndNote
XML bibliography</a>)</li>
<li><code>epub</code> (<a href="http://idpf.org/epub">EPUB</a>)</li>
<li><code>fb2</code> (<a
href="http://www.fictionbook.org/index.php/Eng:XML_Schema_Fictionbook_2.1">FictionBook2</a>
e-book)</li>
<li><code>gfm</code> (<a
href="https://help.github.com/articles/github-flavored-markdown/">GitHub-Flavored
Markdown</a>), or the deprecated and less accurate
<code>markdown_github</code>; use <a
href="#markdown-variants"><code>markdown_github</code></a> only if you
need extensions not supported in <a
href="#markdown-variants"><code>gfm</code></a>.</li>
<li><code>haddock</code> (<a
href="https://www.haskell.org/haddock/doc/html/ch03s08.html">Haddock
markup</a>)</li>
<li><code>html</code> (<a href="https://www.w3.org/html/">HTML</a>)</li>
<li><code>ipynb</code> (<a
href="https://nbformat.readthedocs.io/en/latest/">Jupyter
notebook</a>)</li>
<li><code>jats</code> (<a href="https://jats.nlm.nih.gov">JATS</a>
XML)</li>
<li><code>jira</code> (<a
href="https://jira.atlassian.com/secure/WikiRendererHelpAction.jspa?section=all">Jira</a>/Confluence
wiki markup)</li>
<li><code>json</code> (JSON version of native AST)</li>
<li><code>latex</code> (<a
href="https://www.latex-project.org/">LaTeX</a>)</li>
<li><code>markdown</code> (<a href="#pandocs-markdown">Pandoc’s
Markdown</a>)</li>
<li><code>markdown_mmd</code> (<a
href="https://fletcherpenney.net/multimarkdown/">MultiMarkdown</a>)</li>
<li><code>markdown_phpextra</code> (<a
href="https://michelf.ca/projects/php-markdown/extra/">PHP Markdown
Extra</a>)</li>
<li><code>markdown_strict</code> (original unextended <a
href="https://daringfireball.net/projects/markdown/">Markdown</a>)</li>
<li><code>mediawiki</code> (<a
href="https://www.mediawiki.org/wiki/Help:Formatting">MediaWiki
markup</a>)</li>
<li><code>man</code> (<a href="https://man.cx/groff_man(7)">roff
man</a>)</li>
<li><code>muse</code> (<a
href="https://amusewiki.org/library/manual">Muse</a>)</li>
<li><code>native</code> (native Haskell)</li>
<li><code>odt</code> (<a
href="https://en.wikipedia.org/wiki/OpenDocument">ODT</a>)</li>
<li><code>opml</code> (<a
href="http://dev.opml.org/spec2.html">OPML</a>)</li>
<li><code>org</code> (<a href="https://orgmode.org">Emacs Org
mode</a>)</li>
<li><code>ris</code> (<a
href="https://en.wikipedia.org/wiki/RIS_(file_format)">RIS</a>
bibliography)</li>
<li><code>rtf</code> (<a
href="https://en.wikipedia.org/wiki/Rich_Text_Format">Rich Text
Format</a>)</li>
<li><code>rst</code> (<a
href="https://docutils.sourceforge.io/docs/ref/rst/introduction.html">reStructuredText</a>)</li>
<li><code>t2t</code> (<a href="https://txt2tags.org">txt2tags</a>)</li>
<li><code>textile</code> (<a
href="https://textile-lang.com">Textile</a>)</li>
<li><code>tikiwiki</code> (<a
href="https://doc.tiki.org/Wiki-Syntax-Text#The_Markup_Language_Wiki-Syntax">TikiWiki
markup</a>)</li>
<li><code>twiki</code> (<a
href="https://twiki.org/cgi-bin/view/TWiki/TextFormattingRules">TWiki
markup</a>)</li>
<li><code>typst</code> (<a href="https://typst.app">typst</a>)</li>
<li><code>vimwiki</code> (<a
href="https://vimwiki.github.io">Vimwiki</a>)</li>
<li>the path of a custom Lua reader, see <a
href="#custom-readers-and-writers">Custom readers and writers</a>
below</li>
</ul>
</div>
<p>Extensions can be individually enabled or disabled by appending
<code>+EXTENSION</code> or <code>-EXTENSION</code> to the format name.
See <a href="#extensions">Extensions</a> below, for a list of extensions
and their names. See <code>--list-input-formats</code> and
<code>--list-extensions</code>, below.</p>
</dd>
<dt><code>-t</code> <em>FORMAT</em>, <code>-w</code> <em>FORMAT</em>,
<code>--to=</code><em>FORMAT</em>,
<code>--write=</code><em>FORMAT</em></dt>
<dd>
<p>Specify output format. <em>FORMAT</em> can be:</p>
<div id="output-formats">
<ul>
<li><code>asciidoc</code> (modern <a
href="https://www.methods.co.nz/asciidoc/">AsciiDoc</a> as interpreted
by <a href="https://asciidoctor.org/">AsciiDoctor</a>)</li>
<li><code>asciidoc_legacy</code> (<a
href="https://www.methods.co.nz/asciidoc/">AsciiDoc</a> as interpreted
by <a
href="https://github.com/asciidoc-py/asciidoc-py"><code>asciidoc-py</code></a>).</li>
<li><code>asciidoctor</code> (deprecated synonym for
<code>asciidoc</code>)</li>
<li><code>beamer</code> (<a href="https://ctan.org/pkg/beamer">LaTeX
beamer</a> slide show)</li>
<li><code>bibtex</code> (<a
href="https://ctan.org/pkg/bibtex">BibTeX</a> bibliography)</li>
<li><code>biblatex</code> (<a
href="https://ctan.org/pkg/biblatex">BibLaTeX</a> bibliography)</li>
<li><code>chunkedhtml</code> (zip archive of multiple linked HTML
files)</li>
<li><code>commonmark</code> (<a
href="https://commonmark.org">CommonMark</a> Markdown)</li>
<li><code>commonmark_x</code> (<a
href="https://commonmark.org">CommonMark</a> Markdown with
extensions)</li>
<li><code>context</code> (<a
href="https://www.contextgarden.net/">ConTeXt</a>)</li>
<li><code>csljson</code> (<a
href="https://citeproc-js.readthedocs.io/en/latest/csl-json/markup.html">CSL
JSON</a> bibliography)</li>
<li><code>docbook</code> or <code>docbook4</code> (<a
href="https://docbook.org">DocBook</a> 4)</li>
<li><code>docbook5</code> (DocBook 5)</li>
<li><code>docx</code> (<a
href="https://en.wikipedia.org/wiki/Office_Open_XML">Word docx</a>)</li>
<li><code>dokuwiki</code> (<a
href="https://www.dokuwiki.org/dokuwiki">DokuWiki markup</a>)</li>
<li><code>epub</code> or <code>epub3</code> (<a
href="http://idpf.org/epub">EPUB</a> v3 book)</li>
<li><code>epub2</code> (EPUB v2)</li>
<li><code>fb2</code> (<a
href="http://www.fictionbook.org/index.php/Eng:XML_Schema_Fictionbook_2.1">FictionBook2</a>
e-book)</li>
<li><code>gfm</code> (<a
href="https://help.github.com/articles/github-flavored-markdown/">GitHub-Flavored
Markdown</a>), or the deprecated and less accurate
<code>markdown_github</code>; use <a
href="#markdown-variants"><code>markdown_github</code></a> only if you
need extensions not supported in <a
href="#markdown-variants"><code>gfm</code></a>.</li>
<li><code>haddock</code> (<a
href="https://www.haskell.org/haddock/doc/html/ch03s08.html">Haddock
markup</a>)</li>
<li><code>html</code> or <code>html5</code> (<a
href="https://www.w3.org/html/">HTML</a>, i.e. <a
href="https://html.spec.whatwg.org/">HTML5</a>/XHTML <a
href="https://www.w3.org/TR/html-polyglot/">polyglot markup</a>)</li>
<li><code>html4</code> (<a
href="https://www.w3.org/TR/xhtml1/">XHTML</a> 1.0 Transitional)</li>
<li><code>icml</code> (<a
href="https://wwwimages.adobe.com/www.adobe.com/content/dam/acom/en/devnet/indesign/sdk/cs6/idml/idml-cookbook.pdf">InDesign
ICML</a>)</li>
<li><code>ipynb</code> (<a
href="https://nbformat.readthedocs.io/en/latest/">Jupyter
notebook</a>)</li>
<li><code>jats_archiving</code> (<a
href="https://jats.nlm.nih.gov">JATS</a> XML, Archiving and Interchange
Tag Set)</li>
<li><code>jats_articleauthoring</code> (<a
href="https://jats.nlm.nih.gov">JATS</a> XML, Article Authoring Tag
Set)</li>
<li><code>jats_publishing</code> (<a
href="https://jats.nlm.nih.gov">JATS</a> XML, Journal Publishing Tag
Set)</li>
<li><code>jats</code> (alias for <code>jats_archiving</code>)</li>
<li><code>jira</code> (<a
href="https://jira.atlassian.com/secure/WikiRendererHelpAction.jspa?section=all">Jira</a>/Confluence
wiki markup)</li>
<li><code>json</code> (JSON version of native AST)</li>
<li><code>latex</code> (<a
href="https://www.latex-project.org/">LaTeX</a>)</li>
<li><code>man</code> (<a href="https://man.cx/groff_man(7)">roff
man</a>)</li>
<li><code>markdown</code> (<a href="#pandocs-markdown">Pandoc’s
Markdown</a>)</li>
<li><code>markdown_mmd</code> (<a
href="https://fletcherpenney.net/multimarkdown/">MultiMarkdown</a>)</li>
<li><code>markdown_phpextra</code> (<a
href="https://michelf.ca/projects/php-markdown/extra/">PHP Markdown
Extra</a>)</li>
<li><code>markdown_strict</code> (original unextended <a
href="https://daringfireball.net/projects/markdown/">Markdown</a>)</li>
<li><code>markua</code> (<a
href="https://leanpub.com/markua/read">Markua</a>)</li>
<li><code>mediawiki</code> (<a
href="https://www.mediawiki.org/wiki/Help:Formatting">MediaWiki
markup</a>)</li>
<li><code>ms</code> (<a href="https://man.cx/groff_ms(7)">roff
ms</a>)</li>
<li><code>muse</code> (<a
href="https://amusewiki.org/library/manual">Muse</a>)</li>
<li><code>native</code> (native Haskell)</li>
<li><code>odt</code> (<a
href="https://en.wikipedia.org/wiki/OpenDocument">OpenOffice text
document</a>)</li>
<li><code>opml</code> (<a
href="http://dev.opml.org/spec2.html">OPML</a>)</li>
<li><code>opendocument</code> (<a
href="http://opendocument.xml.org">OpenDocument</a>)</li>
<li><code>org</code> (<a href="https://orgmode.org">Emacs Org
mode</a>)</li>
<li><code>pdf</code> (<a href="https://www.adobe.com/pdf/">PDF</a>)</li>
<li><code>plain</code> (plain text)</li>
<li><code>pptx</code> (<a
href="https://en.wikipedia.org/wiki/Microsoft_PowerPoint">PowerPoint</a>
slide show)</li>
<li><code>rst</code> (<a
href="https://docutils.sourceforge.io/docs/ref/rst/introduction.html">reStructuredText</a>)</li>
<li><code>rtf</code> (<a
href="https://en.wikipedia.org/wiki/Rich_Text_Format">Rich Text
Format</a>)</li>
<li><code>texinfo</code> (<a
href="https://www.gnu.org/software/texinfo/">GNU Texinfo</a>)</li>
<li><code>textile</code> (<a
href="https://textile-lang.com">Textile</a>)</li>
<li><code>slideous</code> (<a
href="https://goessner.net/articles/slideous/">Slideous</a> HTML and
JavaScript slide show)</li>
<li><code>slidy</code> (<a
href="https://www.w3.org/Talks/Tools/Slidy2/">Slidy</a> HTML and
JavaScript slide show)</li>
<li><code>dzslides</code> (<a
href="https://paulrouget.com/dzslides/">DZSlides</a> HTML5 + JavaScript
slide show)</li>
<li><code>revealjs</code> (<a href="https://revealjs.com/">reveal.js</a>
HTML5 + JavaScript slide show)</li>
<li><code>s5</code> (<a
href="https://meyerweb.com/eric/tools/s5/">S5</a> HTML and JavaScript
slide show)</li>
<li><code>tei</code> (<a href="https://github.com/TEIC/TEI-Simple">TEI
Simple</a>)</li>
<li><code>typst</code> (<a href="https://typst.app">typst</a>)</li>
<li><code>xwiki</code> (<a
href="https://www.xwiki.org/xwiki/bin/view/Documentation/UserGuide/Features/XWikiSyntax/">XWiki
markup</a>)</li>
<li><code>zimwiki</code> (<a
href="https://zim-wiki.org/manual/Help/Wiki_Syntax.html">ZimWiki
markup</a>)</li>
<li>the path of a custom Lua writer, see <a
href="#custom-readers-and-writers">Custom readers and writers</a>
below</li>
</ul>
</div>
<p>Note that <code>odt</code>, <code>docx</code>, <code>epub</code>, and
<code>pdf</code> output will not be directed to <em>stdout</em> unless
forced with <code>-o -</code>.</p>
<p>Extensions can be individually enabled or disabled by appending
<code>+EXTENSION</code> or <code>-EXTENSION</code> to the format name.
See <a href="#extensions">Extensions</a> below, for a list of extensions
and their names. See <code>--list-output-formats</code> and
<code>--list-extensions</code>, below.</p>
</dd>
<dt><code>-o</code> <em>FILE</em>,
<code>--output=</code><em>FILE</em></dt>
<dd>
<p>Write output to <em>FILE</em> instead of <em>stdout</em>. If
<em>FILE</em> is <code>-</code>, output will go to <em>stdout</em>, even
if a non-textual format (<code>docx</code>, <code>odt</code>,
<code>epub2</code>, <code>epub3</code>) is specified. If the output
format is <code>chunkedhtml</code> and <em>FILE</em> has no extension,
then instead of producing a <code>.zip</code> file pandoc will create a
directory <em>FILE</em> and unpack the zip archive there (unless
<em>FILE</em> already exists, in which case an error will be
raised).</p>
</dd>
<dt><code>--data-dir=</code><em>DIRECTORY</em></dt>
<dd>
<p>Specify the user data directory to search for pandoc data files. If
this option is not specified, the default user data directory will be
used. On *nix and macOS systems this will be the <code>pandoc</code>
subdirectory of the XDG data directory (by default,
<code>$HOME/.local/share</code>, overridable by setting the
<code>XDG_DATA_HOME</code> environment variable). If that directory does
not exist and <code>$HOME/.pandoc</code> exists, it will be used (for
backwards compatibility). On Windows the default user data directory is
<code>%APPDATA%\pandoc</code>. You can find the default user data
directory on your system by looking at the output of
<code>pandoc --version</code>. Data files placed in this directory (for
example, <code>reference.odt</code>, <code>reference.docx</code>,
<code>epub.css</code>, <code>templates</code>) will override pandoc’s
normal defaults. (Note that the user data directory is not created by
pandoc, so you will need to create it yourself if you want to make use
of it.)</p>
</dd>
<dt><code>-d</code> <em>FILE</em>,
<code>--defaults=</code><em>FILE</em></dt>
<dd>
<p>Specify a set of default option settings. <em>FILE</em> is a YAML
file whose fields correspond to command-line option settings. All
options for document conversion, including input and output files, can
be set using a defaults file. The file will be searched for first in the
working directory, and then in the <code>defaults</code> subdirectory of
the user data directory (see <code>--data-dir</code>). The
<code>.yaml</code> extension may be omitted. See the section <a
href="#defaults-files">Defaults files</a> for more information on the
file format. Settings from the defaults file may be overridden or
extended by subsequent options on the command line.</p>
</dd>
<dt><code>--bash-completion</code></dt>
<dd>
<p>Generate a bash completion script. To enable bash completion with
pandoc, add this to your <code>.bashrc</code>:</p>
<pre><code>eval &quot;$(pandoc --bash-completion)&quot;</code></pre>
</dd>
<dt><code>--verbose</code></dt>
<dd>
<p>Give verbose debugging output.</p>
</dd>
<dt><code>--quiet</code></dt>
<dd>
<p>Suppress warning messages.</p>
</dd>
<dt><code>--fail-if-warnings[=true|false]</code></dt>
<dd>
<p>Exit with error status if there are any warnings.</p>
</dd>
<dt><code>--log=</code><em>FILE</em></dt>
<dd>
<p>Write log messages in machine-readable JSON format to <em>FILE</em>.
All messages above DEBUG level will be written, regardless of verbosity
settings (<code>--verbose</code>, <code>--quiet</code>).</p>
</dd>
<dt><code>--list-input-formats</code></dt>
<dd>
<p>List supported input formats, one per line.</p>
</dd>
<dt><code>--list-output-formats</code></dt>
<dd>
<p>List supported output formats, one per line.</p>
</dd>
<dt><code>--list-extensions</code>[<code>=</code><em>FORMAT</em>]</dt>
<dd>
<p>List supported extensions for <em>FORMAT</em>, one per line, preceded
by a <code>+</code> or <code>-</code> indicating whether it is enabled
by default in <em>FORMAT</em>. If <em>FORMAT</em> is not specified,
defaults for pandoc’s Markdown are given.</p>
</dd>
<dt><code>--list-highlight-languages</code></dt>
<dd>
<p>List supported languages for syntax highlighting, one per line.</p>
</dd>
<dt><code>--list-highlight-styles</code></dt>
<dd>
<p>List supported styles for syntax highlighting, one per line. See
<code>--highlight-style</code>.</p>
</dd>
<dt><code>-v</code>, <code>--version</code></dt>
<dd>
<p>Print version.</p>
</dd>
<dt><code>-h</code>, <code>--help</code></dt>
<dd>
<p>Show usage message.</p>
</dd>
</dl>
<h2 class="options" id="reader-options">Reader options</h2>
<dl>
<dt><code>--shift-heading-level-by=</code><em>NUMBER</em></dt>
<dd>
<p>Shift heading levels by a positive or negative integer. For example,
with <code>--shift-heading-level-by=-1</code>, level 2 headings become
level 1 headings, and level 3 headings become level 2 headings. Headings
cannot have a level less than 1, so a heading that would be shifted
below level 1 becomes a regular paragraph. Exception: with a shift of
-N, a level-N heading at the beginning of the document replaces the
metadata title. <code>--shift-heading-level-by=-1</code> is a good
choice when converting HTML or Markdown documents that use an initial
level-1 heading for the document title and level-2+ headings for
sections. <code>--shift-heading-level-by=1</code> may be a good choice
for converting Markdown documents that use level-1 headings for sections
to HTML, since pandoc uses a level-1 heading to render the document
title.</p>
</dd>
<dt><code>--base-header-level=</code><em>NUMBER</em></dt>
<dd>
<p><em>Deprecated. Use <code>--shift-heading-level-by</code>=X instead,
where X = NUMBER - 1.</em> Specify the base level for headings (defaults
to 1).</p>
</dd>
<dt><code>--indented-code-classes=</code><em>CLASSES</em></dt>
<dd>
<p>Specify classes to use for indented code blocks–for example,
<code>perl,numberLines</code> or <code>haskell</code>. Multiple classes
may be separated by spaces or commas.</p>
</dd>
<dt><code>--default-image-extension=</code><em>EXTENSION</em></dt>
<dd>
<p>Specify a default extension to use when image paths/URLs have no
extension. This allows you to use the same source for formats that
require different kinds of images. Currently this option only affects
the Markdown and LaTeX readers.</p>
</dd>
<dt><code>--file-scope[=true|false]</code></dt>
<dd>
<p>Parse each file individually before combining for multifile
documents. This will allow footnotes in different files with the same
identifiers to work as expected. If this option is set, footnotes and
links will not work across files. Reading binary files (docx, odt, epub)
implies <code>--file-scope</code>.</p>
<p>If two or more files are processed using <code>--file-scope</code>,
prefixes based on the filenames will be added to identifiers in order to
disambiguate them, and internal links will be adjusted accordingly. For
example, a header with identifier <code>foo</code> in
<code>subdir/file1.txt</code> will have its identifier changed to
<code>subdir__file1.txt__foo</code>.</p>
<p>In addition, a Div with an identifier based on the filename will be
added around the file’s content, so that internal links to the filename
will point to this Div’s identifier.</p>
</dd>
<dt><code>-F</code> <em>PROGRAM</em>,
<code>--filter=</code><em>PROGRAM</em></dt>
<dd>
<p>Specify an executable to be used as a filter transforming the pandoc
AST after the input is parsed and before the output is written. The
executable should read JSON from stdin and write JSON to stdout. The
JSON must be formatted like pandoc’s own JSON input and output. The name
of the output format will be passed to the filter as the first argument.
Hence,</p>
<pre><code>pandoc --filter ./caps.py -t latex</code></pre>
<p>is equivalent to</p>
<pre><code>pandoc -t json | ./caps.py latex | pandoc -f json -t latex</code></pre>
<p>The latter form may be useful for debugging filters.</p>
<p>Filters may be written in any language. <code>Text.Pandoc.JSON</code>
exports <code>toJSONFilter</code> to facilitate writing filters in
Haskell. Those who would prefer to write filters in python can use the
module <a
href="https://github.com/jgm/pandocfilters"><code>pandocfilters</code></a>,
installable from PyPI. There are also pandoc filter libraries in <a
href="https://github.com/vinai/pandocfilters-php">PHP</a>, <a
href="https://metacpan.org/pod/Pandoc::Filter">perl</a>, and <a
href="https://github.com/mvhenderson/pandoc-filter-node">JavaScript/node.js</a>.</p>
<p>In order of preference, pandoc will look for filters in</p>
<ol type="1">
<li><p>a specified full or relative path (executable or
non-executable),</p></li>
<li><p><code>$DATADIR/filters</code> (executable or non-executable)
where <code>$DATADIR</code> is the user data directory (see
<code>--data-dir</code>, above),</p></li>
<li><p><code>$PATH</code> (executable only).</p></li>
</ol>
<p>Filters, Lua-filters, and citeproc processing are applied in the
order specified on the command line.</p>
</dd>
<dt><code>-L</code> <em>SCRIPT</em>,
<code>--lua-filter=</code><em>SCRIPT</em></dt>
<dd>
<p>Transform the document in a similar fashion as JSON filters (see
<code>--filter</code>), but use pandoc’s built-in Lua filtering system.
The given Lua script is expected to return a list of Lua filters which
will be applied in order. Each Lua filter must contain
element-transforming functions indexed by the name of the AST element on
which the filter function should be applied.</p>
<p>The <code>pandoc</code> Lua module provides helper functions for
element creation. It is always loaded into the script’s Lua
environment.</p>
<p>See the <a href="https://pandoc.org/lua-filters.html">Lua filters
documentation</a> for further details.</p>
<p>In order of preference, pandoc will look for Lua filters in</p>
<ol type="1">
<li><p>a specified full or relative path,</p></li>
<li><p><code>$DATADIR/filters</code> where <code>$DATADIR</code> is the
user data directory (see <code>--data-dir</code>, above).</p></li>
</ol>
<p>Filters, Lua filters, and citeproc processing are applied in the
order specified on the command line.</p>
</dd>
<dt><code>-M</code> <em>KEY</em>[<code>=</code><em>VAL</em>],
<code>--metadata=</code><em>KEY</em>[<code>:</code><em>VAL</em>]</dt>
<dd>
<p>Set the metadata field <em>KEY</em> to the value <em>VAL</em>. A
value specified on the command line overrides a value specified in the
document using <a href="#extension-yaml_metadata_block">YAML metadata
blocks</a>. Values will be parsed as YAML boolean or string values. If
no value is specified, the value will be treated as Boolean true. Like
<code>--variable</code>, <code>--metadata</code> causes template
variables to be set. But unlike <code>--variable</code>,
<code>--metadata</code> affects the metadata of the underlying document
(which is accessible from filters and may be printed in some output
formats) and metadata values will be escaped when inserted into the
template.</p>
</dd>
<dt><code>--metadata-file=</code><em>FILE</em></dt>
<dd>
<p>Read metadata from the supplied YAML (or JSON) file. This option can
be used with every input format, but string scalars in the metadata file
will always be parsed as Markdown. (If the input format is Markdown or a
Markdown variant, then the same variant will be used to parse the
metadata file; if it is a non-Markdown format, pandoc’s default Markdown
extensions will be used.) This option can be used repeatedly to include
multiple metadata files; values in files specified later on the command
line will be preferred over those specified in earlier files. Metadata
values specified inside the document, or by using <code>-M</code>,
overwrite values specified with this option. The file will be searched
for first in the working directory, and then in the
<code>metadata</code> subdirectory of the user data directory (see
<code>--data-dir</code>).</p>
</dd>
<dt><code>-p</code>, <code>--preserve-tabs[=true|false]</code></dt>
<dd>
<p>Preserve tabs instead of converting them to spaces. (By default,
pandoc converts tabs to spaces before parsing its input.) Note that this
will only affect tabs in literal code spans and code blocks. Tabs in
regular text are always treated as spaces.</p>
</dd>
<dt><code>--tab-stop=</code><em>NUMBER</em></dt>
<dd>
<p>Specify the number of spaces per tab (default is 4).</p>
</dd>
<dt><code>--track-changes=accept</code>|<code>reject</code>|<code>all</code></dt>
<dd>
<p>Specifies what to do with insertions, deletions, and comments
produced by the MS Word “Track Changes” feature. <code>accept</code>
(the default) processes all the insertions and deletions.
<code>reject</code> ignores them. Both <code>accept</code> and
<code>reject</code> ignore comments. <code>all</code> includes all
insertions, deletions, and comments, wrapped in spans with
<code>insertion</code>, <code>deletion</code>,
<code>comment-start</code>, and <code>comment-end</code> classes,
respectively. The author and time of change is included.
<code>all</code> is useful for scripting: only accepting changes from a
certain reviewer, say, or before a certain date. If a paragraph is
inserted or deleted, <code>track-changes=all</code> produces a span with
the class
<code>paragraph-insertion</code>/<code>paragraph-deletion</code> before
the affected paragraph break. This option only affects the docx
reader.</p>
</dd>
<dt><code>--extract-media=</code><em>DIR</em></dt>
<dd>
<p>Extract images and other media contained in or linked from the source
document to the path <em>DIR</em>, creating it if necessary, and adjust
the images references in the document so they point to the extracted
files. Media are downloaded, read from the file system, or extracted
from a binary container (e.g. docx), as needed. The original file paths
are used if they are relative paths not containing <code>..</code>.
Otherwise filenames are constructed from the SHA1 hash of the
contents.</p>
</dd>
<dt><code>--abbreviations=</code><em>FILE</em></dt>
<dd>
<p>Specifies a custom abbreviations file, with abbreviations one to a
line. If this option is not specified, pandoc will read the data file
<code>abbreviations</code> from the user data directory or fall back on
a system default. To see the system default, use
<code>pandoc --print-default-data-file=abbreviations</code>. The only
use pandoc makes of this list is in the Markdown reader. Strings found
in this list will be followed by a nonbreaking space, and the period
will not produce sentence-ending space in formats like LaTeX. The
strings may not contain spaces.</p>
</dd>
<dt><code>--trace[=true|false]</code></dt>
<dd>
<p>Print diagnostic output tracing parser progress to stderr. This
option is intended for use by developers in diagnosing performance
issues.</p>
</dd>
</dl>
<h2 class="options" id="general-writer-options">General writer
options</h2>
<dl>
<dt><code>-s</code>, <code>--standalone</code></dt>
<dd>
<p>Produce output with an appropriate header and footer (e.g. a
standalone HTML, LaTeX, TEI, or RTF file, not a fragment). This option
is set automatically for <code>pdf</code>, <code>epub</code>,
<code>epub3</code>, <code>fb2</code>, <code>docx</code>, and
<code>odt</code> output. For <code>native</code> output, this option
causes metadata to be included; otherwise, metadata is suppressed.</p>
</dd>
<dt><code>--template=</code><em>FILE</em>|<em>URL</em></dt>
<dd>
<p>Use the specified file as a custom template for the generated
document. Implies <code>--standalone</code>. See <a
href="#templates">Templates</a>, below, for a description of template
syntax. If no extension is specified, an extension corresponding to the
writer will be added, so that <code>--template=special</code> looks for
<code>special.html</code> for HTML output. If the template is not found,
pandoc will search for it in the <code>templates</code> subdirectory of
the user data directory (see <code>--data-dir</code>). If this option is
not used, a default template appropriate for the output format will be
used (see <code>-D/--print-default-template</code>).</p>
</dd>
<dt><code>-V</code> <em>KEY</em>[<code>=</code><em>VAL</em>],
<code>--variable=</code><em>KEY</em>[<code>:</code><em>VAL</em>]</dt>
<dd>
<p>Set the template variable <em>KEY</em> to the value <em>VAL</em> when
rendering the document in standalone mode. If no <em>VAL</em> is
specified, the key will be given the value <code>true</code>.</p>
</dd>
<dt><code>--sandbox[=true|false]</code></dt>
<dd>
<p>Run pandoc in a sandbox, limiting IO operations in readers and
writers to reading the files specified on the command line. Note that
this option does not limit IO operations by filters or in the production
of PDF documents. But it does offer security against, for example,
disclosure of files through the use of <code>include</code> directives.
Anyone using pandoc on untrusted user input should use this option.</p>
<p>Note: some readers and writers (e.g., <code>docx</code>) need access
to data files. If these are stored on the file system, then pandoc will
not be able to find them when run in <code>--sandbox</code> mode and
will raise an error. For these applications, we recommend using a pandoc
binary compiled with the <code>embed_data_files</code> option, which
causes the data files to be baked into the binary instead of being
stored on the file system.</p>
</dd>
<dt><code>-D</code> <em>FORMAT</em>,
<code>--print-default-template=</code><em>FORMAT</em></dt>
<dd>
<p>Print the system default template for an output <em>FORMAT</em>. (See
<code>-t</code> for a list of possible <em>FORMAT</em>s.) Templates in
the user data directory are ignored. This option may be used with
<code>-o</code>/<code>--output</code> to redirect output to a file, but
<code>-o</code>/<code>--output</code> must come before
<code>--print-default-template</code> on the command line.</p>
<p>Note that some of the default templates use partials, for example
<code>styles.html</code>. To print the partials, use
<code>--print-default-data-file</code>: for example,
<code>--print-default-data-file=templates/styles.html</code>.</p>
</dd>
<dt><code>--print-default-data-file=</code><em>FILE</em></dt>
<dd>
<p>Print a system default data file. Files in the user data directory
are ignored. This option may be used with
<code>-o</code>/<code>--output</code> to redirect output to a file, but
<code>-o</code>/<code>--output</code> must come before
<code>--print-default-data-file</code> on the command line.</p>
</dd>
<dt><code>--eol=crlf</code>|<code>lf</code>|<code>native</code></dt>
<dd>
<p>Manually specify line endings: <code>crlf</code> (Windows),
<code>lf</code> (macOS/Linux/UNIX), or <code>native</code> (line endings
appropriate to the OS on which pandoc is being run). The default is
<code>native</code>.</p>
</dd>
<dt><code>--dpi</code>=<em>NUMBER</em></dt>
<dd>
<p>Specify the default dpi (dots per inch) value for conversion from
pixels to inch/centimeters and vice versa. (Technically, the correct
term would be ppi: pixels per inch.) The default is 96dpi. When images
contain information about dpi internally, the encoded value is used
instead of the default specified by this option.</p>
</dd>
<dt><code>--wrap=auto</code>|<code>none</code>|<code>preserve</code></dt>
<dd>
<p>Determine how text is wrapped in the output (the source code, not the
rendered version). With <code>auto</code> (the default), pandoc will
attempt to wrap lines to the column width specified by
<code>--columns</code> (default 72). With <code>none</code>, pandoc will
not wrap lines at all. With <code>preserve</code>, pandoc will attempt
to preserve the wrapping from the source document (that is, where there
are nonsemantic newlines in the source, there will be nonsemantic
newlines in the output as well). In <code>ipynb</code> output, this
option affects wrapping of the contents of markdown cells.</p>
</dd>
<dt><code>--columns=</code><em>NUMBER</em></dt>
<dd>
<p>Specify length of lines in characters. This affects text wrapping in
the generated source code (see <code>--wrap</code>). It also affects
calculation of column widths for plain text tables (see <a
href="#tables">Tables</a> below).</p>
</dd>
<dt><code>--toc[=true|false]</code>,
<code>--table-of-contents[=true|false]</code></dt>
<dd>
<p>Include an automatically generated table of contents (or, in the case
of <code>latex</code>, <code>context</code>, <code>docx</code>,
<code>odt</code>, <code>opendocument</code>, <code>rst</code>, or
<code>ms</code>, an instruction to create one) in the output document.
This option has no effect unless <code>-s/--standalone</code> is used,
and it has no effect on <code>man</code>, <code>docbook4</code>,
<code>docbook5</code>, or <code>jats</code> output.</p>
<p>Note that if you are producing a PDF via <code>ms</code>, the table
of contents will appear at the beginning of the document, before the
title. If you would prefer it to be at the end of the document, use the
option <code>--pdf-engine-opt=--no-toc-relocation</code>.</p>
</dd>
<dt><code>--toc-depth=</code><em>NUMBER</em></dt>
<dd>
<p>Specify the number of section levels to include in the table of
contents. The default is 3 (which means that level-1, 2, and 3 headings
will be listed in the contents).</p>
</dd>
<dt><code>--strip-comments[=true|false]</code></dt>
<dd>
<p>Strip out HTML comments in the Markdown or Textile source, rather
than passing them on to Markdown, Textile or HTML output as raw HTML.
This does not apply to HTML comments inside raw HTML blocks when the
<code>markdown_in_html_blocks</code> extension is not set.</p>
</dd>
<dt><code>--no-highlight</code></dt>
<dd>
<p>Disables syntax highlighting for code blocks and inlines, even when a
language attribute is given.</p>
</dd>
<dt><code>--highlight-style=</code><em>STYLE</em>|<em>FILE</em></dt>
<dd>
<p>Specifies the coloring style to be used in highlighted source code.
Options are <code>pygments</code> (the default), <code>kate</code>,
<code>monochrome</code>, <code>breezeDark</code>, <code>espresso</code>,
<code>zenburn</code>, <code>haddock</code>, and <code>tango</code>. For
more information on syntax highlighting in pandoc, see <a
href="#syntax-highlighting">Syntax highlighting</a>, below. See also
<code>--list-highlight-styles</code>.</p>
<p>Instead of a <em>STYLE</em> name, a JSON file with extension
<code>.theme</code> may be supplied. This will be parsed as a KDE syntax
highlighting theme and (if valid) used as the highlighting style.</p>
<p>To generate the JSON version of an existing style, use
<code>--print-highlight-style</code>.</p>
</dd>
<dt><code>--print-highlight-style=</code><em>STYLE</em>|<em>FILE</em></dt>
<dd>
<p>Prints a JSON version of a highlighting style, which can be modified,
saved with a <code>.theme</code> extension, and used with
<code>--highlight-style</code>. This option may be used with
<code>-o</code>/<code>--output</code> to redirect output to a file, but
<code>-o</code>/<code>--output</code> must come before
<code>--print-highlight-style</code> on the command line.</p>
</dd>
<dt><code>--syntax-definition=</code><em>FILE</em></dt>
<dd>
<p>Instructs pandoc to load a KDE XML syntax definition file, which will
be used for syntax highlighting of appropriately marked code blocks.
This can be used to add support for new languages or to use altered
syntax definitions for existing languages. This option may be repeated
to add multiple syntax definitions.</p>
</dd>
<dt><code>-H</code> <em>FILE</em>,
<code>--include-in-header=</code><em>FILE</em>|<em>URL</em></dt>
<dd>
<p>Include contents of <em>FILE</em>, verbatim, at the end of the
header. This can be used, for example, to include special CSS or
JavaScript in HTML documents. This option can be used repeatedly to
include multiple files in the header. They will be included in the order
specified. Implies <code>--standalone</code>.</p>
</dd>
<dt><code>-B</code> <em>FILE</em>,
<code>--include-before-body=</code><em>FILE</em>|<em>URL</em></dt>
<dd>
<p>Include contents of <em>FILE</em>, verbatim, at the beginning of the
document body (e.g. after the <code>&lt;body&gt;</code> tag in HTML, or
the <code>\begin{document}</code> command in LaTeX). This can be used to
include navigation bars or banners in HTML documents. This option can be
used repeatedly to include multiple files. They will be included in the
order specified. Implies <code>--standalone</code>.</p>
</dd>
<dt><code>-A</code> <em>FILE</em>,
<code>--include-after-body=</code><em>FILE</em>|<em>URL</em></dt>
<dd>
<p>Include contents of <em>FILE</em>, verbatim, at the end of the
document body (before the <code>&lt;/body&gt;</code> tag in HTML, or the
<code>\end{document}</code> command in LaTeX). This option can be used
repeatedly to include multiple files. They will be included in the order
specified. Implies <code>--standalone</code>.</p>
</dd>
<dt><code>--resource-path=</code><em>SEARCHPATH</em></dt>
<dd>
<p>List of paths to search for images and other resources. The paths
should be separated by <code>:</code> on Linux, UNIX, and macOS systems,
and by <code>;</code> on Windows. If <code>--resource-path</code> is not
specified, the default resource path is the working directory. Note
that, if <code>--resource-path</code> is specified, the working
directory must be explicitly listed or it will not be searched. For
example: <code>--resource-path=.:test</code> will search the working
directory and the <code>test</code> subdirectory, in that order. This
option can be used repeatedly. Search path components that come later on
the command line will be searched before those that come earlier, so
<code>--resource-path foo:bar --resource-path baz:bim</code> is
equivalent to <code>--resource-path baz:bim:foo:bar</code>.</p>
</dd>
<dt><code>--request-header=</code><em>NAME</em><code>:</code><em>VAL</em></dt>
<dd>
<p>Set the request header <em>NAME</em> to the value <em>VAL</em> when
making HTTP requests (for example, when a URL is given on the command
line, or when resources used in a document must be downloaded). If
you’re behind a proxy, you also need to set the environment variable
<code>http_proxy</code> to <code>http://...</code>.</p>
</dd>
<dt><code>--no-check-certificate[=true|false]</code></dt>
<dd>
<p>Disable the certificate verification to allow access to unsecure HTTP
resources (for example when the certificate is no longer valid or self
signed).</p>
</dd>
</dl>
<h2 class="options" id="options-affecting-specific-writers">Options
affecting specific writers</h2>
<dl>
<dt><code>--self-contained[=true|false]</code></dt>
<dd>
<p><em>Deprecated synonym for
<code>--embed-resources --standalone</code>.</em></p>
</dd>
<dt><code>--embed-resources[=true|false]</code></dt>
<dd>
<p>Produce a standalone HTML file with no external dependencies, using
<code>data:</code> URIs to incorporate the contents of linked scripts,
stylesheets, images, and videos. The resulting file should be
“self-contained,” in the sense that it needs no external files and no
net access to be displayed properly by a browser. This option works only
with HTML output formats, including <code>html4</code>,
<code>html5</code>, <code>html+lhs</code>, <code>html5+lhs</code>,
<code>s5</code>, <code>slidy</code>, <code>slideous</code>,
<code>dzslides</code>, and <code>revealjs</code>. Scripts, images, and
stylesheets at absolute URLs will be downloaded; those at relative URLs
will be sought relative to the working directory (if the first source
file is local) or relative to the base URL (if the first source file is
remote). Elements with the attribute <code>data-external="1"</code> will
be left alone; the documents they link to will not be incorporated in
the document. Limitation: resources that are loaded dynamically through
JavaScript cannot be incorporated; as a result, fonts may be missing
when <code>--mathjax</code> is used, and some advanced features
(e.g. zoom or speaker notes) may not work in an offline “self-contained”
<code>reveal.js</code> slide show.</p>
</dd>
<dt><code>--html-q-tags[=true|false]</code></dt>
<dd>
<p>Use <code>&lt;q&gt;</code> tags for quotes in HTML. (This option only
has an effect if the <code>smart</code> extension is enabled for the
input format used.)</p>
</dd>
<dt><code>--ascii[=true|false]</code></dt>
<dd>
<p>Use only ASCII characters in output. Currently supported for XML and
HTML formats (which use entities instead of UTF-8 when this option is
selected), CommonMark, gfm, and Markdown (which use entities), roff man
and ms (which use hexadecimal escapes), and to a limited degree LaTeX
(which uses standard commands for accented characters when
possible).</p>
</dd>
<dt><code>--reference-links[=true|false]</code></dt>
<dd>
<p>Use reference-style links, rather than inline links, in writing
Markdown or reStructuredText. By default inline links are used. The
placement of link references is affected by the
<code>--reference-location</code> option.</p>
</dd>
<dt><code>--reference-location=block</code>|<code>section</code>|<code>document</code></dt>
<dd>
<p>Specify whether footnotes (and references, if
<code>reference-links</code> is set) are placed at the end of the
current (top-level) block, the current section, or the document. The
default is <code>document</code>. Currently this option only affects the
<code>markdown</code>, <code>muse</code>, <code>html</code>,
<code>epub</code>, <code>slidy</code>, <code>s5</code>,
<code>slideous</code>, <code>dzslides</code>, and <code>revealjs</code>
writers. In slide formats, specifying
<code>--reference-location=section</code> will cause notes to be
rendered at the bottom of a slide.</p>
</dd>
<dt><code>--markdown-headings=setext</code>|<code>atx</code></dt>
<dd>
<p>Specify whether to use ATX-style (<code>#</code>-prefixed) or
Setext-style (underlined) headings for level 1 and 2 headings in
Markdown output. (The default is <code>atx</code>.) ATX-style headings
are always used for levels 3+. This option also affects Markdown cells
in <code>ipynb</code> output.</p>
</dd>
<dt><code>--list-tables[=true|false]</code></dt>
<dd>
<p>Render tables as list tables in RST output.</p>
</dd>
<dt><code>--top-level-division=default</code>|<code>section</code>|<code>chapter</code>|<code>part</code></dt>
<dd>
<p>Treat top-level headings as the given division type in LaTeX,
ConTeXt, DocBook, and TEI output. The hierarchy order is part, chapter,
then section; all headings are shifted such that the top-level heading
becomes the specified type. The default behavior is to determine the
best division type via heuristics: unless other conditions apply,
<code>section</code> is chosen. When the <code>documentclass</code>
variable is set to <code>report</code>, <code>book</code>, or
<code>memoir</code> (unless the <code>article</code> option is
specified), <code>chapter</code> is implied as the setting for this
option. If <code>beamer</code> is the output format, specifying either
<code>chapter</code> or <code>part</code> will cause top-level headings
to become <code>\part{..}</code>, while second-level headings remain as
their default type.</p>
</dd>
<dt><code>-N</code>, <code>--number-sections</code></dt>
<dd>
<p>Number section headings in LaTeX, ConTeXt, HTML, Docx, ms, or EPUB
output. By default, sections are not numbered. Sections with class
<code>unnumbered</code> will never be numbered, even if
<code>--number-sections</code> is specified.</p>
</dd>
<dt><code>--number-offset=</code><em>NUMBER</em>[<code>,</code><em>NUMBER</em><code>,</code><em>…</em>]</dt>
<dd>
<p>Offset for section headings in HTML output (ignored in other output
formats). The first number is added to the section number for top-level
headings, the second for second-level headings, and so on. So, for
example, if you want the first top-level heading in your document to be
numbered “6”, specify <code>--number-offset=5</code>. If your document
starts with a level-2 heading which you want to be numbered “1.5”,
specify <code>--number-offset=1,4</code>. Offsets are 0 by default.
Implies <code>--number-sections</code>.</p>
</dd>
<dt><code>--listings[=true|false]</code></dt>
<dd>
<p>Use the <a
href="https://ctan.org/pkg/listings"><code>listings</code></a> package
for LaTeX code blocks. The package does not support multi-byte encoding
for source code. To handle UTF-8 you would need to use a custom
template. This issue is fully documented here: <a
href="https://en.wikibooks.org/wiki/LaTeX/Source_Code_Listings#Encoding_issue">Encoding
issue with the listings package</a>.</p>
</dd>
<dt><code>-i</code>, <code>--incremental[=true|false]</code></dt>
<dd>
<p>Make list items in slide shows display incrementally (one by one).
The default is for lists to be displayed all at once.</p>
</dd>
<dt><code>--slide-level=</code><em>NUMBER</em></dt>
<dd>
<p>Specifies that headings with the specified level create slides (for
<code>beamer</code>, <code>s5</code>, <code>slidy</code>,
<code>slideous</code>, <code>dzslides</code>). Headings above this level
in the hierarchy are used to divide the slide show into sections;
headings below this level create subheads within a slide. Valid values
are 0-6. If a slide level of 0 is specified, slides will not be split
automatically on headings, and horizontal rules must be used to indicate
slide boundaries. If a slide level is not specified explicitly, the
slide level will be set automatically based on the contents of the
document; see <a href="#structuring-the-slide-show">Structuring the
slide show</a>.</p>
</dd>
<dt><code>--section-divs[=true|false]</code></dt>
<dd>
<p>Wrap sections in <code>&lt;section&gt;</code> tags (or
<code>&lt;div&gt;</code> tags for <code>html4</code>), and attach
identifiers to the enclosing <code>&lt;section&gt;</code> (or
<code>&lt;div&gt;</code>) rather than the heading itself (see <a
href="#heading-identifiers">Heading identifiers</a>, below). This option
only affects HTML output (and does not affect HTML slide formats).</p>
</dd>
<dt><code>--email-obfuscation=none</code>|<code>javascript</code>|<code>references</code></dt>
<dd>
<p>Specify a method for obfuscating <code>mailto:</code> links in HTML
documents. <code>none</code> leaves <code>mailto:</code> links as they
are. <code>javascript</code> obfuscates them using JavaScript.
<code>references</code> obfuscates them by printing their letters as
decimal or hexadecimal character references. The default is
<code>none</code>.</p>
</dd>
<dt><code>--id-prefix=</code><em>STRING</em></dt>
<dd>
<p>Specify a prefix to be added to all identifiers and internal links in
HTML and DocBook output, and to footnote numbers in Markdown and Haddock
output. This is useful for preventing duplicate identifiers when
generating fragments to be included in other pages.</p>
</dd>
<dt><code>-T</code> <em>STRING</em>,
<code>--title-prefix=</code><em>STRING</em></dt>
<dd>
<p>Specify <em>STRING</em> as a prefix at the beginning of the title
that appears in the HTML header (but not in the title as it appears at
the beginning of the HTML body). Implies <code>--standalone</code>.</p>
</dd>
<dt><code>-c</code> <em>URL</em>, <code>--css=</code><em>URL</em></dt>
<dd>
<p>Link to a CSS style sheet. This option can be used repeatedly to
include multiple files. They will be included in the order specified.
This option only affects HTML (including HTML slide shows) and EPUB
output. It should be used together with <code>-s/--standalone</code>,
because the link to the stylesheet goes in the document header.</p>
<p>A stylesheet is required for generating EPUB. If none is provided
using this option (or the <code>css</code> or <code>stylesheet</code>
metadata fields), pandoc will look for a file <code>epub.css</code> in
the user data directory (see <code>--data-dir</code>). If it is not
found there, sensible defaults will be used.</p>
</dd>
<dt><code>--reference-doc=</code><em>FILE</em>|<em>URL</em></dt>
<dd>
<p>Use the specified file as a style reference in producing a docx or
ODT file.</p>
<dl>
<dt>Docx</dt>
<dd>
<p>For best results, the reference docx should be a modified version of
a docx file produced using pandoc. The contents of the reference docx
are ignored, but its stylesheets and document properties (including
margins, page size, header, and footer) are used in the new docx. If no
reference docx is specified on the command line, pandoc will look for a
file <code>reference.docx</code> in the user data directory (see
<code>--data-dir</code>). If this is not found either, sensible defaults
will be used.</p>
<p>To produce a custom <code>reference.docx</code>, first get a copy of
the default <code>reference.docx</code>:
<code>pandoc -o custom-reference.docx --print-default-data-file reference.docx</code>.
Then open <code>custom-reference.docx</code> in Word, modify the styles
as you wish, and save the file. For best results, do not make changes to
this file other than modifying the styles used by pandoc:</p>
<p>Paragraph styles:</p>
<ul>
<li>Normal</li>
<li>Body Text</li>
<li>First Paragraph</li>
<li>Compact</li>
<li>Title</li>
<li>Subtitle</li>
<li>Author</li>
<li>Date</li>
<li>Abstract</li>
<li>AbstractTitle</li>
<li>Bibliography</li>
<li>Heading 1</li>
<li>Heading 2</li>
<li>Heading 3</li>
<li>Heading 4</li>
<li>Heading 5</li>
<li>Heading 6</li>
<li>Heading 7</li>
<li>Heading 8</li>
<li>Heading 9</li>
<li>Block Text</li>
<li>Source Code</li>
<li>Footnote Text</li>
<li>Definition Term</li>
<li>Definition</li>
<li>Caption</li>
<li>Table Caption</li>
<li>Image Caption</li>
<li>Figure</li>
<li>Captioned Figure</li>
<li>TOC Heading</li>
</ul>
<p>Character styles:</p>
<ul>
<li>Default Paragraph Font</li>
<li>Body Text Char</li>
<li>Verbatim Char</li>
<li>Footnote Reference</li>
<li>Hyperlink</li>
<li>Section Number</li>
</ul>
<p>Table style:</p>
<ul>
<li>Table</li>
</ul>
</dd>
<dt>ODT</dt>
<dd>
<p>For best results, the reference ODT should be a modified version of
an ODT produced using pandoc. The contents of the reference ODT are
ignored, but its stylesheets are used in the new ODT. If no reference
ODT is specified on the command line, pandoc will look for a file
<code>reference.odt</code> in the user data directory (see
<code>--data-dir</code>). If this is not found either, sensible defaults
will be used.</p>
<p>To produce a custom <code>reference.odt</code>, first get a copy of
the default <code>reference.odt</code>:
<code>pandoc -o custom-reference.odt --print-default-data-file reference.odt</code>.
Then open <code>custom-reference.odt</code> in LibreOffice, modify the
styles as you wish, and save the file.</p>
</dd>
<dt>PowerPoint</dt>
<dd>
<p>Templates included with Microsoft PowerPoint 2013 (either with
<code>.pptx</code> or <code>.potx</code> extension) are known to work,
as are most templates derived from these.</p>
<p>The specific requirement is that the template should contain layouts
with the following names (as seen within PowerPoint):</p>
<ul>
<li>Title Slide</li>
<li>Title and Content</li>
<li>Section Header</li>
<li>Two Content</li>
<li>Comparison</li>
<li>Content with Caption</li>
<li>Blank</li>
</ul>
<p>For each name, the first layout found with that name will be used. If
no layout is found with one of the names, pandoc will output a warning
and use the layout with that name from the default reference doc
instead. (How these layouts are used is described in <a
href="#powerpoint-layout-choice">PowerPoint layout choice</a>.)</p>
<p>All templates included with a recent version of MS PowerPoint will
fit these criteria. (You can click on <code>Layout</code> under the
<code>Home</code> menu to check.)</p>
<p>You can also modify the default <code>reference.pptx</code>: first
run
<code>pandoc -o custom-reference.pptx --print-default-data-file reference.pptx</code>,
and then modify <code>custom-reference.pptx</code> in MS PowerPoint
(pandoc will use the layouts with the names listed above).</p>
</dd>
</dl>
</dd>
<dt><code>--split-level=</code><em>NUMBER</em></dt>
<dd>
<p>Specify the heading level at which to split an EPUB or chunked HTML
document into separate files. The default is to split into chapters at
level-1 headings. In the case of EPUB, this option only affects the
internal composition of the EPUB, not the way chapters and sections are
displayed to users. Some readers may be slow if the chapter files are
too large, so for large documents with few level-1 headings, one might
want to use a chapter level of 2 or 3. For chunked HTML, this option
determines how much content goes in each “chunk.”</p>
</dd>
<dt><code>--chunk-template=</code><em>PATHTEMPLATE</em></dt>
<dd>
<p>Specify a template for the filenames in a <code>chunkedhtml</code>
document. In the template, <code>%n</code> will be replaced by the chunk
number (padded with leading 0s to 3 digits), <code>%s</code> with the
section number of the chunk, <code>%h</code> with the heading text (with
formatting removed), <code>%i</code> with the section identifier. For
example, <code>%section-%s-%i.html</code> might be resolved to
<code>section-1.1-introduction.html</code>. The characters
<code>/</code> and <code>\</code> are not allowed in chunk templates and
will be ignored. The default is <code>%s-%i.html</code>.</p>
</dd>
<dt><code>--epub-chapter-level=</code><em>NUMBER</em></dt>
<dd>
<p><em>Deprecated synonym for <code>--split-level</code>.</em></p>
</dd>
<dt><code>--epub-cover-image=</code><em>FILE</em></dt>
<dd>
<p>Use the specified image as the EPUB cover. It is recommended that the
image be less than 1000px in width and height. Note that in a Markdown
source document you can also specify <code>cover-image</code> in a YAML
metadata block (see <a href="#epub-metadata">EPUB Metadata</a>,
below).</p>
</dd>
<dt><code>--epub-title-page=true</code>|<code>false</code></dt>
<dd>
<p>Determines whether a the title page is included in the EPUB (default
is <code>true</code>).</p>
</dd>
<dt><code>--epub-metadata=</code><em>FILE</em></dt>
<dd>
<p>Look in the specified XML file for metadata for the EPUB. The file
should contain a series of <a
href="https://www.dublincore.org/specifications/dublin-core/dces/">Dublin
Core elements</a>. For example:</p>
<pre><code> &lt;dc:rights&gt;Creative Commons&lt;/dc:rights&gt;
 &lt;dc:language&gt;es-AR&lt;/dc:language&gt;</code></pre>
<p>By default, pandoc will include the following metadata elements:
<code>&lt;dc:title&gt;</code> (from the document title),
<code>&lt;dc:creator&gt;</code> (from the document authors),
<code>&lt;dc:date&gt;</code> (from the document date, which should be in
<a href="https://www.w3.org/TR/NOTE-datetime">ISO 8601 format</a>),
<code>&lt;dc:language&gt;</code> (from the <code>lang</code> variable,
or, if is not set, the locale), and
<code>&lt;dc:identifier id="BookId"&gt;</code> (a randomly generated
UUID). Any of these may be overridden by elements in the metadata
file.</p>
<p>Note: if the source document is Markdown, a YAML metadata block in
the document can be used instead. See below under <a
href="#epub-metadata">EPUB Metadata</a>.</p>
</dd>
<dt><code>--epub-embed-font=</code><em>FILE</em></dt>
<dd>
<p>Embed the specified font in the EPUB. This option can be repeated to
embed multiple fonts. Wildcards can also be used: for example,
<code>DejaVuSans-*.ttf</code>. However, if you use wildcards on the
command line, be sure to escape them or put the whole filename in single
quotes, to prevent them from being interpreted by the shell. To use the
embedded fonts, you will need to add declarations like the following to
your CSS (see <code>--css</code>):</p>
<pre><code>@font-face {
   font-family: DejaVuSans;
   font-style: normal;
   font-weight: normal;
   src:url(&quot;../fonts/DejaVuSans-Regular.ttf&quot;);
}
@font-face {
   font-family: DejaVuSans;
   font-style: normal;
   font-weight: bold;
   src:url(&quot;../fonts/DejaVuSans-Bold.ttf&quot;);
}
@font-face {
   font-family: DejaVuSans;
   font-style: italic;
   font-weight: normal;
   src:url(&quot;../fonts/DejaVuSans-Oblique.ttf&quot;);
}
@font-face {
   font-family: DejaVuSans;
   font-style: italic;
   font-weight: bold;
   src:url(&quot;../fonts/DejaVuSans-BoldOblique.ttf&quot;);
}
body { font-family: &quot;DejaVuSans&quot;; }</code></pre>
</dd>
<dt><code>--epub-subdirectory=</code><em>DIRNAME</em></dt>
<dd>
<p>Specify the subdirectory in the OCF container that is to hold the
EPUB-specific contents. The default is <code>EPUB</code>. To put the
EPUB contents in the top level, use an empty string.</p>
</dd>
<dt><code>--ipynb-output=all|none|best</code></dt>
<dd>
<p>Determines how ipynb output cells are treated. <code>all</code> means
that all of the data formats included in the original are preserved.
<code>none</code> means that the contents of data cells are omitted.
<code>best</code> causes pandoc to try to pick the richest data block in
each output cell that is compatible with the output format. The default
is <code>best</code>.</p>
</dd>
<dt><code>--pdf-engine=</code><em>PROGRAM</em></dt>
<dd>
<p>Use the specified engine when producing PDF output. Valid values are
<code>pdflatex</code>, <code>lualatex</code>, <code>xelatex</code>,
<code>latexmk</code>, <code>tectonic</code>, <code>wkhtmltopdf</code>,
<code>weasyprint</code>, <code>pagedjs-cli</code>, <code>prince</code>,
<code>context</code>, <code>pdfroff</code>, and <code>typst</code>. If
the engine is not in your PATH, the full path of the engine may be
specified here. If this option is not specified, pandoc uses the
following defaults depending on the output format specified using
<code>-t/--to</code>:</p>
<ul>
<li><code>-t latex</code> or none: <code>pdflatex</code> (other options:
<code>xelatex</code>, <code>lualatex</code>, <code>tectonic</code>,
<code>latexmk</code>)</li>
<li><code>-t context</code>: <code>context</code></li>
<li><code>-t html</code>: <code>wkhtmltopdf</code> (other options:
<code>prince</code>, <code>weasyprint</code>, <code>pagedjs-cli</code>;
see <a href="https://print-css.rocks">print-css.rocks</a> for a good
introduction to PDF generation from HTML/CSS)</li>
<li><code>-t ms</code>: <code>pdfroff</code></li>
<li><code>-t typst</code>: <code>typst</code></li>
</ul>
</dd>
<dt><code>--pdf-engine-opt=</code><em>STRING</em></dt>
<dd>
<p>Use the given string as a command-line argument to the
<code>pdf-engine</code>. For example, to use a persistent directory
<code>foo</code> for <code>latexmk</code>’s auxiliary files, use
<code>--pdf-engine-opt=-outdir=foo</code>. Note that no check for
duplicate options is done.</p>
</dd>
</dl>
<h2 class="options" id="citation-rendering">Citation rendering</h2>
<dl>
<dt><code>-C</code>, <code>--citeproc</code></dt>
<dd>
<p>Process the citations in the file, replacing them with rendered
citations and adding a bibliography. Citation processing will not take
place unless bibliographic data is supplied, either through an external
file specified using the <code>--bibliography</code> option or the
<code>bibliography</code> field in metadata, or via a
<code>references</code> section in metadata containing a list of
citations in CSL YAML format with Markdown formatting. The style is
controlled by a <a
href="https://docs.citationstyles.org/en/stable/specification.html">CSL</a>
stylesheet specified using the <code>--csl</code> option or the
<code>csl</code> field in metadata. (If no stylesheet is specified, the
<code>chicago-author-date</code> style will be used by default.) The
citation processing transformation may be applied before or after
filters or Lua filters (see <code>--filter</code>,
<code>--lua-filter</code>): these transformations are applied in the
order they appear on the command line. For more information, see the
section on <a href="#citations">Citations</a>.</p>
</dd>
<dt><code>--bibliography=</code><em>FILE</em></dt>
<dd>
<p>Set the <code>bibliography</code> field in the document’s metadata to
<em>FILE</em>, overriding any value set in the metadata. If you supply
this argument multiple times, each <em>FILE</em> will be added to
bibliography. If <em>FILE</em> is a URL, it will be fetched via HTTP. If
<em>FILE</em> is not found relative to the working directory, it will be
sought in the resource path (see <code>--resource-path</code>).</p>
</dd>
<dt><code>--csl=</code><em>FILE</em></dt>
<dd>
<p>Set the <code>csl</code> field in the document’s metadata to
<em>FILE</em>, overriding any value set in the metadata. (This is
equivalent to <code>--metadata csl=FILE</code>.) If <em>FILE</em> is a
URL, it will be fetched via HTTP. If <em>FILE</em> is not found relative
to the working directory, it will be sought in the resource path (see
<code>--resource-path</code>) and finally in the <code>csl</code>
subdirectory of the pandoc user data directory.</p>
</dd>
<dt><code>--citation-abbreviations=</code><em>FILE</em></dt>
<dd>
<p>Set the <code>citation-abbreviations</code> field in the document’s
metadata to <em>FILE</em>, overriding any value set in the metadata.
(This is equivalent to
<code>--metadata citation-abbreviations=FILE</code>.) If <em>FILE</em>
is a URL, it will be fetched via HTTP. If <em>FILE</em> is not found
relative to the working directory, it will be sought in the resource
path (see <code>--resource-path</code>) and finally in the
<code>csl</code> subdirectory of the pandoc user data directory.</p>
</dd>
<dt><code>--natbib</code></dt>
<dd>
<p>Use <a href="https://ctan.org/pkg/natbib"><code>natbib</code></a> for
citations in LaTeX output. This option is not for use with the
<code>--citeproc</code> option or with PDF output. It is intended for
use in producing a LaTeX file that can be processed with <a
href="https://ctan.org/pkg/bibtex"><code>bibtex</code></a>.</p>
</dd>
<dt><code>--biblatex</code></dt>
<dd>
<p>Use <a href="https://ctan.org/pkg/biblatex"><code>biblatex</code></a>
for citations in LaTeX output. This option is not for use with the
<code>--citeproc</code> option or with PDF output. It is intended for
use in producing a LaTeX file that can be processed with <a
href="https://ctan.org/pkg/bibtex"><code>bibtex</code></a> or <a
href="https://ctan.org/pkg/biber"><code>biber</code></a>.</p>
</dd>
</dl>
<h2 class="options" id="math-rendering-in-html">Math rendering in
HTML</h2>
<p>The default is to render TeX math as far as possible using Unicode
characters. Formulas are put inside a <code>span</code> with
<code>class="math"</code>, so that they may be styled differently from
the surrounding text if needed. However, this gives acceptable results
only for basic math, usually you will want to use <code>--mathjax</code>
or another of the following options.</p>
<dl>
<dt><code>--mathjax</code>[<code>=</code><em>URL</em>]</dt>
<dd>
<p>Use <a href="https://www.mathjax.org">MathJax</a> to display embedded
TeX math in HTML output. TeX math will be put between
<code>\(...\)</code> (for inline math) or <code>\[...\]</code> (for
display math) and wrapped in <code>&lt;span&gt;</code> tags with class
<code>math</code>. Then the MathJax JavaScript will render it. The
<em>URL</em> should point to the <code>MathJax.js</code> load script. If
a <em>URL</em> is not provided, a link to the Cloudflare CDN will be
inserted.</p>
</dd>
<dt><code>--mathml</code></dt>
<dd>
<p>Convert TeX math to <a href="https://www.w3.org/Math/">MathML</a> (in
<code>epub3</code>, <code>docbook4</code>, <code>docbook5</code>,
<code>jats</code>, <code>html4</code> and <code>html5</code>). This is
the default in <code>odt</code> output. MathML is supported natively by
the main web browsers and select e-book readers.</p>
</dd>
<dt><code>--webtex</code>[<code>=</code><em>URL</em>]</dt>
<dd>
<p>Convert TeX formulas to <code>&lt;img&gt;</code> tags that link to an
external script that converts formulas to images. The formula will be
URL-encoded and concatenated with the URL provided. For SVG images you
can for example use
<code>--webtex https://latex.codecogs.com/svg.latex?</code>. If no URL
is specified, the CodeCogs URL generating PNGs will be used
(<code>https://latex.codecogs.com/png.latex?</code>). Note: the
<code>--webtex</code> option will affect Markdown output as well as
HTML, which is useful if you’re targeting a version of Markdown without
native math support.</p>
</dd>
<dt><code>--katex</code>[<code>=</code><em>URL</em>]</dt>
<dd>
<p>Use <a href="https://github.com/Khan/KaTeX">KaTeX</a> to display
embedded TeX math in HTML output. The <em>URL</em> is the base URL for
the KaTeX library. That directory should contain a
<code>katex.min.js</code> and a <code>katex.min.css</code> file. If a
<em>URL</em> is not provided, a link to the KaTeX CDN will be
inserted.</p>
</dd>
<dt><code>--gladtex</code></dt>
<dd>
<p>Enclose TeX math in <code>&lt;eq&gt;</code> tags in HTML output. The
resulting HTML can then be processed by <a
href="https://humenda.github.io/GladTeX/">GladTeX</a> to produce SVG
images of the typeset formulas and an HTML file with these images
embedded.</p>
<pre><code>pandoc -s --gladtex input.md -o myfile.htex
gladtex -d image_dir myfile.htex
# produces myfile.html and images in image_dir</code></pre>
</dd>
</dl>
<h2 class="options" id="options-for-wrapper-scripts">Options for wrapper
scripts</h2>
<dl>
<dt><code>--dump-args[=true|false]</code></dt>
<dd>
<p>Print information about command-line arguments to <em>stdout</em>,
then exit. This option is intended primarily for use in wrapper scripts.
The first line of output contains the name of the output file specified
with the <code>-o</code> option, or <code>-</code> (for <em>stdout</em>)
if no output file was specified. The remaining lines contain the
command-line arguments, one per line, in the order they appear. These do
not include regular pandoc options and their arguments, but do include
any options appearing after a <code>--</code> separator at the end of
the line.</p>
</dd>
<dt><code>--ignore-args[=true|false]</code></dt>
<dd>
<p>Ignore command-line arguments (for use in wrapper scripts). Regular
pandoc options are not ignored. Thus, for example,</p>
<pre><code>pandoc --ignore-args -o foo.html -s foo.txt -- -e latin1</code></pre>
<p>is equivalent to</p>
<pre><code>pandoc -o foo.html -s</code></pre>
</dd>
</dl>
<h1 id="exit-codes">Exit codes</h1>
<p>If pandoc completes successfully, it will return exit code 0. Nonzero
exit codes have the following meanings:</p>
<table>
<thead>
<tr class="header">
<th style="text-align: right;">Code</th>
<th style="text-align: left;">Error</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: right;">1</td>
<td style="text-align: left;">PandocIOError</td>
</tr>
<tr class="even">
<td style="text-align: right;">3</td>
<td style="text-align: left;">PandocFailOnWarningError</td>
</tr>
<tr class="odd">
<td style="text-align: right;">4</td>
<td style="text-align: left;">PandocAppError</td>
</tr>
<tr class="even">
<td style="text-align: right;">5</td>
<td style="text-align: left;">PandocTemplateError</td>
</tr>
<tr class="odd">
<td style="text-align: right;">6</td>
<td style="text-align: left;">PandocOptionError</td>
</tr>
<tr class="even">
<td style="text-align: right;">21</td>
<td style="text-align: left;">PandocUnknownReaderError</td>
</tr>
<tr class="odd">
<td style="text-align: right;">22</td>
<td style="text-align: left;">PandocUnknownWriterError</td>
</tr>
<tr class="even">
<td style="text-align: right;">23</td>
<td style="text-align: left;">PandocUnsupportedExtensionError</td>
</tr>
<tr class="odd">
<td style="text-align: right;">24</td>
<td style="text-align: left;">PandocCiteprocError</td>
</tr>
<tr class="even">
<td style="text-align: right;">25</td>
<td style="text-align: left;">PandocBibliographyError</td>
</tr>
<tr class="odd">
<td style="text-align: right;">31</td>
<td style="text-align: left;">PandocEpubSubdirectoryError</td>
</tr>
<tr class="even">
<td style="text-align: right;">43</td>
<td style="text-align: left;">PandocPDFError</td>
</tr>
<tr class="odd">
<td style="text-align: right;">44</td>
<td style="text-align: left;">PandocXMLError</td>
</tr>
<tr class="even">
<td style="text-align: right;">47</td>
<td style="text-align: left;">PandocPDFProgramNotFoundError</td>
</tr>
<tr class="odd">
<td style="text-align: right;">61</td>
<td style="text-align: left;">PandocHttpError</td>
</tr>
<tr class="even">
<td style="text-align: right;">62</td>
<td style="text-align: left;">PandocShouldNeverHappenError</td>
</tr>
<tr class="odd">
<td style="text-align: right;">63</td>
<td style="text-align: left;">PandocSomeError</td>
</tr>
<tr class="even">
<td style="text-align: right;">64</td>
<td style="text-align: left;">PandocParseError</td>
</tr>
<tr class="odd">
<td style="text-align: right;">66</td>
<td style="text-align: left;">PandocMakePDFError</td>
</tr>
<tr class="even">
<td style="text-align: right;">67</td>
<td style="text-align: left;">PandocSyntaxMapError</td>
</tr>
<tr class="odd">
<td style="text-align: right;">83</td>
<td style="text-align: left;">PandocFilterError</td>
</tr>
<tr class="even">
<td style="text-align: right;">84</td>
<td style="text-align: left;">PandocLuaError</td>
</tr>
<tr class="odd">
<td style="text-align: right;">89</td>
<td style="text-align: left;">PandocNoScriptingEngine</td>
</tr>
<tr class="even">
<td style="text-align: right;">91</td>
<td style="text-align: left;">PandocMacroLoop</td>
</tr>
<tr class="odd">
<td style="text-align: right;">92</td>
<td style="text-align: left;">PandocUTF8DecodingError</td>
</tr>
<tr class="even">
<td style="text-align: right;">93</td>
<td style="text-align: left;">PandocIpynbDecodingError</td>
</tr>
<tr class="odd">
<td style="text-align: right;">94</td>
<td style="text-align: left;">PandocUnsupportedCharsetError</td>
</tr>
<tr class="even">
<td style="text-align: right;">97</td>
<td style="text-align: left;">PandocCouldNotFindDataFileError</td>
</tr>
<tr class="odd">
<td style="text-align: right;">98</td>
<td style="text-align: left;">PandocCouldNotFindMetadataFileError</td>
</tr>
<tr class="even">
<td style="text-align: right;">99</td>
<td style="text-align: left;">PandocResourceNotFound</td>
</tr>
</tbody>
</table>
<h1 id="defaults-files">Defaults files</h1>
<p>The <code>--defaults</code> option may be used to specify a package
of options, in the form of a YAML file.</p>
<p>Fields that are omitted will just have their regular default values.
So a defaults file can be as simple as one line:</p>
<div class="sourceCode" id="cb18"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb18-1"><a href="#cb18-1" aria-hidden="true" tabindex="-1"></a><span class="fu">verbosity</span><span class="kw">:</span><span class="at"> INFO</span></span></code></pre></div>
<p>In fields that expect a file path (or list of file paths), the
following syntax may be used to interpolate environment variables:</p>
<div class="sourceCode" id="cb19"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb19-1"><a href="#cb19-1" aria-hidden="true" tabindex="-1"></a><span class="fu">csl</span><span class="kw">:</span><span class="at">  ${HOME}/mycsldir/special.csl</span></span></code></pre></div>
<p><code>${USERDATA}</code> may also be used; this will always resolve
to the user data directory that is current when the defaults file is
parsed, regardless of the setting of the environment variable
<code>USERDATA</code>.</p>
<p><code>${.}</code> will resolve to the directory containing the
defaults file itself. This allows you to refer to resources contained in
that directory:</p>
<div class="sourceCode" id="cb20"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb20-1"><a href="#cb20-1" aria-hidden="true" tabindex="-1"></a><span class="fu">epub-cover-image</span><span class="kw">:</span><span class="at"> ${.}/cover.jpg</span></span>
<span id="cb20-2"><a href="#cb20-2" aria-hidden="true" tabindex="-1"></a><span class="fu">epub-metadata</span><span class="kw">:</span><span class="at"> ${.}/meta.xml</span></span>
<span id="cb20-3"><a href="#cb20-3" aria-hidden="true" tabindex="-1"></a><span class="fu">resource-path</span><span class="kw">:</span></span>
<span id="cb20-4"><a href="#cb20-4" aria-hidden="true" tabindex="-1"></a><span class="kw">-</span><span class="at"> .</span><span class="co">             # the working directory from which pandoc is run</span></span>
<span id="cb20-5"><a href="#cb20-5" aria-hidden="true" tabindex="-1"></a><span class="kw">-</span><span class="at"> ${.}/images</span><span class="co">   # the images subdirectory of the directory</span></span>
<span id="cb20-6"><a href="#cb20-6" aria-hidden="true" tabindex="-1"></a><span class="co">                # containing this defaults file</span></span></code></pre></div>
<p>This environment variable interpolation syntax <em>only</em> works in
fields that expect file paths.</p>
<p>Defaults files can be placed in the <code>defaults</code>
subdirectory of the user data directory and used from any directory. For
example, one could create a file specifying defaults for writing
letters, save it as <code>letter.yaml</code> in the
<code>defaults</code> subdirectory of the user data directory, and then
invoke these defaults from any directory using
<code>pandoc --defaults letter</code> or
<code>pandoc -dletter</code>.</p>
<p>When multiple defaults are used, their contents will be combined.</p>
<p>Note that, where command-line arguments may be repeated
(<code>--metadata-file</code>, <code>--css</code>,
<code>--include-in-header</code>, <code>--include-before-body</code>,
<code>--include-after-body</code>, <code>--variable</code>,
<code>--metadata</code>, <code>--syntax-definition</code>), the values
specified on the command line will combine with values specified in the
defaults file, rather than replacing them.</p>
<p>The following tables show the mapping between the command line and
defaults file entries.</p>
<table style="width:99%;">
<colgroup>
<col style="width: 48%" />
<col style="width: 50%" />
</colgroup>
<thead>
<tr class="header">
<th style="text-align: left;">command line</th>
<th style="text-align: left;">defaults file</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: left;"><pre><code>foo.md</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb22"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb22-1"><a href="#cb22-1" aria-hidden="true" tabindex="-1"></a><span class="fu">input-file</span><span class="kw">:</span><span class="at"> foo.md</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>foo.md bar.md

</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb24"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb24-1"><a href="#cb24-1" aria-hidden="true" tabindex="-1"></a><span class="fu">input-files</span><span class="kw">:</span></span>
<span id="cb24-2"><a href="#cb24-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="kw">-</span><span class="at"> foo.md</span></span>
<span id="cb24-3"><a href="#cb24-3" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="kw">-</span><span class="at"> bar.md</span></span></code></pre></div></td>
</tr>
</tbody>
</table>
<p>The value of <code>input-files</code> may be left empty to indicate
input from stdin, and it can be an empty sequence <code>[]</code> for no
input.</p>
<h2 id="general-options-1">General options</h2>
<table style="width:99%;">
<colgroup>
<col style="width: 48%" />
<col style="width: 50%" />
</colgroup>
<thead>
<tr class="header">
<th style="text-align: left;">command line</th>
<th style="text-align: left;">defaults file</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: left;"><pre><code>--from markdown+emoji</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb26"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb26-1"><a href="#cb26-1" aria-hidden="true" tabindex="-1"></a><span class="fu">from</span><span class="kw">:</span><span class="at"> markdown+emoji</span></span></code></pre></div>
<div class="sourceCode" id="cb27"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb27-1"><a href="#cb27-1" aria-hidden="true" tabindex="-1"></a><span class="fu">reader</span><span class="kw">:</span><span class="at"> markdown+emoji</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--to markdown+hard_line_breaks</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb29"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb29-1"><a href="#cb29-1" aria-hidden="true" tabindex="-1"></a><span class="fu">to</span><span class="kw">:</span><span class="at"> markdown+hard_line_breaks</span></span></code></pre></div>
<div class="sourceCode" id="cb30"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb30-1"><a href="#cb30-1" aria-hidden="true" tabindex="-1"></a><span class="fu">writer</span><span class="kw">:</span><span class="at"> markdown+hard_line_breaks</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--output foo.pdf</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb32"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb32-1"><a href="#cb32-1" aria-hidden="true" tabindex="-1"></a><span class="fu">output-file</span><span class="kw">:</span><span class="at"> foo.pdf</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--output -</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb34"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb34-1"><a href="#cb34-1" aria-hidden="true" tabindex="-1"></a><span class="fu">output-file</span><span class="kw">:</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--data-dir dir</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb36"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb36-1"><a href="#cb36-1" aria-hidden="true" tabindex="-1"></a><span class="fu">data-dir</span><span class="kw">:</span><span class="at"> dir</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--defaults file</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb38"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb38-1"><a href="#cb38-1" aria-hidden="true" tabindex="-1"></a><span class="fu">defaults</span><span class="kw">:</span></span>
<span id="cb38-2"><a href="#cb38-2" aria-hidden="true" tabindex="-1"></a><span class="kw">-</span><span class="at"> file</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--verbose</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb40"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb40-1"><a href="#cb40-1" aria-hidden="true" tabindex="-1"></a><span class="fu">verbosity</span><span class="kw">:</span><span class="at"> INFO</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--quiet</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb42"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb42-1"><a href="#cb42-1" aria-hidden="true" tabindex="-1"></a><span class="fu">verbosity</span><span class="kw">:</span><span class="at"> ERROR</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--fail-if-warnings</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb44"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb44-1"><a href="#cb44-1" aria-hidden="true" tabindex="-1"></a><span class="fu">fail-if-warnings</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--sandbox</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb46"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb46-1"><a href="#cb46-1" aria-hidden="true" tabindex="-1"></a><span class="fu">sandbox</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--log=FILE</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb48"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb48-1"><a href="#cb48-1" aria-hidden="true" tabindex="-1"></a><span class="fu">log-file</span><span class="kw">:</span><span class="at"> FILE</span></span></code></pre></div></td>
</tr>
</tbody>
</table>
<p>Options specified in a defaults file itself always have priority over
those in another file included with a <code>defaults:</code> entry.</p>
<p><code>verbosity</code> can have the values <code>ERROR</code>,
<code>WARNING</code>, or <code>INFO</code>.</p>
<h2 id="reader-options-1">Reader options</h2>
<table style="width:99%;">
<colgroup>
<col style="width: 48%" />
<col style="width: 50%" />
</colgroup>
<thead>
<tr class="header">
<th style="text-align: left;">command line</th>
<th style="text-align: left;">defaults file</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: left;"><pre><code>--shift-heading-level-by -1</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb50"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb50-1"><a href="#cb50-1" aria-hidden="true" tabindex="-1"></a><span class="fu">shift-heading-level-by</span><span class="kw">:</span><span class="at"> </span><span class="dv">-1</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--indented-code-classes python
</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb52"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb52-1"><a href="#cb52-1" aria-hidden="true" tabindex="-1"></a><span class="fu">indented-code-classes</span><span class="kw">:</span></span>
<span id="cb52-2"><a href="#cb52-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="kw">-</span><span class="at"> python</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--default-image-extension &quot;.jpg&quot;</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb54"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb54-1"><a href="#cb54-1" aria-hidden="true" tabindex="-1"></a><span class="fu">default-image-extension</span><span class="kw">:</span><span class="at"> </span><span class="st">&#39;.jpg&#39;</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--file-scope</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb56"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb56-1"><a href="#cb56-1" aria-hidden="true" tabindex="-1"></a><span class="fu">file-scope</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--filter pandoc-citeproc \
 --lua-filter count-words.lua \
 --filter special.lua

</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb58"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb58-1"><a href="#cb58-1" aria-hidden="true" tabindex="-1"></a><span class="fu">filters</span><span class="kw">:</span></span>
<span id="cb58-2"><a href="#cb58-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="kw">-</span><span class="at"> pandoc-citeproc</span></span>
<span id="cb58-3"><a href="#cb58-3" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="kw">-</span><span class="at"> count-words.lua</span></span>
<span id="cb58-4"><a href="#cb58-4" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="kw">-</span><span class="at"> </span><span class="fu">type</span><span class="kw">:</span><span class="at"> json</span></span>
<span id="cb58-5"><a href="#cb58-5" aria-hidden="true" tabindex="-1"></a><span class="at">    </span><span class="fu">path</span><span class="kw">:</span><span class="at"> special.lua</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--metadata key=value \
 --metadata key2
</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb60"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb60-1"><a href="#cb60-1" aria-hidden="true" tabindex="-1"></a><span class="fu">metadata</span><span class="kw">:</span></span>
<span id="cb60-2"><a href="#cb60-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="fu">key</span><span class="kw">:</span><span class="at"> value</span></span>
<span id="cb60-3"><a href="#cb60-3" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="fu">key2</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--metadata-file meta.yaml
</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb62"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb62-1"><a href="#cb62-1" aria-hidden="true" tabindex="-1"></a><span class="fu">metadata-files</span><span class="kw">:</span></span>
<span id="cb62-2"><a href="#cb62-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="kw">-</span><span class="at"> meta.yaml</span></span></code></pre></div>
<div class="sourceCode" id="cb63"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb63-1"><a href="#cb63-1" aria-hidden="true" tabindex="-1"></a><span class="fu">metadata-file</span><span class="kw">:</span><span class="at"> meta.yaml</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--preserve-tabs</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb65"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb65-1"><a href="#cb65-1" aria-hidden="true" tabindex="-1"></a><span class="fu">preserve-tabs</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--tab-stop 8</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb67"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb67-1"><a href="#cb67-1" aria-hidden="true" tabindex="-1"></a><span class="fu">tab-stop</span><span class="kw">:</span><span class="at"> </span><span class="dv">8</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--track-changes accept</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb69"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb69-1"><a href="#cb69-1" aria-hidden="true" tabindex="-1"></a><span class="fu">track-changes</span><span class="kw">:</span><span class="at"> accept</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--extract-media dir</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb71"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb71-1"><a href="#cb71-1" aria-hidden="true" tabindex="-1"></a><span class="fu">extract-media</span><span class="kw">:</span><span class="at"> dir</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--abbreviations abbrevs.txt</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb73"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb73-1"><a href="#cb73-1" aria-hidden="true" tabindex="-1"></a><span class="fu">abbreviations</span><span class="kw">:</span><span class="at"> abbrevs.txt</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--trace</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb75"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb75-1"><a href="#cb75-1" aria-hidden="true" tabindex="-1"></a><span class="fu">trace</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
</tbody>
</table>
<p>Metadata values specified in a defaults file are parsed as literal
string text, not Markdown.</p>
<p>Filters will be assumed to be Lua filters if they have the
<code>.lua</code> extension, and JSON filters otherwise. But the filter
type can also be specified explicitly, as shown. Filters are run in the
order specified. To include the built-in citeproc filter, use either
<code>citeproc</code> or <code>{type: citeproc}</code>.</p>
<h2 id="general-writer-options-1">General writer options</h2>
<table style="width:99%;">
<colgroup>
<col style="width: 48%" />
<col style="width: 50%" />
</colgroup>
<thead>
<tr class="header">
<th style="text-align: left;">command line</th>
<th style="text-align: left;">defaults file</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: left;"><pre><code>--standalone</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb77"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb77-1"><a href="#cb77-1" aria-hidden="true" tabindex="-1"></a><span class="fu">standalone</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--template letter</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb79"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb79-1"><a href="#cb79-1" aria-hidden="true" tabindex="-1"></a><span class="fu">template</span><span class="kw">:</span><span class="at"> letter</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--variable key=val \
  --variable key2
</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb81"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb81-1"><a href="#cb81-1" aria-hidden="true" tabindex="-1"></a><span class="fu">variables</span><span class="kw">:</span></span>
<span id="cb81-2"><a href="#cb81-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="fu">key</span><span class="kw">:</span><span class="at"> val</span></span>
<span id="cb81-3"><a href="#cb81-3" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="fu">key2</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--eol nl</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb83"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb83-1"><a href="#cb83-1" aria-hidden="true" tabindex="-1"></a><span class="fu">eol</span><span class="kw">:</span><span class="at"> nl</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--dpi 300</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb85"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb85-1"><a href="#cb85-1" aria-hidden="true" tabindex="-1"></a><span class="fu">dpi</span><span class="kw">:</span><span class="at"> </span><span class="dv">300</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--wrap 60</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb87"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb87-1"><a href="#cb87-1" aria-hidden="true" tabindex="-1"></a><span class="fu">wrap</span><span class="kw">:</span><span class="at"> </span><span class="dv">60</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--columns 72</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb89"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb89-1"><a href="#cb89-1" aria-hidden="true" tabindex="-1"></a><span class="fu">columns</span><span class="kw">:</span><span class="at"> </span><span class="dv">72</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--table-of-contents</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb91"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb91-1"><a href="#cb91-1" aria-hidden="true" tabindex="-1"></a><span class="fu">table-of-contents</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--toc</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb93"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb93-1"><a href="#cb93-1" aria-hidden="true" tabindex="-1"></a><span class="fu">toc</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--toc-depth 3</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb95"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb95-1"><a href="#cb95-1" aria-hidden="true" tabindex="-1"></a><span class="fu">toc-depth</span><span class="kw">:</span><span class="at"> </span><span class="dv">3</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--strip-comments</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb97"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb97-1"><a href="#cb97-1" aria-hidden="true" tabindex="-1"></a><span class="fu">strip-comments</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--no-highlight</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb99"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb99-1"><a href="#cb99-1" aria-hidden="true" tabindex="-1"></a><span class="fu">highlight-style</span><span class="kw">:</span><span class="at"> </span><span class="ch">null</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--highlight-style kate</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb101"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb101-1"><a href="#cb101-1" aria-hidden="true" tabindex="-1"></a><span class="fu">highlight-style</span><span class="kw">:</span><span class="at"> kate</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--syntax-definition mylang.xml
</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb103"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb103-1"><a href="#cb103-1" aria-hidden="true" tabindex="-1"></a><span class="fu">syntax-definitions</span><span class="kw">:</span></span>
<span id="cb103-2"><a href="#cb103-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="kw">-</span><span class="at"> mylang.xml</span></span></code></pre></div>
<div class="sourceCode" id="cb104"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb104-1"><a href="#cb104-1" aria-hidden="true" tabindex="-1"></a><span class="fu">syntax-definition</span><span class="kw">:</span><span class="at"> mylang.xml</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--include-in-header inc.tex
</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb106"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb106-1"><a href="#cb106-1" aria-hidden="true" tabindex="-1"></a><span class="fu">include-in-header</span><span class="kw">:</span></span>
<span id="cb106-2"><a href="#cb106-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="kw">-</span><span class="at"> inc.tex</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--include-before-body inc.tex
</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb108"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb108-1"><a href="#cb108-1" aria-hidden="true" tabindex="-1"></a><span class="fu">include-before-body</span><span class="kw">:</span></span>
<span id="cb108-2"><a href="#cb108-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="kw">-</span><span class="at"> inc.tex</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--include-after-body inc.tex
</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb110"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb110-1"><a href="#cb110-1" aria-hidden="true" tabindex="-1"></a><span class="fu">include-after-body</span><span class="kw">:</span></span>
<span id="cb110-2"><a href="#cb110-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="kw">-</span><span class="at"> inc.tex</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--resource-path .:foo</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb112"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb112-1"><a href="#cb112-1" aria-hidden="true" tabindex="-1"></a><span class="fu">resource-path</span><span class="kw">:</span><span class="at"> </span><span class="kw">[</span><span class="st">&#39;.&#39;</span><span class="kw">,</span><span class="st">&#39;foo&#39;</span><span class="kw">]</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--request-header foo:bar
</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb114"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb114-1"><a href="#cb114-1" aria-hidden="true" tabindex="-1"></a><span class="fu">request-headers</span><span class="kw">:</span></span>
<span id="cb114-2"><a href="#cb114-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="kw">-</span><span class="at"> </span><span class="kw">[</span><span class="st">&quot;User-Agent&quot;</span><span class="kw">,</span><span class="at"> </span><span class="st">&quot;Mozilla/5.0&quot;</span><span class="kw">]</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--no-check-certificate</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb116"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb116-1"><a href="#cb116-1" aria-hidden="true" tabindex="-1"></a><span class="fu">no-check-certificate</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
</tbody>
</table>
<h2 id="options-affecting-specific-writers-1">Options affecting specific
writers</h2>
<table style="width:99%;">
<colgroup>
<col style="width: 48%" />
<col style="width: 50%" />
</colgroup>
<thead>
<tr class="header">
<th style="text-align: left;">command line</th>
<th style="text-align: left;">defaults file</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: left;"><pre><code>--self-contained</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb118"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb118-1"><a href="#cb118-1" aria-hidden="true" tabindex="-1"></a><span class="fu">self-contained</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--html-q-tags</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb120"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb120-1"><a href="#cb120-1" aria-hidden="true" tabindex="-1"></a><span class="fu">html-q-tags</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--ascii</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb122"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb122-1"><a href="#cb122-1" aria-hidden="true" tabindex="-1"></a><span class="fu">ascii</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--reference-links</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb124"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb124-1"><a href="#cb124-1" aria-hidden="true" tabindex="-1"></a><span class="fu">reference-links</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--reference-location block</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb126"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb126-1"><a href="#cb126-1" aria-hidden="true" tabindex="-1"></a><span class="fu">reference-location</span><span class="kw">:</span><span class="at"> block</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--markdown-headings atx</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb128"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb128-1"><a href="#cb128-1" aria-hidden="true" tabindex="-1"></a><span class="fu">markdown-headings</span><span class="kw">:</span><span class="at"> atx</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--list-tables</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb130"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb130-1"><a href="#cb130-1" aria-hidden="true" tabindex="-1"></a><span class="fu">list-tables</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--top-level-division chapter</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb132"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb132-1"><a href="#cb132-1" aria-hidden="true" tabindex="-1"></a><span class="fu">top-level-division</span><span class="kw">:</span><span class="at"> chapter</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--number-sections</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb134"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb134-1"><a href="#cb134-1" aria-hidden="true" tabindex="-1"></a><span class="fu">number-sections</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--number-offset=1,4</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb136"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb136-1"><a href="#cb136-1" aria-hidden="true" tabindex="-1"></a><span class="fu">number-offset</span><span class="kw">:</span><span class="at"> \[1,4\]</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--listings</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb138"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb138-1"><a href="#cb138-1" aria-hidden="true" tabindex="-1"></a><span class="fu">listings</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--incremental</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb140"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb140-1"><a href="#cb140-1" aria-hidden="true" tabindex="-1"></a><span class="fu">incremental</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--slide-level 2</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb142"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb142-1"><a href="#cb142-1" aria-hidden="true" tabindex="-1"></a><span class="fu">slide-level</span><span class="kw">:</span><span class="at"> </span><span class="dv">2</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--section-divs</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb144"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb144-1"><a href="#cb144-1" aria-hidden="true" tabindex="-1"></a><span class="fu">section-divs</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--email-obfuscation references</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb146"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb146-1"><a href="#cb146-1" aria-hidden="true" tabindex="-1"></a><span class="fu">email-obfuscation</span><span class="kw">:</span><span class="at"> references</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--id-prefix ch1</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb148"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb148-1"><a href="#cb148-1" aria-hidden="true" tabindex="-1"></a><span class="fu">identifier-prefix</span><span class="kw">:</span><span class="at"> ch1</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--title-prefix MySite</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb150"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb150-1"><a href="#cb150-1" aria-hidden="true" tabindex="-1"></a><span class="fu">title-prefix</span><span class="kw">:</span><span class="at"> MySite</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--css styles/screen.css  \
  --css styles/special.css
</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb152"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb152-1"><a href="#cb152-1" aria-hidden="true" tabindex="-1"></a><span class="fu">css</span><span class="kw">:</span></span>
<span id="cb152-2"><a href="#cb152-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="kw">-</span><span class="at"> styles/screen.css</span></span>
<span id="cb152-3"><a href="#cb152-3" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="kw">-</span><span class="at"> styles/special.css</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--reference-doc my.docx</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb154"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb154-1"><a href="#cb154-1" aria-hidden="true" tabindex="-1"></a><span class="fu">reference-doc</span><span class="kw">:</span><span class="at"> my.docx</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--epub-cover-image cover.jpg</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb156"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb156-1"><a href="#cb156-1" aria-hidden="true" tabindex="-1"></a><span class="fu">epub-cover-image</span><span class="kw">:</span><span class="at"> cover.jpg</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--epub-title-page=false</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb158"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb158-1"><a href="#cb158-1" aria-hidden="true" tabindex="-1"></a><span class="fu">epub-title-page</span><span class="kw">:</span><span class="at"> </span><span class="ch">false</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--epub-metadata meta.xml</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb160"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb160-1"><a href="#cb160-1" aria-hidden="true" tabindex="-1"></a><span class="fu">epub-metadata</span><span class="kw">:</span><span class="at"> meta.xml</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--epub-embed-font special.otf \
  --epub-embed-font headline.otf
</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb162"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb162-1"><a href="#cb162-1" aria-hidden="true" tabindex="-1"></a><span class="fu">epub-fonts</span><span class="kw">:</span></span>
<span id="cb162-2"><a href="#cb162-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="kw">-</span><span class="at"> special.otf</span></span>
<span id="cb162-3"><a href="#cb162-3" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="kw">-</span><span class="at"> headline.otf</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--split-level 2</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb164"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb164-1"><a href="#cb164-1" aria-hidden="true" tabindex="-1"></a><span class="fu">split-level</span><span class="kw">:</span><span class="at"> </span><span class="dv">2</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--chunk-template=&quot;%i.html&quot;</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb166"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb166-1"><a href="#cb166-1" aria-hidden="true" tabindex="-1"></a><span class="fu">chunk-template</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;%i.html&quot;</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--epub-subdirectory=&quot;&quot;</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb168"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb168-1"><a href="#cb168-1" aria-hidden="true" tabindex="-1"></a><span class="fu">epub-subdirectory</span><span class="kw">:</span><span class="at"> </span><span class="st">&#39;&#39;</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--ipynb-output best</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb170"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb170-1"><a href="#cb170-1" aria-hidden="true" tabindex="-1"></a><span class="fu">ipynb-output</span><span class="kw">:</span><span class="at"> best</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--pdf-engine xelatex</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb172"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb172-1"><a href="#cb172-1" aria-hidden="true" tabindex="-1"></a><span class="fu">pdf-engine</span><span class="kw">:</span><span class="at"> xelatex</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--pdf-engine-opt=--shell-escape
</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb174"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb174-1"><a href="#cb174-1" aria-hidden="true" tabindex="-1"></a><span class="fu">pdf-engine-opts</span><span class="kw">:</span></span>
<span id="cb174-2"><a href="#cb174-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="kw">-</span><span class="at"> </span><span class="st">&#39;-shell-escape&#39;</span></span></code></pre></div>
<div class="sourceCode" id="cb175"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb175-1"><a href="#cb175-1" aria-hidden="true" tabindex="-1"></a><span class="fu">pdf-engine-opt</span><span class="kw">:</span><span class="at"> </span><span class="st">&#39;-shell-escape&#39;</span></span></code></pre></div></td>
</tr>
</tbody>
</table>
<h2 id="citation-rendering-1">Citation rendering</h2>
<table style="width:99%;">
<colgroup>
<col style="width: 48%" />
<col style="width: 50%" />
</colgroup>
<thead>
<tr class="header">
<th style="text-align: left;">command line</th>
<th style="text-align: left;">defaults file</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: left;"><pre><code>--citeproc</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb177"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb177-1"><a href="#cb177-1" aria-hidden="true" tabindex="-1"></a><span class="fu">citeproc</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--bibliography logic.bib
</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb179"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb179-1"><a href="#cb179-1" aria-hidden="true" tabindex="-1"></a><span class="fu">metadata</span><span class="kw">:</span></span>
<span id="cb179-2"><a href="#cb179-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="fu">bibliography</span><span class="kw">:</span><span class="at"> logic.bib</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--csl ieee.csl
</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb181"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb181-1"><a href="#cb181-1" aria-hidden="true" tabindex="-1"></a><span class="fu">metadata</span><span class="kw">:</span></span>
<span id="cb181-2"><a href="#cb181-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="fu">csl</span><span class="kw">:</span><span class="at"> ieee.csl</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--citation-abbreviations ab.json
</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb183"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb183-1"><a href="#cb183-1" aria-hidden="true" tabindex="-1"></a><span class="fu">metadata</span><span class="kw">:</span></span>
<span id="cb183-2"><a href="#cb183-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="fu">citation-abbreviations</span><span class="kw">:</span><span class="at"> ab.json</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--natbib</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb185"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb185-1"><a href="#cb185-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cite-method</span><span class="kw">:</span><span class="at"> natbib</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--biblatex</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb187"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb187-1"><a href="#cb187-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cite-method</span><span class="kw">:</span><span class="at"> biblatex</span></span></code></pre></div></td>
</tr>
</tbody>
</table>
<p><code>cite-method</code> can be <code>citeproc</code>,
<code>natbib</code>, or <code>biblatex</code>. This only affects LaTeX
output. If you want to use citeproc to format citations, you should also
set ‘citeproc: true’.</p>
<p>If you need control over when the citeproc processing is done
relative to other filters, you should instead use <code>citeproc</code>
in the list of <code>filters</code> (see above).</p>
<h2 id="math-rendering-in-html-1">Math rendering in HTML</h2>
<table style="width:99%;">
<colgroup>
<col style="width: 48%" />
<col style="width: 50%" />
</colgroup>
<thead>
<tr class="header">
<th style="text-align: left;">command line</th>
<th style="text-align: left;">defaults file</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: left;"><pre><code>--mathjax
</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb189"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb189-1"><a href="#cb189-1" aria-hidden="true" tabindex="-1"></a><span class="fu">html-math-method</span><span class="kw">:</span></span>
<span id="cb189-2"><a href="#cb189-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="fu">method</span><span class="kw">:</span><span class="at"> mathjax</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--mathml
</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb191"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb191-1"><a href="#cb191-1" aria-hidden="true" tabindex="-1"></a><span class="fu">html-math-method</span><span class="kw">:</span></span>
<span id="cb191-2"><a href="#cb191-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="fu">method</span><span class="kw">:</span><span class="at"> mathml</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--webtex
</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb193"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb193-1"><a href="#cb193-1" aria-hidden="true" tabindex="-1"></a><span class="fu">html-math-method</span><span class="kw">:</span></span>
<span id="cb193-2"><a href="#cb193-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="fu">method</span><span class="kw">:</span><span class="at"> webtex</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--katex
</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb195"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb195-1"><a href="#cb195-1" aria-hidden="true" tabindex="-1"></a><span class="fu">html-math-method</span><span class="kw">:</span></span>
<span id="cb195-2"><a href="#cb195-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="fu">method</span><span class="kw">:</span><span class="at"> katex</span></span></code></pre></div></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><pre><code>--gladtex
</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb197"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb197-1"><a href="#cb197-1" aria-hidden="true" tabindex="-1"></a><span class="fu">html-math-method</span><span class="kw">:</span></span>
<span id="cb197-2"><a href="#cb197-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="fu">method</span><span class="kw">:</span><span class="at"> gladtex</span></span></code></pre></div></td>
</tr>
</tbody>
</table>
<p>In addition to the values listed above, <code>method</code> can have
the value <code>plain</code>.</p>
<p>If the command line option accepts a URL argument, an
<code>url:</code> field can be added to
<code>html-math-method:</code>.</p>
<h2 id="options-for-wrapper-scripts-1">Options for wrapper scripts</h2>
<table style="width:99%;">
<colgroup>
<col style="width: 48%" />
<col style="width: 50%" />
</colgroup>
<thead>
<tr class="header">
<th style="text-align: left;">command line</th>
<th style="text-align: left;">defaults file</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: left;"><pre><code>--dump-args</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb199"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb199-1"><a href="#cb199-1" aria-hidden="true" tabindex="-1"></a><span class="fu">dump-args</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
<tr class="even">
<td style="text-align: left;"><pre><code>--ignore-args</code></pre></td>
<td style="text-align: left;"><div class="sourceCode" id="cb201"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb201-1"><a href="#cb201-1" aria-hidden="true" tabindex="-1"></a><span class="fu">ignore-args</span><span class="kw">:</span><span class="at"> </span><span class="ch">true</span></span></code></pre></div></td>
</tr>
</tbody>
</table>
<h1 id="templates">Templates</h1>
<p>When the <code>-s/--standalone</code> option is used, pandoc uses a
template to add header and footer material that is needed for a
self-standing document. To see the default template that is used, just
type</p>
<pre><code>pandoc -D *FORMAT*</code></pre>
<p>where <em>FORMAT</em> is the name of the output format. A custom
template can be specified using the <code>--template</code> option. You
can also override the system default templates for a given output format
<em>FORMAT</em> by putting a file
<code>templates/default.*FORMAT*</code> in the user data directory (see
<code>--data-dir</code>, above). <em>Exceptions:</em></p>
<ul>
<li>For <code>odt</code> output, customize the
<code>default.opendocument</code> template.</li>
<li>For <code>pdf</code> output, customize the
<code>default.latex</code> template (or the <code>default.context</code>
template, if you use <code>-t context</code>, or the
<code>default.ms</code> template, if you use <code>-t ms</code>, or the
<code>default.html</code> template, if you use
<code>-t html</code>).</li>
<li><code>docx</code> and <code>pptx</code> have no template (however,
you can use <code>--reference-doc</code> to customize the output).</li>
</ul>
<p>Templates contain <em>variables</em>, which allow for the inclusion
of arbitrary information at any point in the file. They may be set at
the command line using the <code>-V/--variable</code> option. If a
variable is not set, pandoc will look for the key in the document’s
metadata, which can be set using either <a
href="#extension-yaml_metadata_block">YAML metadata blocks</a> or with
the <code>-M/--metadata</code> option. In addition, some variables are
given default values by pandoc. See <a href="#variables">Variables</a>
below for a list of variables used in pandoc’s default templates.</p>
<p>If you use custom templates, you may need to revise them as pandoc
changes. We recommend tracking the changes in the default templates, and
modifying your custom templates accordingly. An easy way to do this is
to fork the <a
href="https://github.com/jgm/pandoc-templates">pandoc-templates</a>
repository and merge in changes after each pandoc release.</p>
<h2 id="template-syntax">Template syntax</h2>
<h3 id="comments">Comments</h3>
<p>Anything between the sequence <code>$--</code> and the end of the
line will be treated as a comment and omitted from the output.</p>
<h3 id="delimiters">Delimiters</h3>
<p>To mark variables and control structures in the template, either
<code>$</code>…<code>$</code> or <code>${</code>…<code>}</code> may be
used as delimiters. The styles may also be mixed in the same template,
but the opening and closing delimiter must match in each case. The
opening delimiter may be followed by one or more spaces or tabs, which
will be ignored. The closing delimiter may be preceded by one or more
spaces or tabs, which will be ignored.</p>
<p>To include a literal <code>$</code> in the document, use
<code>$$</code>.</p>
<h3 id="interpolated-variables">Interpolated variables</h3>
<p>A slot for an interpolated variable is a variable name surrounded by
matched delimiters. Variable names must begin with a letter and can
contain letters, numbers, <code>_</code>, <code>-</code>, and
<code>.</code>. The keywords <code>it</code>, <code>if</code>,
<code>else</code>, <code>endif</code>, <code>for</code>,
<code>sep</code>, and <code>endfor</code> may not be used as variable
names. Examples:</p>
<pre><code>$foo$
$foo.bar.baz$
$foo_bar.baz-bim$
$ foo $
${foo}
${foo.bar.baz}
${foo_bar.baz-bim}
${ foo }</code></pre>
<p>Variable names with periods are used to get at structured variable
values. So, for example, <code>employee.salary</code> will return the
value of the <code>salary</code> field of the object that is the value
of the <code>employee</code> field.</p>
<ul>
<li>If the value of the variable is a simple value, it will be rendered
verbatim. (Note that no escaping is done; the assumption is that the
calling program will escape the strings appropriately for the output
format.)</li>
<li>If the value is a list, the values will be concatenated.</li>
<li>If the value is a map, the string <code>true</code> will be
rendered.</li>
<li>Every other value will be rendered as the empty string.</li>
</ul>
<h3 id="conditionals">Conditionals</h3>
<p>A conditional begins with <code>if(variable)</code> (enclosed in
matched delimiters) and ends with <code>endif</code> (enclosed in
matched delimiters). It may optionally contain an <code>else</code>
(enclosed in matched delimiters). The <code>if</code> section is used if
<code>variable</code> has a non-empty value, otherwise the
<code>else</code> section is used (if present). Examples:</p>
<pre><code>$if(foo)$bar$endif$

$if(foo)$
  $foo$
$endif$

$if(foo)$
part one
$else$
part two
$endif$

${if(foo)}bar${endif}

${if(foo)}
  ${foo}
${endif}

${if(foo)}
${ foo.bar }
${else}
no foo!
${endif}</code></pre>
<p>The keyword <code>elseif</code> may be used to simplify complex
nested conditionals:</p>
<pre><code>$if(foo)$
XXX
$elseif(bar)$
YYY
$else$
ZZZ
$endif$</code></pre>
<h3 id="for-loops">For loops</h3>
<p>A for loop begins with <code>for(variable)</code> (enclosed in
matched delimiters) and ends with <code>endfor</code> (enclosed in
matched delimiters).</p>
<ul>
<li>If <code>variable</code> is an array, the material inside the loop
will be evaluated repeatedly, with <code>variable</code> being set to
each value of the array in turn, and concatenated.</li>
<li>If <code>variable</code> is a map, the material inside will be set
to the map.</li>
<li>If the value of the associated variable is not an array or a map, a
single iteration will be performed on its value.</li>
</ul>
<p>Examples:</p>
<pre><code>$for(foo)$$foo$$sep$, $endfor$

$for(foo)$
  - $foo.last$, $foo.first$
$endfor$

${ for(foo.bar) }
  - ${ foo.bar.last }, ${ foo.bar.first }
${ endfor }

$for(mymap)$
$it.name$: $it.office$
$endfor$</code></pre>
<p>You may optionally specify a separator between consecutive values
using <code>sep</code> (enclosed in matched delimiters). The material
between <code>sep</code> and the <code>endfor</code> is the
separator.</p>
<pre><code>${ for(foo) }${ foo }${ sep }, ${ endfor }</code></pre>
<p>Instead of using <code>variable</code> inside the loop, the special
anaphoric keyword <code>it</code> may be used.</p>
<pre><code>${ for(foo.bar) }
  - ${ it.last }, ${ it.first }
${ endfor }</code></pre>
<h3 id="partials">Partials</h3>
<p>Partials (subtemplates stored in different files) may be included by
using the name of the partial, followed by <code>()</code>, for
example:</p>
<pre><code>${ styles() }</code></pre>
<p>Partials will be sought in the directory containing the main
template. The file name will be assumed to have the same extension as
the main template if it lacks an extension. When calling the partial,
the full name including file extension can also be used:</p>
<pre><code>${ styles.html() }</code></pre>
<p>(If a partial is not found in the directory of the template and the
template path is given as a relative path, it will also be sought in the
<code>templates</code> subdirectory of the user data directory.)</p>
<p>Partials may optionally be applied to variables using a colon:</p>
<pre><code>${ date:fancy() }

${ articles:bibentry() }</code></pre>
<p>If <code>articles</code> is an array, this will iterate over its
values, applying the partial <code>bibentry()</code> to each one. So the
second example above is equivalent to</p>
<pre><code>${ for(articles) }
${ it:bibentry() }
${ endfor }</code></pre>
<p>Note that the anaphoric keyword <code>it</code> must be used when
iterating over partials. In the above examples, the
<code>bibentry</code> partial should contain <code>it.title</code> (and
so on) instead of <code>articles.title</code>.</p>
<p>Final newlines are omitted from included partials.</p>
<p>Partials may include other partials.</p>
<p>A separator between values of an array may be specified in square
brackets, immediately after the variable name or partial:</p>
<pre><code>${months[, ]}$

${articles:bibentry()[; ]$</code></pre>
<p>The separator in this case is literal and (unlike with
<code>sep</code> in an explicit <code>for</code> loop) cannot contain
interpolated variables or other template directives.</p>
<h3 id="nesting">Nesting</h3>
<p>To ensure that content is “nested,” that is, subsequent lines
indented, use the <code>^</code> directive:</p>
<pre><code>$item.number$  $^$$item.description$ ($item.price$)</code></pre>
<p>In this example, if <code>item.description</code> has multiple lines,
they will all be indented to line up with the first line:</p>
<pre><code>00123  A fine bottle of 18-year old
       Oban whiskey. ($148)</code></pre>
<p>To nest multiple lines to the same level, align them with the
<code>^</code> directive in the template. For example:</p>
<pre><code>$item.number$  $^$$item.description$ ($item.price$)
               (Available til $item.sellby$.)</code></pre>
<p>will produce</p>
<pre><code>00123  A fine bottle of 18-year old
       Oban whiskey. ($148)
       (Available til March 30, 2020.)</code></pre>
<p>If a variable occurs by itself on a line, preceded by whitespace and
not followed by further text or directives on the same line, and the
variable’s value contains multiple lines, it will be nested
automatically.</p>
<h3 id="breakable-spaces">Breakable spaces</h3>
<p>Normally, spaces in the template itself (as opposed to values of the
interpolated variables) are not breakable, but they can be made
breakable in part of the template by using the <code>~</code> keyword
(ended with another <code>~</code>).</p>
<pre><code>$~$This long line may break if the document is rendered
with a short line length.$~$</code></pre>
<h3 id="pipes">Pipes</h3>
<p>A pipe transforms the value of a variable or partial. Pipes are
specified using a slash (<code>/</code>) between the variable name (or
partial) and the pipe name. Example:</p>
<pre><code>$for(name)$
$name/uppercase$
$endfor$

$for(metadata/pairs)$
- $it.key$: $it.value$
$endfor$

$employee:name()/uppercase$</code></pre>
<p>Pipes may be chained:</p>
<pre><code>$for(employees/pairs)$
$it.key/alpha/uppercase$. $it.name$
$endfor$</code></pre>
<p>Some pipes take parameters:</p>
<pre><code>|----------------------|------------|
$for(employee)$
$it.name.first/uppercase/left 20 &quot;| &quot;$$it.name.salary/right 10 &quot; | &quot; &quot; |&quot;$
$endfor$
|----------------------|------------|</code></pre>
<p>Currently the following pipes are predefined:</p>
<ul>
<li><p><code>pairs</code>: Converts a map or array to an array of maps,
each with <code>key</code> and <code>value</code> fields. If the
original value was an array, the <code>key</code> will be the array
index, starting with 1.</p></li>
<li><p><code>uppercase</code>: Converts text to uppercase.</p></li>
<li><p><code>lowercase</code>: Converts text to lowercase.</p></li>
<li><p><code>length</code>: Returns the length of the value: number of
characters for a textual value, number of elements for a map or
array.</p></li>
<li><p><code>reverse</code>: Reverses a textual value or array, and has
no effect on other values.</p></li>
<li><p><code>first</code>: Returns the first value of an array, if
applied to a non-empty array; otherwise returns the original
value.</p></li>
<li><p><code>last</code>: Returns the last value of an array, if applied
to a non-empty array; otherwise returns the original value.</p></li>
<li><p><code>rest</code>: Returns all but the first value of an array,
if applied to a non-empty array; otherwise returns the original
value.</p></li>
<li><p><code>allbutlast</code>: Returns all but the last value of an
array, if applied to a non-empty array; otherwise returns the original
value.</p></li>
<li><p><code>chomp</code>: Removes trailing newlines (and breakable
space).</p></li>
<li><p><code>nowrap</code>: Disables line wrapping on breakable
spaces.</p></li>
<li><p><code>alpha</code>: Converts textual values that can be read as
an integer into lowercase alphabetic characters <code>a..z</code> (mod
26). This can be used to get lettered enumeration from array indices. To
get uppercase letters, chain with <code>uppercase</code>.</p></li>
<li><p><code>roman</code>: Converts textual values that can be read as
an integer into lowercase roman numerals. This can be used to get
lettered enumeration from array indices. To get uppercase roman, chain
with <code>uppercase</code>.</p></li>
<li><p><code>left n "leftborder" "rightborder"</code>: Renders a textual
value in a block of width <code>n</code>, aligned to the left, with an
optional left and right border. Has no effect on other values. This can
be used to align material in tables. Widths are positive integers
indicating the number of characters. Borders are strings inside double
quotes; literal <code>"</code> and <code>\</code> characters must be
backslash-escaped.</p></li>
<li><p><code>right n "leftborder" "rightborder"</code>: Renders a
textual value in a block of width <code>n</code>, aligned to the right,
and has no effect on other values.</p></li>
<li><p><code>center n "leftborder" "rightborder"</code>: Renders a
textual value in a block of width <code>n</code>, aligned to the center,
and has no effect on other values.</p></li>
</ul>
<h2 id="variables">Variables</h2>
<h3 id="metadata-variables">Metadata variables</h3>
<dl>
<dt><code>title</code>, <code>author</code>, <code>date</code></dt>
<dd>
<p>allow identification of basic aspects of the document. Included in
PDF metadata through LaTeX and ConTeXt. These can be set through a <a
href="#extension-pandoc_title_block">pandoc title block</a>, which
allows for multiple authors, or through a <a
href="#extension-yaml_metadata_block">YAML metadata block</a>:</p>
<pre><code>---
author:
- Aristotle
- Peter Abelard
...</code></pre>
<p>Note that if you just want to set PDF or HTML metadata, without
including a title block in the document itself, you can set the
<code>title-meta</code>, <code>author-meta</code>, and
<code>date-meta</code> variables. (By default these are set
automatically, based on <code>title</code>, <code>author</code>, and
<code>date</code>.) The page title in HTML is set by
<code>pagetitle</code>, which is equal to <code>title</code> by
default.</p>
</dd>
<dt><code>subtitle</code></dt>
<dd>
document subtitle, included in HTML, EPUB, LaTeX, ConTeXt, and docx
documents
</dd>
<dt><code>abstract</code></dt>
<dd>
document summary, included in HTML, LaTeX, ConTeXt, AsciiDoc, and docx
documents
</dd>
<dt><code>abstract-title</code></dt>
<dd>
title of abstract, currently used only in HTML, EPUB, and docx. This
will be set automatically to a localized value, depending on
<code>lang</code>, but can be manually overridden.
</dd>
<dt><code>keywords</code></dt>
<dd>
list of keywords to be included in HTML, PDF, ODT, pptx, docx and
AsciiDoc metadata; repeat as for <code>author</code>, above
</dd>
<dt><code>subject</code></dt>
<dd>
document subject, included in ODT, PDF, docx, EPUB, and pptx metadata
</dd>
<dt><code>description</code></dt>
<dd>
document description, included in ODT, docx and pptx metadata. Some
applications show this as <code>Comments</code> metadata.
</dd>
<dt><code>category</code></dt>
<dd>
document category, included in docx and pptx metadata
</dd>
</dl>
<p>Additionally, any root-level string metadata, not included in ODT,
docx or pptx metadata is added as a <em>custom property</em>. The
following <a href="https://yaml.org/spec/1.2/spec.html"
title="YAML v1.2 Spec">YAML</a> metadata block for instance:</p>
<pre><code>---
title:  &#39;This is the title&#39;
subtitle: &quot;This is the subtitle&quot;
author:
- Author One
- Author Two
description: |
    This is a long
    description.

    It consists of two paragraphs
...</code></pre>
<p>will include <code>title</code>, <code>author</code> and
<code>description</code> as standard document properties and
<code>subtitle</code> as a custom property when converting to docx, ODT
or pptx.</p>
<h3 id="language-variables">Language variables</h3>
<dl>
<dt><code>lang</code></dt>
<dd>
<p>identifies the main language of the document using IETF language tags
(following the <a href="https://tools.ietf.org/html/bcp47">BCP 47</a>
standard), such as <code>en</code> or <code>en-GB</code>. The <a
href="https://r12a.github.io/app-subtags/">Language subtag lookup</a>
tool can look up or verify these tags. This affects most formats, and
controls hyphenation in PDF output when using LaTeX (through <a
href="https://ctan.org/pkg/babel"><code>babel</code></a> and <a
href="https://ctan.org/pkg/polyglossia"><code>polyglossia</code></a>) or
ConTeXt.</p>
<p>Use native pandoc <a href="#divs-and-spans">Divs and Spans</a> with
the <code>lang</code> attribute to switch the language:</p>
<pre><code>---
lang: en-GB
...

Text in the main document language (British English).

::: {lang=fr-CA}
&gt; Cette citation est écrite en français canadien.
:::

More text in English. [&#39;Zitat auf Deutsch.&#39;]{lang=de}</code></pre>
</dd>
<dt><code>dir</code></dt>
<dd>
<p>the base script direction, either <code>rtl</code> (right-to-left) or
<code>ltr</code> (left-to-right).</p>
<p>For bidirectional documents, native pandoc <code>span</code>s and
<code>div</code>s with the <code>dir</code> attribute (value
<code>rtl</code> or <code>ltr</code>) can be used to override the base
direction in some output formats. This may not always be necessary if
the final renderer (e.g. the browser, when generating HTML) supports the
<a
href="https://www.w3.org/International/articles/inline-bidi-markup/uba-basics">Unicode
Bidirectional Algorithm</a>.</p>
<p>When using LaTeX for bidirectional documents, only the
<code>xelatex</code> engine is fully supported (use
<code>--pdf-engine=xelatex</code>).</p>
</dd>
</dl>
<h3 id="variables-for-html">Variables for HTML</h3>
<dl>
<dt><code>document-css</code></dt>
<dd>
Enables inclusion of most of the <a
href="https://developer.mozilla.org/en-US/docs/Learn/CSS">CSS</a> in the
<code>styles.html</code> <a href="#partials">partial</a> (have a look
with
<code>pandoc --print-default-data-file=templates/styles.html</code>).
Unless you use <a href="#option--css"><code>--css</code></a>, this
variable is set to <code>true</code> by default. You can disable it with
e.g. <code>pandoc -M document-css=false</code>.
</dd>
<dt><code>mainfont</code></dt>
<dd>
sets the CSS <code>font-family</code> property on the <code>html</code>
element.
</dd>
<dt><code>fontsize</code></dt>
<dd>
sets the base CSS <code>font-size</code>, which you’d usually set to
e.g. <code>20px</code>, but it also accepts <code>pt</code> (12pt = 16px
in most browsers).
</dd>
<dt><code>fontcolor</code></dt>
<dd>
sets the CSS <code>color</code> property on the <code>html</code>
element.
</dd>
<dt><code>linkcolor</code></dt>
<dd>
sets the CSS <code>color</code> property on all links.
</dd>
<dt><code>monofont</code></dt>
<dd>
sets the CSS <code>font-family</code> property on <code>code</code>
elements.
</dd>
<dt><code>monobackgroundcolor</code></dt>
<dd>
sets the CSS <code>background-color</code> property on <code>code</code>
elements and adds extra padding.
</dd>
<dt><code>linestretch</code></dt>
<dd>
sets the CSS <code>line-height</code> property on the <code>html</code>
element, which is preferred to be unitless.
</dd>
<dt><code>maxwidth</code></dt>
<dd>
sets the CSS <code>max-width</code> property (default is 32em).
</dd>
<dt><code>backgroundcolor</code></dt>
<dd>
sets the CSS <code>background-color</code> property on the
<code>html</code> element.
</dd>
<dt><code>margin-left</code>, <code>margin-right</code>,
<code>margin-top</code>, <code>margin-bottom</code></dt>
<dd>
sets the corresponding CSS <code>padding</code> properties on the
<code>body</code> element.
</dd>
</dl>
<p>To override or extend some <a
href="https://developer.mozilla.org/en-US/docs/Learn/CSS">CSS</a> for
just one document, include for example:</p>
<pre><code>---
header-includes: |
  &lt;style&gt;
  blockquote {
    font-style: italic;
  }
  tr.even {
    background-color: #f0f0f0;
  }
  td, th {
    padding: 0.5em 2em 0.5em 0.5em;
  }
  tbody {
    border-bottom: none;
  }
  &lt;/style&gt;
---</code></pre>
<h3 id="variables-for-html-math">Variables for HTML math</h3>
<dl>
<dt><code>classoption</code></dt>
<dd>
when using <a href="#option--katex">KaTeX</a>, you can render display
math equations flush left using <a href="#layout">YAML metadata</a> or
with <code>-M classoption=fleqn</code>.
</dd>
</dl>
<h3 id="variables-for-html-slides">Variables for HTML slides</h3>
<p>These affect HTML output when <a href="#slide-shows">producing slide
shows with pandoc</a>.</p>
<dl>
<dt><code>institute</code></dt>
<dd>
author affiliations: can be a list when there are multiple authors
</dd>
<dt><code>revealjs-url</code></dt>
<dd>
base URL for reveal.js documents (defaults to
<code>https://unpkg.com/reveal.js@^4/</code>)
</dd>
<dt><code>s5-url</code></dt>
<dd>
base URL for S5 documents (defaults to <code>s5/default</code>)
</dd>
<dt><code>slidy-url</code></dt>
<dd>
base URL for Slidy documents (defaults to
<code>https://www.w3.org/Talks/Tools/Slidy2</code>)
</dd>
<dt><code>slideous-url</code></dt>
<dd>
base URL for Slideous documents (defaults to <code>slideous</code>)
</dd>
<dt><code>title-slide-attributes</code></dt>
<dd>
additional attributes for the title slide of reveal.js slide shows. See
<a href="#background-in-reveal.js-beamer-and-pptx">background in
reveal.js, beamer, and pptx</a> for an example.
</dd>
</dl>
<p>All <a href="https://revealjs.com/config/">reveal.js configuration
options</a> are available as variables. To turn off boolean flags that
default to true in reveal.js, use <code>0</code>.</p>
<h3 id="variables-for-beamer-slides">Variables for Beamer slides</h3>
<p>These variables change the appearance of PDF slides using <a
href="https://ctan.org/pkg/beamer"><code>beamer</code></a>.</p>
<dl>
<dt><code>aspectratio</code></dt>
<dd>
slide aspect ratio (<code>43</code> for 4:3 [default], <code>169</code>
for 16:9, <code>1610</code> for 16:10, <code>149</code> for 14:9,
<code>141</code> for 1.41:1, <code>54</code> for 5:4, <code>32</code>
for 3:2)
</dd>
<dt><code>beameroption</code></dt>
<dd>
add extra beamer option with <code>\setbeameroption{}</code>
</dd>
<dt><code>institute</code></dt>
<dd>
author affiliations: can be a list when there are multiple authors
</dd>
<dt><code>logo</code></dt>
<dd>
logo image for slides
</dd>
<dt><code>navigation</code></dt>
<dd>
controls navigation symbols (default is <code>empty</code> for no
navigation symbols; other valid values are <code>frame</code>,
<code>vertical</code>, and <code>horizontal</code>)
</dd>
<dt><code>section-titles</code></dt>
<dd>
enables “title pages” for new sections (default is true)
</dd>
<dt><code>theme</code>, <code>colortheme</code>, <code>fonttheme</code>,
<code>innertheme</code>, <code>outertheme</code></dt>
<dd>
beamer themes
</dd>
<dt><code>themeoptions</code></dt>
<dd>
options for LaTeX beamer themes (a list).
</dd>
<dt><code>titlegraphic</code></dt>
<dd>
image for title slide
</dd>
</dl>
<h3 id="variables-for-powerpoint">Variables for PowerPoint</h3>
<p>These variables control the visual aspects of a slide show that are
not easily controlled via templates.</p>
<dl>
<dt><code>monofont</code></dt>
<dd>
font to use for code.
</dd>
</dl>
<h3 id="variables-for-latex">Variables for LaTeX</h3>
<p>Pandoc uses these variables when <a href="#creating-a-pdf">creating a
PDF</a> with a LaTeX engine.</p>
<h4 id="layout">Layout</h4>
<dl>
<dt><code>block-headings</code></dt>
<dd>
<p>make <code>\paragraph</code> and <code>\subparagraph</code> (fourth-
and fifth-level headings, or fifth- and sixth-level with book classes)
free-standing rather than run-in; requires further formatting to
distinguish from <code>\subsubsection</code> (third- or fourth-level
headings). Instead of using this option, <a
href="https://ctan.org/pkg/koma-script">KOMA-Script</a> can adjust
headings more extensively:</p>
<pre><code>---
documentclass: scrartcl
header-includes: |
  \RedeclareSectionCommand[
    beforeskip=-10pt plus -2pt minus -1pt,
    afterskip=1sp plus -1sp minus 1sp,
    font=\normalfont\itshape]{paragraph}
  \RedeclareSectionCommand[
    beforeskip=-10pt plus -2pt minus -1pt,
    afterskip=1sp plus -1sp minus 1sp,
    font=\normalfont\scshape,
    indent=0pt]{subparagraph}
...</code></pre>
</dd>
<dt><code>classoption</code></dt>
<dd>
<p>option for document class, e.g. <code>oneside</code>; repeat for
multiple options:</p>
<pre><code>---
classoption:
- twocolumn
- landscape
...</code></pre>
</dd>
<dt><code>documentclass</code></dt>
<dd>
document class: usually one of the standard classes, <a
href="https://ctan.org/pkg/article"><code>article</code></a>, <a
href="https://ctan.org/pkg/book"><code>book</code></a>, and <a
href="https://ctan.org/pkg/report"><code>report</code></a>; the <a
href="https://ctan.org/pkg/koma-script">KOMA-Script</a> equivalents,
<code>scrartcl</code>, <code>scrbook</code>, and <code>scrreprt</code>,
which default to smaller margins; or <a
href="https://ctan.org/pkg/memoir"><code>memoir</code></a>
</dd>
<dt><code>geometry</code></dt>
<dd>
<p>option for <a
href="https://ctan.org/pkg/geometry"><code>geometry</code></a> package,
e.g. <code>margin=1in</code>; repeat for multiple options:</p>
<pre><code>---
geometry:
- top=30mm
- left=20mm
- heightrounded
...</code></pre>
</dd>
<dt><code>hyperrefoptions</code></dt>
<dd>
<p>option for <a
href="https://ctan.org/pkg/hyperref"><code>hyperref</code></a> package,
e.g. <code>linktoc=all</code>; repeat for multiple options:</p>
<pre><code>---
hyperrefoptions:
- linktoc=all
- pdfwindowui
- pdfpagemode=FullScreen
...</code></pre>
</dd>
<dt><code>indent</code></dt>
<dd>
if true, pandoc will use document class settings for indentation (the
default LaTeX template otherwise removes indentation and adds space
between paragraphs)
</dd>
<dt><code>linestretch</code></dt>
<dd>
adjusts line spacing using the <a
href="https://ctan.org/pkg/setspace"><code>setspace</code></a> package,
e.g. <code>1.25</code>, <code>1.5</code>
</dd>
<dt><code>margin-left</code>, <code>margin-right</code>,
<code>margin-top</code>, <code>margin-bottom</code></dt>
<dd>
sets margins if <code>geometry</code> is not used (otherwise
<code>geometry</code> overrides these)
</dd>
<dt><code>pagestyle</code></dt>
<dd>
control <code>\pagestyle{}</code>: the default article class supports
<code>plain</code> (default), <code>empty</code> (no running heads or
page numbers), and <code>headings</code> (section titles in running
heads)
</dd>
<dt><code>papersize</code></dt>
<dd>
paper size, e.g. <code>letter</code>, <code>a4</code>
</dd>
<dt><code>secnumdepth</code></dt>
<dd>
numbering depth for sections (with <code>--number-sections</code> option
or <code>numbersections</code> variable)
</dd>
<dt><code>beamerarticle</code></dt>
<dd>
produce an article from Beamer slides
</dd>
</dl>
<h4 id="fonts">Fonts</h4>
<dl>
<dt><code>fontenc</code></dt>
<dd>
allows font encoding to be specified through <code>fontenc</code>
package (with <code>pdflatex</code>); default is <code>T1</code> (see <a
href="https://ctan.org/pkg/encguide">LaTeX font encodings guide</a>)
</dd>
<dt><code>fontfamily</code></dt>
<dd>
font package for use with <code>pdflatex</code>: <a
href="https://www.tug.org/texlive/">TeX Live</a> includes many options,
documented in the <a href="https://tug.org/FontCatalogue/">LaTeX Font
Catalogue</a>. The default is <a href="https://ctan.org/pkg/lm">Latin
Modern</a>.
</dd>
<dt><code>fontfamilyoptions</code></dt>
<dd>
<p>options for package used as <code>fontfamily</code>; repeat for
multiple options. For example, to use the Libertine font with
proportional lowercase (old-style) figures through the <a
href="https://ctan.org/pkg/libertinus"><code>libertinus</code></a>
package:</p>
<pre><code>---
fontfamily: libertinus
fontfamilyoptions:
- osf
- p
...</code></pre>
</dd>
<dt><code>fontsize</code></dt>
<dd>
font size for body text. The standard classes allow 10pt, 11pt, and
12pt. To use another size, set <code>documentclass</code> to one of the
<a href="https://ctan.org/pkg/koma-script">KOMA-Script</a> classes, such
as <code>scrartcl</code> or <code>scrbook</code>.
</dd>
<dt><code>mainfont</code>, <code>sansfont</code>, <code>monofont</code>,
<code>mathfont</code>, <code>CJKmainfont</code>,
<code>CJKsansfont</code>, <code>CJKmonofont</code></dt>
<dd>
font families for use with <code>xelatex</code> or
<code>lualatex</code>: take the name of any system font, using the <a
href="https://ctan.org/pkg/fontspec"><code>fontspec</code></a> package.
<code>CJKmainfont</code> uses the <a
href="https://ctan.org/pkg/xecjk"><code>xecjk</code></a> package.
</dd>
<dt><code>mainfontoptions</code>, <code>sansfontoptions</code>,
<code>monofontoptions</code>, <code>mathfontoptions</code>,
<code>CJKoptions</code></dt>
<dd>
<p>options to use with <code>mainfont</code>, <code>sansfont</code>,
<code>monofont</code>, <code>mathfont</code>, <code>CJKmainfont</code>
in <code>xelatex</code> and <code>lualatex</code>. Allow for any choices
available through <a
href="https://ctan.org/pkg/fontspec"><code>fontspec</code></a>; repeat
for multiple options. For example, to use the <a
href="http://www.gust.org.pl/projects/e-foundry/tex-gyre">TeX Gyre</a>
version of Palatino with lowercase figures:</p>
<pre><code>---
mainfont: TeX Gyre Pagella
mainfontoptions:
- Numbers=Lowercase
- Numbers=Proportional
...</code></pre>
</dd>
<dt><code>babelfonts</code></dt>
<dd>
<p>a map of Babel language names (e.g. <code>chinese</code>) to the font
to be used with the language:</p>
<hr />
<p>babelfonts: chinese-hant: “Noto Serif CJK TC” russian: “Noto Serif”
…</p>
</dd>
<dt><code>microtypeoptions</code></dt>
<dd>
options to pass to the microtype package
</dd>
</dl>
<h4 id="links">Links</h4>
<dl>
<dt><code>colorlinks</code></dt>
<dd>
add color to link text; automatically enabled if any of
<code>linkcolor</code>, <code>filecolor</code>, <code>citecolor</code>,
<code>urlcolor</code>, or <code>toccolor</code> are set
</dd>
<dt><code>boxlinks</code></dt>
<dd>
add visible box around links (has no effect if <code>colorlinks</code>
is set)
</dd>
<dt><code>linkcolor</code>, <code>filecolor</code>,
<code>citecolor</code>, <code>urlcolor</code>,
<code>toccolor</code></dt>
<dd>
color for internal links, external links, citation links, linked URLs,
and links in table of contents, respectively: uses options allowed by <a
href="https://ctan.org/pkg/xcolor"><code>xcolor</code></a>, including
the <code>dvipsnames</code>, <code>svgnames</code>, and
<code>x11names</code> lists
</dd>
<dt><code>links-as-notes</code></dt>
<dd>
causes links to be printed as footnotes
</dd>
<dt><code>urlstyle</code></dt>
<dd>
style for URLs (e.g., <code>tt</code>, <code>rm</code>, <code>sf</code>,
and, the default, <code>same</code>)
</dd>
</dl>
<h4 id="front-matter">Front matter</h4>
<dl>
<dt><code>lof</code>, <code>lot</code></dt>
<dd>
include list of figures, list of tables
</dd>
<dt><code>thanks</code></dt>
<dd>
contents of acknowledgments footnote after document title
</dd>
<dt><code>toc</code></dt>
<dd>
include table of contents (can also be set using
<code>--toc/--table-of-contents</code>)
</dd>
<dt><code>toc-depth</code></dt>
<dd>
level of section to include in table of contents
</dd>
</dl>
<h4 id="biblatex-bibliographies">BibLaTeX Bibliographies</h4>
<p>These variables function when using BibLaTeX for <a
href="#citation-rendering">citation rendering</a>.</p>
<dl>
<dt><code>biblatexoptions</code></dt>
<dd>
list of options for biblatex
</dd>
<dt><code>biblio-style</code></dt>
<dd>
bibliography style, when used with <code>--natbib</code> and
<code>--biblatex</code>
</dd>
<dt><code>biblio-title</code></dt>
<dd>
bibliography title, when used with <code>--natbib</code> and
<code>--biblatex</code>
</dd>
<dt><code>bibliography</code></dt>
<dd>
bibliography to use for resolving references
</dd>
<dt><code>natbiboptions</code></dt>
<dd>
list of options for natbib
</dd>
</dl>
<h3 id="variables-for-context">Variables for ConTeXt</h3>
<p>Pandoc uses these variables when <a href="#creating-a-pdf">creating a
PDF</a> with ConTeXt.</p>
<dl>
<dt><code>fontsize</code></dt>
<dd>
font size for body text (e.g. <code>10pt</code>, <code>12pt</code>)
</dd>
<dt><code>headertext</code>, <code>footertext</code></dt>
<dd>
text to be placed in running header or footer (see <a
href="https://wiki.contextgarden.net/Headers_and_Footers">ConTeXt
Headers and Footers</a>); repeat up to four times for different
placement
</dd>
<dt><code>indenting</code></dt>
<dd>
controls indentation of paragraphs, e.g. <code>yes,small,next</code>
(see <a href="https://wiki.contextgarden.net/Indentation">ConTeXt
Indentation</a>); repeat for multiple options
</dd>
<dt><code>interlinespace</code></dt>
<dd>
adjusts line spacing, e.g. <code>4ex</code> (using <a
href="https://wiki.contextgarden.net/Command/setupinterlinespace"><code>setupinterlinespace</code></a>);
repeat for multiple options
</dd>
<dt><code>layout</code></dt>
<dd>
options for page margins and text arrangement (see <a
href="https://wiki.contextgarden.net/Layout">ConTeXt Layout</a>); repeat
for multiple options
</dd>
<dt><code>linkcolor</code>, <code>contrastcolor</code></dt>
<dd>
color for links outside and inside a page, e.g. <code>red</code>,
<code>blue</code> (see <a
href="https://wiki.contextgarden.net/Color">ConTeXt Color</a>)
</dd>
<dt><code>linkstyle</code></dt>
<dd>
typeface style for links, e.g. <code>normal</code>, <code>bold</code>,
<code>slanted</code>, <code>boldslanted</code>, <code>type</code>,
<code>cap</code>, <code>small</code>
</dd>
<dt><code>lof</code>, <code>lot</code></dt>
<dd>
include list of figures, list of tables
</dd>
<dt><code>mainfont</code>, <code>sansfont</code>, <code>monofont</code>,
<code>mathfont</code></dt>
<dd>
font families: take the name of any system font (see <a
href="https://wiki.contextgarden.net/Font_Switching">ConTeXt Font
Switching</a>)
</dd>
<dt><code>margin-left</code>, <code>margin-right</code>,
<code>margin-top</code>, <code>margin-bottom</code></dt>
<dd>
sets margins, if <code>layout</code> is not used (otherwise
<code>layout</code> overrides these)
</dd>
<dt><code>pagenumbering</code></dt>
<dd>
page number style and location (using <a
href="https://wiki.contextgarden.net/Command/setuppagenumbering"><code>setuppagenumbering</code></a>);
repeat for multiple options
</dd>
<dt><code>papersize</code></dt>
<dd>
paper size, e.g. <code>letter</code>, <code>A4</code>,
<code>landscape</code> (see <a
href="https://wiki.contextgarden.net/PaperSetup">ConTeXt Paper
Setup</a>); repeat for multiple options
</dd>
<dt><code>pdfa</code></dt>
<dd>
adds to the preamble the setup necessary to generate PDF/A of the type
specified, e.g. <code>1a:2005</code>, <code>2a</code>. If no type is
specified (i.e. the value is set to True, by e.g.
<code>--metadata=pdfa</code> or <code>pdfa: true</code> in a YAML
metadata block), <code>1b:2005</code> will be used as default, for
reasons of backwards compatibility. Using <code>--variable=pdfa</code>
without specified value is not supported. To successfully generate PDF/A
the required ICC color profiles have to be available and the content and
all included files (such as images) have to be standard-conforming. The
ICC profiles and output intent may be specified using the variables
<code>pdfaiccprofile</code> and <code>pdfaintent</code>. See also <a
href="https://wiki.contextgarden.net/PDF/A">ConTeXt PDFA</a> for more
details.
</dd>
<dt><code>pdfaiccprofile</code></dt>
<dd>
when used in conjunction with <code>pdfa</code>, specifies the ICC
profile to use in the PDF, e.g. <code>default.cmyk</code>. If left
unspecified, <code>sRGB.icc</code> is used as default. May be repeated
to include multiple profiles. Note that the profiles have to be
available on the system. They can be obtained from <a
href="https://wiki.contextgarden.net/PDFX#ICC_profiles">ConTeXt ICC
Profiles</a>.
</dd>
<dt><code>pdfaintent</code></dt>
<dd>
when used in conjunction with <code>pdfa</code>, specifies the output
intent for the colors,
e.g. <code>ISO coated v2 300\letterpercent\space (ECI)</code> If left
unspecified, <code>sRGB IEC61966-2.1</code> is used as default.
</dd>
<dt><code>toc</code></dt>
<dd>
include table of contents (can also be set using
<code>--toc/--table-of-contents</code>)
</dd>
<dt><code>urlstyle</code></dt>
<dd>
typeface style for links without link text, e.g. <code>normal</code>,
<code>bold</code>, <code>slanted</code>, <code>boldslanted</code>,
<code>type</code>, <code>cap</code>, <code>small</code>
</dd>
<dt><code>whitespace</code></dt>
<dd>
spacing between paragraphs, e.g. <code>none</code>, <code>small</code>
(using <a
href="https://wiki.contextgarden.net/Command/setupwhitespace"><code>setupwhitespace</code></a>)
</dd>
<dt><code>includesource</code></dt>
<dd>
include all source documents as file attachments in the PDF file
</dd>
</dl>
<h3 id="variables-for-wkhtmltopdf">Variables for
<code>wkhtmltopdf</code></h3>
<p>Pandoc uses these variables when <a href="#creating-a-pdf">creating a
PDF</a> with <a
href="https://wkhtmltopdf.org"><code>wkhtmltopdf</code></a>. The
<code>--css</code> option also affects the output.</p>
<dl>
<dt><code>footer-html</code>, <code>header-html</code></dt>
<dd>
add information to the header and footer
</dd>
<dt><code>margin-left</code>, <code>margin-right</code>,
<code>margin-top</code>, <code>margin-bottom</code></dt>
<dd>
set the page margins
</dd>
<dt><code>papersize</code></dt>
<dd>
sets the PDF paper size
</dd>
</dl>
<h3 id="variables-for-man-pages">Variables for man pages</h3>
<dl>
<dt><code>adjusting</code></dt>
<dd>
adjusts text to left (<code>l</code>), right (<code>r</code>), center
(<code>c</code>), or both (<code>b</code>) margins
</dd>
<dt><code>footer</code></dt>
<dd>
footer in man pages
</dd>
<dt><code>header</code></dt>
<dd>
header in man pages
</dd>
<dt><code>section</code></dt>
<dd>
section number in man pages
</dd>
</dl>
<h3 id="variables-for-typst">Variables for Typst</h3>
<dl>
<dt><code>margin</code></dt>
<dd>
A dictionary with the fields defined in the Typst documentation:
<code>x</code>, <code>y</code>, <code>top</code>, <code>bottom</code>,
<code>left</code>, <code>right</code>.
</dd>
<dt><code>papersize</code></dt>
<dd>
Paper size: <code>a4</code>, <code>us-letter</code>, etc.
</dd>
<dt><code>mainfont</code></dt>
<dd>
Name of system font to use for the main font.
</dd>
<dt><code>fontsize</code></dt>
<dd>
Font size (e.g., <code>12pt</code>).
</dd>
<dt><code>section-numbering</code></dt>
<dd>
Schema to use for numbering sections, e.g. <code>1.A.1</code>.
</dd>
<dt><code>columns</code></dt>
<dd>
Number of columns for body text.
</dd>
</dl>
<h3 id="variables-for-ms">Variables for ms</h3>
<dl>
<dt><code>fontfamily</code></dt>
<dd>
<code>A</code> (Avant Garde), <code>B</code> (Bookman), <code>C</code>
(Helvetica), <code>HN</code> (Helvetica Narrow), <code>P</code>
(Palatino), or <code>T</code> (Times New Roman). This setting does not
affect source code, which is always displayed using monospace Courier.
These built-in fonts are limited in their coverage of characters.
Additional fonts may be installed using the script <a
href="https://www.schaffter.ca/mom/bin/install-font.sh"><code>install-font.sh</code></a>
provided by Peter Schaffter and documented in detail on <a
href="https://www.schaffter.ca/mom/momdoc/appendices.html#steps">his web
site</a>.
</dd>
<dt><code>indent</code></dt>
<dd>
paragraph indent (e.g. <code>2m</code>)
</dd>
<dt><code>lineheight</code></dt>
<dd>
line height (e.g. <code>12p</code>)
</dd>
<dt><code>pointsize</code></dt>
<dd>
point size (e.g. <code>10p</code>)
</dd>
</dl>
<h3 id="variables-set-automatically">Variables set automatically</h3>
<p>Pandoc sets these variables automatically in response to <a
href="#options">options</a> or document contents; users can also modify
them. These vary depending on the output format, and include the
following:</p>
<dl>
<dt><code>body</code></dt>
<dd>
body of document
</dd>
<dt><code>date-meta</code></dt>
<dd>
the <code>date</code> variable converted to ISO 8601 YYYY-MM-DD,
included in all HTML based formats (dzslides, epub, html, html4, html5,
revealjs, s5, slideous, slidy). The recognized formats for
<code>date</code> are: <code>mm/dd/yyyy</code>, <code>mm/dd/yy</code>,
<code>yyyy-mm-dd</code> (ISO 8601), <code>dd MM yyyy</code> (e.g. either
<code>02 Apr 2018</code> or <code>02 April 2018</code>),
<code>MM dd, yyyy</code> (e.g. <code>Apr. 02, 2018</code> or
<code>April 02, 2018),</code>yyyy[mm[dd]]<code>(e.g.</code>20180402,
<code>201804</code> or <code>2018</code>).
</dd>
<dt><code>header-includes</code></dt>
<dd>
contents specified by <code>-H/--include-in-header</code> (may have
multiple values)
</dd>
<dt><code>include-before</code></dt>
<dd>
contents specified by <code>-B/--include-before-body</code> (may have
multiple values)
</dd>
<dt><code>include-after</code></dt>
<dd>
contents specified by <code>-A/--include-after-body</code> (may have
multiple values)
</dd>
<dt><code>meta-json</code></dt>
<dd>
JSON representation of all of the document’s metadata. Field values are
transformed to the selected output format.
</dd>
<dt><code>numbersections</code></dt>
<dd>
non-null value if <code>-N/--number-sections</code> was specified
</dd>
<dt><code>sourcefile</code>, <code>outputfile</code></dt>
<dd>
<p>source and destination filenames, as given on the command line.
<code>sourcefile</code> can also be a list if input comes from multiple
files, or empty if input is from stdin. You can use the following
snippet in your template to distinguish them:</p>
<pre><code>$if(sourcefile)$
$for(sourcefile)$
$sourcefile$
$endfor$
$else$
(stdin)
$endif$</code></pre>
<p>Similarly, <code>outputfile</code> can be <code>-</code> if output
goes to the terminal.</p>
<p>If you need absolute paths, use
e.g. <code>$curdir$/$sourcefile$</code>.</p>
</dd>
<dt><code>curdir</code></dt>
<dd>
working directory from which pandoc is run.
</dd>
<dt><code>pandoc-version</code></dt>
<dd>
pandoc version.
</dd>
<dt><code>toc</code></dt>
<dd>
non-null value if <code>--toc/--table-of-contents</code> was specified
</dd>
<dt><code>toc-title</code></dt>
<dd>
title of table of contents (works only with EPUB, HTML, revealjs,
opendocument, odt, docx, pptx, beamer, LaTeX)
</dd>
</dl>
<h1 id="extensions">Extensions</h1>
<p>The behavior of some of the readers and writers can be adjusted by
enabling or disabling various extensions.</p>
<p>An extension can be enabled by adding <code>+EXTENSION</code> to the
format name and disabled by adding <code>-EXTENSION</code>. For example,
<code>--from markdown_strict+footnotes</code> is strict Markdown with
footnotes enabled, while
<code>--from markdown-footnotes-pipe_tables</code> is pandoc’s Markdown
without footnotes or pipe tables.</p>
<p>The markdown reader and writer make by far the most use of
extensions. Extensions only used by them are therefore covered in the
section <a href="#pandocs-markdown">Pandoc’s Markdown</a> below (see <a
href="#markdown-variants">Markdown variants</a> for
<code>commonmark</code> and <code>gfm</code>). In the following,
extensions that also work for other formats are covered.</p>
<p>Note that markdown extensions added to the <code>ipynb</code> format
affect Markdown cells in Jupyter notebooks (as do command-line options
like <code>--markdown-headings</code>).</p>
<h2 id="typography">Typography</h2>
<h4 id="extension-smart">Extension: <code>smart</code></h4>
<p>Interpret straight quotes as curly quotes, <code>---</code> as
em-dashes, <code>--</code> as en-dashes, and <code>...</code> as
ellipses. Nonbreaking spaces are inserted after certain abbreviations,
such as “Mr.”</p>
<p>This extension can be enabled/disabled for the following formats:</p>
<dl>
<dt>input formats</dt>
<dd>
<code>markdown</code>, <code>commonmark</code>, <code>latex</code>,
<code>mediawiki</code>, <code>org</code>, <code>rst</code>,
<code>twiki</code>, <code>html</code>
</dd>
<dt>output formats</dt>
<dd>
<code>markdown</code>, <code>latex</code>, <code>context</code>,
<code>rst</code>
</dd>
<dt>enabled by default in</dt>
<dd>
<code>markdown</code>, <code>latex</code>, <code>context</code> (both
input and output)
</dd>
</dl>
<p>Note: If you are <em>writing</em> Markdown, then the
<code>smart</code> extension has the reverse effect: what would have
been curly quotes comes out straight.</p>
<p>In LaTeX, <code>smart</code> means to use the standard TeX ligatures
for quotation marks (<code>``</code> and <code>''</code> for double
quotes, <code>`</code> and <code>'</code> for single quotes) and dashes
(<code>--</code> for en-dash and <code>---</code> for em-dash). If
<code>smart</code> is disabled, then in reading LaTeX pandoc will parse
these characters literally. In writing LaTeX, enabling
<code>smart</code> tells pandoc to use the ligatures when possible; if
<code>smart</code> is disabled pandoc will use unicode quotation mark
and dash characters.</p>
<h2 id="headings-and-sections">Headings and sections</h2>
<h4 id="extension-auto_identifiers">Extension:
<code>auto_identifiers</code></h4>
<p>A heading without an explicitly specified identifier will be
automatically assigned a unique identifier based on the heading
text.</p>
<p>This extension can be enabled/disabled for the following formats:</p>
<dl>
<dt>input formats</dt>
<dd>
<code>markdown</code>, <code>latex</code>, <code>rst</code>,
<code>mediawiki</code>, <code>textile</code>
</dd>
<dt>output formats</dt>
<dd>
<code>markdown</code>, <code>muse</code>
</dd>
<dt>enabled by default in</dt>
<dd>
<code>markdown</code>, <code>muse</code>
</dd>
</dl>
<p>The default algorithm used to derive the identifier from the heading
text is:</p>
<ul>
<li>Remove all formatting, links, etc.</li>
<li>Remove all footnotes.</li>
<li>Remove all non-alphanumeric characters, except underscores, hyphens,
and periods.</li>
<li>Replace all spaces and newlines with hyphens.</li>
<li>Convert all alphabetic characters to lowercase.</li>
<li>Remove everything up to the first letter (identifiers may not begin
with a number or punctuation mark).</li>
<li>If nothing is left after this, use the identifier
<code>section</code>.</li>
</ul>
<p>Thus, for example,</p>
<table>
<thead>
<tr class="header">
<th style="text-align: left;">Heading</th>
<th style="text-align: left;">Identifier</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td
style="text-align: left;"><code>Heading identifiers in HTML</code></td>
<td
style="text-align: left;"><code>heading-identifiers-in-html</code></td>
</tr>
<tr class="even">
<td style="text-align: left;"><code>Maître d'hôtel</code></td>
<td style="text-align: left;"><code>maître-dhôtel</code></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><code>*Dogs*?--in *my* house?</code></td>
<td style="text-align: left;"><code>dogs--in-my-house</code></td>
</tr>
<tr class="even">
<td style="text-align: left;"><code>[HTML], [S5], or [RTF]?</code></td>
<td style="text-align: left;"><code>html-s5-or-rtf</code></td>
</tr>
<tr class="odd">
<td style="text-align: left;"><code>3. Applications</code></td>
<td style="text-align: left;"><code>applications</code></td>
</tr>
<tr class="even">
<td style="text-align: left;"><code>33</code></td>
<td style="text-align: left;"><code>section</code></td>
</tr>
</tbody>
</table>
<p>These rules should, in most cases, allow one to determine the
identifier from the heading text. The exception is when several headings
have the same text; in this case, the first will get an identifier as
described above; the second will get the same identifier with
<code>-1</code> appended; the third with <code>-2</code>; and so on.</p>
<p>(However, a different algorithm is used if
<code>gfm_auto_identifiers</code> is enabled; see below.)</p>
<p>These identifiers are used to provide link targets in the table of
contents generated by the <code>--toc|--table-of-contents</code> option.
They also make it easy to provide links from one section of a document
to another. A link to this section, for example, might look like
this:</p>
<pre><code>See the section on
[heading identifiers](#heading-identifiers-in-html-latex-and-context).</code></pre>
<p>Note, however, that this method of providing links to sections works
only in HTML, LaTeX, and ConTeXt formats.</p>
<p>If the <code>--section-divs</code> option is specified, then each
section will be wrapped in a <code>section</code> (or a
<code>div</code>, if <code>html4</code> was specified), and the
identifier will be attached to the enclosing
<code>&lt;section&gt;</code> (or <code>&lt;div&gt;</code>) tag rather
than the heading itself. This allows entire sections to be manipulated
using JavaScript or treated differently in CSS.</p>
<h4 id="extension-ascii_identifiers">Extension:
<code>ascii_identifiers</code></h4>
<p>Causes the identifiers produced by <code>auto_identifiers</code> to
be pure ASCII. Accents are stripped off of accented Latin letters, and
non-Latin letters are omitted.</p>
<h4 id="extension-gfm_auto_identifiers">Extension:
<code>gfm_auto_identifiers</code></h4>
<p>Changes the algorithm used by <code>auto_identifiers</code> to
conform to GitHub’s method. Spaces are converted to dashes
(<code>-</code>), uppercase characters to lowercase characters, and
punctuation characters other than <code>-</code> and <code>_</code> are
removed. Emojis are replaced by their names.</p>
<h2 id="math-input">Math Input</h2>
<p>The extensions <a
href="#extension-tex_math_dollars"><code>tex_math_dollars</code></a>, <a
href="#extension-tex_math_single_backslash"><code>tex_math_single_backslash</code></a>,
and <a
href="#extension-tex_math_double_backslash"><code>tex_math_double_backslash</code></a>
are described in the section about Pandoc’s Markdown.</p>
<p>However, they can also be used with HTML input. This is handy for
reading web pages formatted using MathJax, for example.</p>
<h2 id="raw-htmltex">Raw HTML/TeX</h2>
<p>The following extensions are described in more detail in their
respective sections of <a href="#pandocs-markdown">Pandoc’s
Markdown</a>:</p>
<ul>
<li><p><a href="#extension-raw_html"><code>raw_html</code></a> allows
HTML elements which are not representable in pandoc’s AST to be parsed
as raw HTML. By default, this is disabled for HTML input.</p></li>
<li><p><a href="#extension-raw_tex"><code>raw_tex</code></a> allows raw
LaTeX, TeX, and ConTeXt to be included in a document. This extension can
be enabled/disabled for the following formats (in addition to
<code>markdown</code>):</p>
<dl>
<dt>input formats</dt>
<dd>
<code>latex</code>, <code>textile</code>, <code>html</code>
(environments, <code>\ref</code>, and <code>\eqref</code> only),
<code>ipynb</code>
</dd>
<dt>output formats</dt>
<dd>
<code>textile</code>, <code>commonmark</code>
</dd>
</dl>
<p>Note: as applied to <code>ipynb</code>, <code>raw_html</code> and
<code>raw_tex</code> affect not only raw TeX in markdown cells, but data
with mime type <code>text/html</code> in output cells. Since the
<code>ipynb</code> reader attempts to preserve the richest possible
outputs when several options are given, you will get best results if you
disable <code>raw_html</code> and <code>raw_tex</code> when converting
to formats like <code>docx</code> which don’t allow raw
<code>html</code> or <code>tex</code>.</p></li>
<li><p><a href="#extension-native_divs"><code>native_divs</code></a>
causes HTML <code>div</code> elements to be parsed as native pandoc Div
blocks. If you want them to be parsed as raw HTML, use
<code>-f html-native_divs+raw_html</code>.</p></li>
<li><p><a href="#extension-native_spans"><code>native_spans</code></a>
causes HTML <code>span</code> elements to be parsed as native pandoc
Span inlines. If you want them to be parsed as raw HTML, use
<code>-f html-native_spans+raw_html</code>. If you want to drop all
<code>div</code>s and <code>span</code>s when converting HTML to
Markdown, you can use
<code>pandoc -f html-native_divs-native_spans -t markdown</code>.</p></li>
</ul>
<h2 id="literate-haskell-support">Literate Haskell support</h2>
<h4 id="extension-literate_haskell">Extension:
<code>literate_haskell</code></h4>
<p>Treat the document as literate Haskell source.</p>
<p>This extension can be enabled/disabled for the following formats:</p>
<dl>
<dt>input formats</dt>
<dd>
<code>markdown</code>, <code>rst</code>, <code>latex</code>
</dd>
<dt>output formats</dt>
<dd>
<code>markdown</code>, <code>rst</code>, <code>latex</code>,
<code>html</code>
</dd>
</dl>
<p>If you append <code>+lhs</code> (or <code>+literate_haskell</code>)
to one of the formats above, pandoc will treat the document as literate
Haskell source. This means that</p>
<ul>
<li><p>In Markdown input, “bird track” sections will be parsed as
Haskell code rather than block quotations. Text between
<code>\begin{code}</code> and <code>\end{code}</code> will also be
treated as Haskell code. For ATX-style headings the character ‘=’ will
be used instead of ‘#’.</p></li>
<li><p>In Markdown output, code blocks with classes <code>haskell</code>
and <code>literate</code> will be rendered using bird tracks, and block
quotations will be indented one space, so they will not be treated as
Haskell code. In addition, headings will be rendered setext-style (with
underlines) rather than ATX-style (with ‘#’ characters). (This is
because ghc treats ‘#’ characters in column 1 as introducing line
numbers.)</p></li>
<li><p>In restructured text input, “bird track” sections will be parsed
as Haskell code.</p></li>
<li><p>In restructured text output, code blocks with class
<code>haskell</code> will be rendered using bird tracks.</p></li>
<li><p>In LaTeX input, text in <code>code</code> environments will be
parsed as Haskell code.</p></li>
<li><p>In LaTeX output, code blocks with class <code>haskell</code> will
be rendered inside <code>code</code> environments.</p></li>
<li><p>In HTML output, code blocks with class <code>haskell</code> will
be rendered with class <code>literatehaskell</code> and bird
tracks.</p></li>
</ul>
<p>Examples:</p>
<pre><code>pandoc -f markdown+lhs -t html</code></pre>
<p>reads literate Haskell source formatted with Markdown conventions and
writes ordinary HTML (without bird tracks).</p>
<pre><code>pandoc -f markdown+lhs -t html+lhs</code></pre>
<p>writes HTML with the Haskell code in bird tracks, so it can be copied
and pasted as literate Haskell source.</p>
<p>Note that GHC expects the bird tracks in the first column, so
indented literate code blocks (e.g. inside an itemized environment) will
not be picked up by the Haskell compiler.</p>
<h2 id="other-extensions">Other extensions</h2>
<h4 id="extension-empty_paragraphs">Extension:
<code>empty_paragraphs</code></h4>
<p>Allows empty paragraphs. By default empty paragraphs are omitted.</p>
<p>This extension can be enabled/disabled for the following formats:</p>
<dl>
<dt>input formats</dt>
<dd>
<code>docx</code>, <code>html</code>
</dd>
<dt>output formats</dt>
<dd>
<code>docx</code>, <code>odt</code>, <code>opendocument</code>,
<code>html</code>
</dd>
</dl>
<h4 id="extension-native_numbering">Extension:
<code>native_numbering</code></h4>
<p>Enables native numbering of figures and tables. Enumeration starts at
1.</p>
<p>This extension can be enabled/disabled for the following formats:</p>
<dl>
<dt>output formats</dt>
<dd>
<code>odt</code>, <code>opendocument</code>, <code>docx</code>
</dd>
</dl>
<h4 id="extension-xrefs_name">Extension: <code>xrefs_name</code></h4>
<p>Links to headings, figures and tables inside the document are
substituted with cross-references that will use the name or caption of
the referenced item. The original link text is replaced once the
generated document is refreshed. This extension can be combined with
<code>xrefs_number</code> in which case numbers will appear before the
name.</p>
<p>Text in cross-references is only made consistent with the referenced
item once the document has been refreshed.</p>
<p>This extension can be enabled/disabled for the following formats:</p>
<dl>
<dt>output formats</dt>
<dd>
<code>odt</code>, <code>opendocument</code>
</dd>
</dl>
<h4 id="extension-xrefs_number">Extension:
<code>xrefs_number</code></h4>
<p>Links to headings, figures and tables inside the document are
substituted with cross-references that will use the number of the
referenced item. The original link text is discarded. This extension can
be combined with <code>xrefs_name</code> in which case the name or
caption numbers will appear after the number.</p>
<p>For the <code>xrefs_number</code> to be useful heading numbers must
be enabled in the generated document, also table and figure captions
must be enabled using for example the <code>native_numbering</code>
extension.</p>
<p>Numbers in cross-references are only visible in the final document
once it has been refreshed.</p>
<p>This extension can be enabled/disabled for the following formats:</p>
<dl>
<dt>output formats</dt>
<dd>
<code>odt</code>, <code>opendocument</code>
</dd>
</dl>
<h4 id="ext-styles">Extension: <code>styles</code></h4>
<p>When converting from docx, read all docx styles as divs (for
paragraph styles) and spans (for character styles) regardless of whether
pandoc understands the meaning of these styles. This can be used with <a
href="#custom-styles">docx custom styles</a>. Disabled by default.</p>
<dl>
<dt>input formats</dt>
<dd>
<code>docx</code>
</dd>
</dl>
<h4 id="extension-amuse">Extension: <code>amuse</code></h4>
<p>In the <code>muse</code> input format, this enables Text::Amuse
extensions to Emacs Muse markup.</p>
<h4 id="extension-raw_markdown">Extension:
<code>raw_markdown</code></h4>
<p>In the <code>ipynb</code> input format, this causes Markdown cells to
be included as raw Markdown blocks (allowing lossless round-tripping)
rather than being parsed. Use this only when you are targeting
<code>ipynb</code> or a markdown-based output format.</p>
<h4 id="org-citations">Extension: <code>citations</code></h4>
<p>When the <code>citations</code> extension is enabled in
<code>org</code>, org-cite and org-ref style citations will be parsed as
native pandoc citations.</p>
<p>When <code>citations</code> is enabled in <code>docx</code>,
citations inserted by Zotero or Mendeley or EndNote plugins will be
parsed as native pandoc citations. (Otherwise, the formatted citations
generated by the bibliographic software will be parsed as regular
text.)</p>
<h4 id="org-fancy-lists">Extension: <code>fancy_lists</code></h4>
<p>Some aspects of <a href="#extension-fancy_lists">Pandoc’s Markdown
fancy lists</a> are also accepted in <code>org</code> input, mimicking
the option <code>org-list-allow-alphabetical</code> in Emacs. As in Org
Mode, enabling this extension allows lowercase and uppercase
alphabetical markers for ordered lists to be parsed in addition to
arabic ones. Note that for Org, this does not include roman numerals or
the <code>#</code> placeholder that are enabled by the extension in
Pandoc’s Markdown.</p>
<h4 id="extension-element_citations">Extension:
<code>element_citations</code></h4>
<p>In the <code>jats</code> output formats, this causes reference items
to be replaced with <code>&lt;element-citation&gt;</code> elements.
These elements are not influenced by CSL styles, but all information on
the item is included in tags.</p>
<h4 id="extension-ntb">Extension: <code>ntb</code></h4>
<p>In the <code>context</code> output format this enables the use of <a
href="https://wiki.contextgarden.net/TABLE">Natural Tables (TABLE)</a>
instead of the default <a
href="https://wiki.contextgarden.net/xtables">Extreme Tables
(xtables)</a>. Natural tables allow more fine-grained global
customization but come at a performance penalty compared to extreme
tables.</p>
<h4 id="extension-tagging">Extension: <code>tagging</code></h4>
<p>Enabling this extension with <code>context</code> output will produce
markup suitable for the production of tagged PDFs. This includes
additional markers for paragraphs and alternative markup for emphasized
text. The <code>emphasis-command</code> template variable is set if the
extension is enabled.</p>
<h1 id="pandocs-markdown">Pandoc’s Markdown</h1>
<p>Pandoc understands an extended and slightly revised version of John
Gruber’s <a
href="https://daringfireball.net/projects/markdown/">Markdown</a>
syntax. This document explains the syntax, noting differences from
original Markdown. Except where noted, these differences can be
suppressed by using the <code>markdown_strict</code> format instead of
<code>markdown</code>. Extensions can be enabled or disabled to specify
the behavior more granularly. They are described in the following. See
also <a href="#extensions">Extensions</a> above, for extensions that
work also on other formats.</p>
<h2 id="philosophy">Philosophy</h2>
<p>Markdown is designed to be easy to write, and, even more importantly,
easy to read:</p>
<blockquote>
<p>A Markdown-formatted document should be publishable as-is, as plain
text, without looking like it’s been marked up with tags or formatting
instructions. – <a
href="https://daringfireball.net/projects/markdown/syntax#philosophy">John
Gruber</a></p>
</blockquote>
<p>This principle has guided pandoc’s decisions in finding syntax for
tables, footnotes, and other extensions.</p>
<p>There is, however, one respect in which pandoc’s aims are different
from the original aims of Markdown. Whereas Markdown was originally
designed with HTML generation in mind, pandoc is designed for multiple
output formats. Thus, while pandoc allows the embedding of raw HTML, it
discourages it, and provides other, non-HTMLish ways of representing
important document elements like definition lists, tables, mathematics,
and footnotes.</p>
<h2 id="paragraphs">Paragraphs</h2>
<p>A paragraph is one or more lines of text followed by one or more
blank lines. Newlines are treated as spaces, so you can reflow your
paragraphs as you like. If you need a hard line break, put two or more
spaces at the end of a line.</p>
<h4 id="extension-escaped_line_breaks">Extension:
<code>escaped_line_breaks</code></h4>
<p>A backslash followed by a newline is also a hard line break. Note: in
multiline and grid table cells, this is the only way to create a hard
line break, since trailing spaces in the cells are ignored.</p>
<h2 id="headings">Headings</h2>
<p>There are two kinds of headings: Setext and ATX.</p>
<h3 id="setext-style-headings">Setext-style headings</h3>
<p>A setext-style heading is a line of text “underlined” with a row of
<code>=</code> signs (for a level-one heading) or <code>-</code> signs
(for a level-two heading):</p>
<pre><code>A level-one heading
===================

A level-two heading
-------------------</code></pre>
<p>The heading text can contain inline formatting, such as emphasis (see
<a href="#inline-formatting">Inline formatting</a>, below).</p>
<h3 id="atx-style-headings">ATX-style headings</h3>
<p>An ATX-style heading consists of one to six <code>#</code> signs and
a line of text, optionally followed by any number of <code>#</code>
signs. The number of <code>#</code> signs at the beginning of the line
is the heading level:</p>
<pre><code>## A level-two heading

### A level-three heading ###</code></pre>
<p>As with setext-style headings, the heading text can contain
formatting:</p>
<pre><code># A level-one heading with a [link](/url) and *emphasis*</code></pre>
<h4 id="extension-blank_before_header">Extension:
<code>blank_before_header</code></h4>
<p>Original Markdown syntax does not require a blank line before a
heading. Pandoc does require this (except, of course, at the beginning
of the document). The reason for the requirement is that it is all too
easy for a <code>#</code> to end up at the beginning of a line by
accident (perhaps through line wrapping). Consider, for example:</p>
<pre><code>I like several of their flavors of ice cream:
#22, for example, and #5.</code></pre>
<h4 id="extension-space_in_atx_header">Extension:
<code>space_in_atx_header</code></h4>
<p>Many Markdown implementations do not require a space between the
opening <code>#</code>s of an ATX heading and the heading text, so that
<code>#5 bolt</code> and <code>#hashtag</code> count as headings. With
this extension, pandoc does require the space.</p>
<h3 id="heading-identifiers">Heading identifiers</h3>
<p>See also the <a
href="#extension-auto_identifiers"><code>auto_identifiers</code>
extension</a> above.</p>
<h4 id="extension-header_attributes">Extension:
<code>header_attributes</code></h4>
<p>Headings can be assigned attributes using this syntax at the end of
the line containing the heading text:</p>
<pre><code>{#identifier .class .class key=value key=value}</code></pre>
<p>Thus, for example, the following headings will all be assigned the
identifier <code>foo</code>:</p>
<pre><code># My heading {#foo}

## My heading ##    {#foo}

My other heading   {#foo}
---------------</code></pre>
<p>(This syntax is compatible with <a
href="https://michelf.ca/projects/php-markdown/extra/">PHP Markdown
Extra</a>.)</p>
<p>Note that although this syntax allows assignment of classes and
key/value attributes, writers generally don’t use all of this
information. Identifiers, classes, and key/value attributes are used in
HTML and HTML-based formats such as EPUB and slidy. Identifiers are used
for labels and link anchors in the LaTeX, ConTeXt, Textile, Jira markup,
and AsciiDoc writers.</p>
<p>Headings with the class <code>unnumbered</code> will not be numbered,
even if <code>--number-sections</code> is specified. A single hyphen
(<code>-</code>) in an attribute context is equivalent to
<code>.unnumbered</code>, and preferable in non-English documents.
So,</p>
<pre><code># My heading {-}</code></pre>
<p>is just the same as</p>
<pre><code># My heading {.unnumbered}</code></pre>
<p>If the <code>unlisted</code> class is present in addition to
<code>unnumbered</code>, the heading will not be included in a table of
contents. (Currently this feature is only implemented for certain
formats: those based on LaTeX and HTML, PowerPoint, and RTF.)</p>
<h4 id="extension-implicit_header_references">Extension:
<code>implicit_header_references</code></h4>
<p>Pandoc behaves as if reference links have been defined for each
heading. So, to link to a heading</p>
<pre><code># Heading identifiers in HTML</code></pre>
<p>you can simply write</p>
<pre><code>[Heading identifiers in HTML]</code></pre>
<p>or</p>
<pre><code>[Heading identifiers in HTML][]</code></pre>
<p>or</p>
<pre><code>[the section on heading identifiers][heading identifiers in
HTML]</code></pre>
<p>instead of giving the identifier explicitly:</p>
<pre><code>[Heading identifiers in HTML](#heading-identifiers-in-html)</code></pre>
<p>If there are multiple headings with identical text, the corresponding
reference will link to the first one only, and you will need to use
explicit links to link to the others, as described above.</p>
<p>Like regular reference links, these references are
case-insensitive.</p>
<p>Explicit link reference definitions always take priority over
implicit heading references. So, in the following example, the link will
point to <code>bar</code>, not to <code>#foo</code>:</p>
<pre><code># Foo

[foo]: bar

See [foo]</code></pre>
<h2 id="block-quotations">Block quotations</h2>
<p>Markdown uses email conventions for quoting blocks of text. A block
quotation is one or more paragraphs or other block elements (such as
lists or headings), with each line preceded by a <code>&gt;</code>
character and an optional space. (The <code>&gt;</code> need not start
at the left margin, but it should not be indented more than three
spaces.)</p>
<pre><code>&gt; This is a block quote. This
&gt; paragraph has two lines.
&gt;
&gt; 1. This is a list inside a block quote.
&gt; 2. Second item.</code></pre>
<p>A “lazy” form, which requires the <code>&gt;</code> character only on
the first line of each block, is also allowed:</p>
<pre><code>&gt; This is a block quote. This
paragraph has two lines.

&gt; 1. This is a list inside a block quote.
2. Second item.</code></pre>
<p>Among the block elements that can be contained in a block quote are
other block quotes. That is, block quotes can be nested:</p>
<pre><code>&gt; This is a block quote.
&gt;
&gt; &gt; A block quote within a block quote.</code></pre>
<p>If the <code>&gt;</code> character is followed by an optional space,
that space will be considered part of the block quote marker and not
part of the indentation of the contents. Thus, to put an indented code
block in a block quote, you need five spaces after the
<code>&gt;</code>:</p>
<pre><code>&gt;     code</code></pre>
<h4 id="extension-blank_before_blockquote">Extension:
<code>blank_before_blockquote</code></h4>
<p>Original Markdown syntax does not require a blank line before a block
quote. Pandoc does require this (except, of course, at the beginning of
the document). The reason for the requirement is that it is all too easy
for a <code>&gt;</code> to end up at the beginning of a line by accident
(perhaps through line wrapping). So, unless the
<code>markdown_strict</code> format is used, the following does not
produce a nested block quote in pandoc:</p>
<pre><code>&gt; This is a block quote.
&gt;&gt; Not nested, since `blank_before_blockquote` is enabled by default</code></pre>
<h2 id="verbatim-code-blocks">Verbatim (code) blocks</h2>
<h3 id="indented-code-blocks">Indented code blocks</h3>
<p>A block of text indented four spaces (or one tab) is treated as
verbatim text: that is, special characters do not trigger special
formatting, and all spaces and line breaks are preserved. For
example,</p>
<pre><code>    if (a &gt; 3) {
      moveShip(5 * gravity, DOWN);
    }</code></pre>
<p>The initial (four space or one tab) indentation is not considered
part of the verbatim text, and is removed in the output.</p>
<p>Note: blank lines in the verbatim text need not begin with four
spaces.</p>
<h3 id="fenced-code-blocks">Fenced code blocks</h3>
<h4 id="extension-fenced_code_blocks">Extension:
<code>fenced_code_blocks</code></h4>
<p>In addition to standard indented code blocks, pandoc supports
<em>fenced</em> code blocks. These begin with a row of three or more
tildes (<code>~</code>) and end with a row of tildes that must be at
least as long as the starting row. Everything between these lines is
treated as code. No indentation is necessary:</p>
<pre><code>~~~~~~~
if (a &gt; 3) {
  moveShip(5 * gravity, DOWN);
}
~~~~~~~</code></pre>
<p>Like regular code blocks, fenced code blocks must be separated from
surrounding text by blank lines.</p>
<p>If the code itself contains a row of tildes or backticks, just use a
longer row of tildes or backticks at the start and end:</p>
<pre><code>~~~~~~~~~~~~~~~~
~~~~~~~~~~
code including tildes
~~~~~~~~~~
~~~~~~~~~~~~~~~~</code></pre>
<h4 id="extension-backtick_code_blocks">Extension:
<code>backtick_code_blocks</code></h4>
<p>Same as <code>fenced_code_blocks</code>, but uses backticks
(<code>`</code>) instead of tildes (<code>~</code>).</p>
<h4 id="extension-fenced_code_attributes">Extension:
<code>fenced_code_attributes</code></h4>
<p>Optionally, you may attach attributes to fenced or backtick code
block using this syntax:</p>
<pre><code>~~~~ {#mycode .haskell .numberLines startFrom=&quot;100&quot;}
qsort []     = []
qsort (x:xs) = qsort (filter (&lt; x) xs) ++ [x] ++
               qsort (filter (&gt;= x) xs)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~</code></pre>
<p>Here <code>mycode</code> is an identifier, <code>haskell</code> and
<code>numberLines</code> are classes, and <code>startFrom</code> is an
attribute with value <code>100</code>. Some output formats can use this
information to do syntax highlighting. Currently, the only output
formats that use this information are HTML, LaTeX, Docx, Ms, and
PowerPoint. If highlighting is supported for your output format and
language, then the code block above will appear highlighted, with
numbered lines. (To see which languages are supported, type
<code>pandoc --list-highlight-languages</code>.) Otherwise, the code
block above will appear as follows:</p>
<pre><code>&lt;pre id=&quot;mycode&quot; class=&quot;haskell numberLines&quot; startFrom=&quot;100&quot;&gt;
  &lt;code&gt;
  ...
  &lt;/code&gt;
&lt;/pre&gt;</code></pre>
<p>The <code>numberLines</code> (or <code>number-lines</code>) class
will cause the lines of the code block to be numbered, starting with
<code>1</code> or the value of the <code>startFrom</code> attribute. The
<code>lineAnchors</code> (or <code>line-anchors</code>) class will cause
the lines to be clickable anchors in HTML output.</p>
<p>A shortcut form can also be used for specifying the language of the
code block:</p>
<pre><code>```haskell
qsort [] = []
```</code></pre>
<p>This is equivalent to:</p>
<pre><code>``` {.haskell}
qsort [] = []
```</code></pre>
<p>This shortcut form may be combined with attributes:</p>
<pre><code>```haskell {.numberLines}
qsort [] = []
```</code></pre>
<p>Which is equivalent to:</p>
<pre><code>``` {.haskell .numberLines}
qsort [] = []
```</code></pre>
<p>If the <code>fenced_code_attributes</code> extension is disabled, but
input contains class attribute(s) for the code block, the first class
attribute will be printed after the opening fence as a bare word.</p>
<p>To prevent all highlighting, use the <code>--no-highlight</code>
flag. To set the highlighting style, use <code>--highlight-style</code>.
For more information on highlighting, see <a
href="#syntax-highlighting">Syntax highlighting</a>, below.</p>
<h2 id="line-blocks">Line blocks</h2>
<h4 id="extension-line_blocks">Extension: <code>line_blocks</code></h4>
<p>A line block is a sequence of lines beginning with a vertical bar
(<code>|</code>) followed by a space. The division into lines will be
preserved in the output, as will any leading spaces; otherwise, the
lines will be formatted as Markdown. This is useful for verse and
addresses:</p>
<pre><code>| The limerick packs laughs anatomical
| In space that is quite economical.
|    But the good ones I&#39;ve seen
|    So seldom are clean
| And the clean ones so seldom are comical

| 200 Main St.
| Berkeley, CA 94718</code></pre>
<p>The lines can be hard-wrapped if needed, but the continuation line
must begin with a space.</p>
<pre><code>| The Right Honorable Most Venerable and Righteous Samuel L.
  Constable, Jr.
| 200 Main St.
| Berkeley, CA 94718</code></pre>
<p>Inline formatting (such as emphasis) is allowed in the content, but
not block-level formatting (such as block quotes or lists).</p>
<p>This syntax is borrowed from <a
href="https://docutils.sourceforge.io/docs/ref/rst/introduction.html">reStructuredText</a>.</p>
<h2 id="lists">Lists</h2>
<h3 id="bullet-lists">Bullet lists</h3>
<p>A bullet list is a list of bulleted list items. A bulleted list item
begins with a bullet (<code>*</code>, <code>+</code>, or
<code>-</code>). Here is a simple example:</p>
<pre><code>* one
* two
* three</code></pre>
<p>This will produce a “compact” list. If you want a “loose” list, in
which each item is formatted as a paragraph, put spaces between the
items:</p>
<pre><code>* one

* two

* three</code></pre>
<p>The bullets need not be flush with the left margin; they may be
indented one, two, or three spaces. The bullet must be followed by
whitespace.</p>
<p>List items look best if subsequent lines are flush with the first
line (after the bullet):</p>
<pre><code>* here is my first
  list item.
* and my second.</code></pre>
<p>But Markdown also allows a “lazy” format:</p>
<pre><code>* here is my first
list item.
* and my second.</code></pre>
<h3 id="block-content-in-list-items">Block content in list items</h3>
<p>A list item may contain multiple paragraphs and other block-level
content. However, subsequent paragraphs must be preceded by a blank line
and indented to line up with the first non-space content after the list
marker.</p>
<pre><code>  * First paragraph.

    Continued.

  * Second paragraph. With a code block, which must be indented
    eight spaces:

        { code }</code></pre>
<p>Exception: if the list marker is followed by an indented code block,
which must begin 5 spaces after the list marker, then subsequent
paragraphs must begin two columns after the last character of the list
marker:</p>
<pre><code>*     code

  continuation paragraph</code></pre>
<p>List items may include other lists. In this case the preceding blank
line is optional. The nested list must be indented to line up with the
first non-space character after the list marker of the containing list
item.</p>
<pre><code>* fruits
  + apples
    - macintosh
    - red delicious
  + pears
  + peaches
* vegetables
  + broccoli
  + chard</code></pre>
<p>As noted above, Markdown allows you to write list items “lazily,”
instead of indenting continuation lines. However, if there are multiple
paragraphs or other blocks in a list item, the first line of each must
be indented.</p>
<pre><code>+ A lazy, lazy, list
item.

+ Another one; this looks
bad but is legal.

    Second paragraph of second
list item.</code></pre>
<h3 id="ordered-lists">Ordered lists</h3>
<p>Ordered lists work just like bulleted lists, except that the items
begin with enumerators rather than bullets.</p>
<p>In original Markdown, enumerators are decimal numbers followed by a
period and a space. The numbers themselves are ignored, so there is no
difference between this list:</p>
<pre><code>1.  one
2.  two
3.  three</code></pre>
<p>and this one:</p>
<pre><code>5.  one
7.  two
1.  three</code></pre>
<h4 id="extension-fancy_lists">Extension: <code>fancy_lists</code></h4>
<p>Unlike original Markdown, pandoc allows ordered list items to be
marked with uppercase and lowercase letters and roman numerals, in
addition to Arabic numerals. List markers may be enclosed in parentheses
or followed by a single right-parenthesis or period. They must be
separated from the text that follows by at least one space, and, if the
list marker is a capital letter with a period, by at least two spaces.<a
href="#fn1" class="footnote-ref" id="fnref1"
role="doc-noteref"><sup>1</sup></a></p>
<p>The <code>fancy_lists</code> extension also allows ‘<code>#</code>’
to be used as an ordered list marker in place of a numeral:</p>
<pre><code>#. one
#. two</code></pre>
<p>Note: the ‘<code>#</code>’ ordered list marker doesn’t work with
<code>commonmark</code>.</p>
<h4 id="extension-startnum">Extension: <code>startnum</code></h4>
<p>Pandoc also pays attention to the type of list marker used, and to
the starting number, and both of these are preserved where possible in
the output format. Thus, the following yields a list with numbers
followed by a single parenthesis, starting with 9, and a sublist with
lowercase roman numerals:</p>
<pre><code> 9)  Ninth
10)  Tenth
11)  Eleventh
       i. subone
      ii. subtwo
     iii. subthree</code></pre>
<p>Pandoc will start a new list each time a different type of list
marker is used. So, the following will create three lists:</p>
<pre><code>(2) Two
(5) Three
1.  Four
*   Five</code></pre>
<p>If default list markers are desired, use <code>#.</code>:</p>
<pre><code>#.  one
#.  two
#.  three</code></pre>
<h4 id="extension-task_lists">Extension: <code>task_lists</code></h4>
<p>Pandoc supports task lists, using the syntax of GitHub-Flavored
Markdown.</p>
<pre><code>- [ ] an unchecked task list item
- [x] checked item</code></pre>
<h3 id="definition-lists">Definition lists</h3>
<h4 id="extension-definition_lists">Extension:
<code>definition_lists</code></h4>
<p>Pandoc supports definition lists, using the syntax of <a
href="https://michelf.ca/projects/php-markdown/extra/">PHP Markdown
Extra</a> with some extensions.<a href="#fn2" class="footnote-ref"
id="fnref2" role="doc-noteref"><sup>2</sup></a></p>
<pre><code>Term 1

:   Definition 1

Term 2 with *inline markup*

:   Definition 2

        { some code, part of Definition 2 }

    Third paragraph of definition 2.</code></pre>
<p>Each term must fit on one line, which may optionally be followed by a
blank line, and must be followed by one or more definitions. A
definition begins with a colon or tilde, which may be indented one or
two spaces.</p>
<p>A term may have multiple definitions, and each definition may consist
of one or more block elements (paragraph, code block, list, etc.), each
indented four spaces or one tab stop. The body of the definition (not
including the first line) should be indented four spaces. However, as
with other Markdown lists, you can “lazily” omit indentation except at
the beginning of a paragraph or other block element:</p>
<pre><code>Term 1

:   Definition
with lazy continuation.

    Second paragraph of the definition.</code></pre>
<p>If you leave space before the definition (as in the example above),
the text of the definition will be treated as a paragraph. In some
output formats, this will mean greater spacing between term/definition
pairs. For a more compact definition list, omit the space before the
definition:</p>
<pre><code>Term 1
  ~ Definition 1

Term 2
  ~ Definition 2a
  ~ Definition 2b</code></pre>
<p>Note that space between items in a definition list is required. (A
variant that loosens this requirement, but disallows “lazy” hard
wrapping, can be activated with the <a
href="#extension-compact_definition_lists"><code>compact_definition_lists</code>
extension</a>.)</p>
<h3 id="numbered-example-lists">Numbered example lists</h3>
<h4 id="extension-example_lists">Extension:
<code>example_lists</code></h4>
<p>The special list marker <code>@</code> can be used for sequentially
numbered examples. The first list item with a <code>@</code> marker will
be numbered ‘1’, the next ‘2’, and so on, throughout the document. The
numbered examples need not occur in a single list; each new list using
<code>@</code> will take up where the last stopped. So, for example:</p>
<pre><code>(@)  My first example will be numbered (1).
(@)  My second example will be numbered (2).

Explanation of examples.

(@)  My third example will be numbered (3).</code></pre>
<p>Numbered examples can be labeled and referred to elsewhere in the
document:</p>
<pre><code>(@good)  This is a good example.

As (@good) illustrates, ...</code></pre>
<p>The label can be any string of alphanumeric characters, underscores,
or hyphens.</p>
<p>Note: continuation paragraphs in example lists must always be
indented four spaces, regardless of the length of the list marker. That
is, example lists always behave as if the <code>four_space_rule</code>
extension is set. This is because example labels tend to be long, and
indenting content to the first non-space character after the label would
be awkward.</p>
<h3 id="ending-a-list">Ending a list</h3>
<p>What if you want to put an indented code block after a list?</p>
<pre><code>-   item one
-   item two

    { my code block }</code></pre>
<p>Trouble! Here pandoc (like other Markdown implementations) will treat
<code>{ my code block }</code> as the second paragraph of item two, and
not as a code block.</p>
<p>To “cut off” the list after item two, you can insert some
non-indented content, like an HTML comment, which won’t produce visible
output in any format:</p>
<pre><code>-   item one
-   item two

&lt;!-- end of list --&gt;

    { my code block }</code></pre>
<p>You can use the same trick if you want two consecutive lists instead
of one big list:</p>
<pre><code>1.  one
2.  two
3.  three

&lt;!-- --&gt;

1.  uno
2.  dos
3.  tres</code></pre>
<h2 id="horizontal-rules">Horizontal rules</h2>
<p>A line containing a row of three or more <code>*</code>,
<code>-</code>, or <code>_</code> characters (optionally separated by
spaces) produces a horizontal rule:</p>
<pre><code>*  *  *  *

---------------</code></pre>
<p>We strongly recommend that horizontal rules be separated from
surrounding text by blank lines. If a horizontal rule is not followed by
a blank line, pandoc may try to interpret the lines that follow as a
YAML metadata block or a table.</p>
<h2 id="tables">Tables</h2>
<p>Four kinds of tables may be used. The first three kinds presuppose
the use of a fixed-width font, such as Courier. The fourth kind can be
used with proportionally spaced fonts, as it does not require lining up
columns.</p>
<h4 id="extension-table_captions">Extension:
<code>table_captions</code></h4>
<p>A caption may optionally be provided with all 4 kinds of tables (as
illustrated in the examples below). A caption is a paragraph beginning
with the string <code>Table:</code> (or <code>table:</code> or just
<code>:</code>), which will be stripped off. It may appear either before
or after the table.</p>
<h4 id="extension-simple_tables">Extension:
<code>simple_tables</code></h4>
<p>Simple tables look like this:</p>
<pre><code>  Right     Left     Center     Default
-------     ------ ----------   -------
     12     12        12            12
    123     123       123          123
      1     1          1             1

Table:  Demonstration of simple table syntax.</code></pre>
<p>The header and table rows must each fit on one line. Column
alignments are determined by the position of the header text relative to
the dashed line below it:<a href="#fn3" class="footnote-ref" id="fnref3"
role="doc-noteref"><sup>3</sup></a></p>
<ul>
<li>If the dashed line is flush with the header text on the right side
but extends beyond it on the left, the column is right-aligned.</li>
<li>If the dashed line is flush with the header text on the left side
but extends beyond it on the right, the column is left-aligned.</li>
<li>If the dashed line extends beyond the header text on both sides, the
column is centered.</li>
<li>If the dashed line is flush with the header text on both sides, the
default alignment is used (in most cases, this will be left).</li>
</ul>
<p>The table must end with a blank line, or a line of dashes followed by
a blank line.</p>
<p>The column header row may be omitted, provided a dashed line is used
to end the table. For example:</p>
<pre><code>-------     ------ ----------   -------
     12     12        12             12
    123     123       123           123
      1     1          1              1
-------     ------ ----------   -------</code></pre>
<p>When the header row is omitted, column alignments are determined on
the basis of the first line of the table body. So, in the tables above,
the columns would be right, left, center, and right aligned,
respectively.</p>
<h4 id="extension-multiline_tables">Extension:
<code>multiline_tables</code></h4>
<p>Multiline tables allow header and table rows to span multiple lines
of text (but cells that span multiple columns or rows of the table are
not supported). Here is an example:</p>
<pre><code>-------------------------------------------------------------
 Centered   Default           Right Left
  Header    Aligned         Aligned Aligned
----------- ------- --------------- -------------------------
   First    row                12.0 Example of a row that
                                    spans multiple lines.

  Second    row                 5.0 Here&#39;s another one. Note
                                    the blank line between
                                    rows.
-------------------------------------------------------------

Table: Here&#39;s the caption. It, too, may span
multiple lines.</code></pre>
<p>These work like simple tables, but with the following
differences:</p>
<ul>
<li>They must begin with a row of dashes, before the header text (unless
the header row is omitted).</li>
<li>They must end with a row of dashes, then a blank line.</li>
<li>The rows must be separated by blank lines.</li>
</ul>
<p>In multiline tables, the table parser pays attention to the widths of
the columns, and the writers try to reproduce these relative widths in
the output. So, if you find that one of the columns is too narrow in the
output, try widening it in the Markdown source.</p>
<p>The header may be omitted in multiline tables as well as simple
tables:</p>
<pre><code>----------- ------- --------------- -------------------------
   First    row                12.0 Example of a row that
                                    spans multiple lines.

  Second    row                 5.0 Here&#39;s another one. Note
                                    the blank line between
                                    rows.
----------- ------- --------------- -------------------------

: Here&#39;s a multiline table without a header.</code></pre>
<p>It is possible for a multiline table to have just one row, but the
row should be followed by a blank line (and then the row of dashes that
ends the table), or the table may be interpreted as a simple table.</p>
<h4 id="extension-grid_tables">Extension: <code>grid_tables</code></h4>
<p>Grid tables look like this:</p>
<pre><code>: Sample grid table.

+---------------+---------------+--------------------+
| Fruit         | Price         | Advantages         |
+===============+===============+====================+
| Bananas       | $1.34         | - built-in wrapper |
|               |               | - bright color     |
+---------------+---------------+--------------------+
| Oranges       | $2.10         | - cures scurvy     |
|               |               | - tasty            |
+---------------+---------------+--------------------+</code></pre>
<p>The row of <code>=</code>s separates the header from the table body,
and can be omitted for a headerless table. The cells of grid tables may
contain arbitrary block elements (multiple paragraphs, code blocks,
lists, etc.).</p>
<p>Cells can span multiple columns or rows:</p>
<pre><code>+---------------------+----------+
| Property            | Earth    |
+=============+=======+==========+
|             | min   | -89.2 °C |
| Temperature +-------+----------+
| 1961-1990   | mean  | 14 °C    |
|             +-------+----------+
|             | max   | 56.7 °C  |
+-------------+-------+----------+</code></pre>
<p>A table header may contain more than one row:</p>
<pre><code>+---------------------+-----------------------+
| Location            | Temperature 1961-1990 |
|                     | in degree Celsius     |
|                     +-------+-------+-------+
|                     | min   | mean  | max   |
+=====================+=======+=======+=======+
| Antarctica          | -89.2 | N/A   | 19.8  |
+---------------------+-------+-------+-------+
| Earth               | -89.2 | 14    | 56.7  |
+---------------------+-------+-------+-------+</code></pre>
<p>Alignments can be specified as with pipe tables, by putting colons at
the boundaries of the separator line after the header:</p>
<pre><code>+---------------+---------------+--------------------+
| Right         | Left          | Centered           |
+==============:+:==============+:==================:+
| Bananas       | $1.34         | built-in wrapper   |
+---------------+---------------+--------------------+</code></pre>
<p>For headerless tables, the colons go on the top line instead:</p>
<pre><code>+--------------:+:--------------+:------------------:+
| Right         | Left          | Centered           |
+---------------+---------------+--------------------+</code></pre>
<p>A table foot can be defined by enclosing it with separator lines that
use <code>=</code> instead of <code>-</code>:</p>
<pre><code> +---------------+---------------+
 | Fruit         | Price         |
 +===============+===============+
 | Bananas       | $1.34         |
 +---------------+---------------+
 | Oranges       | $2.10         |
 +===============+===============+
 | Sum           | $3.44         |
 +===============+===============+</code></pre>
<p>The foot must always be placed at the very bottom of the table.</p>
<p>Grid tables can be created easily using Emacs’ table-mode
(<code>M-x table-insert</code>).</p>
<h4 id="extension-pipe_tables">Extension: <code>pipe_tables</code></h4>
<p>Pipe tables look like this:</p>
<pre><code>| Right | Left | Default | Center |
|------:|:-----|---------|:------:|
|   12  |  12  |    12   |    12  |
|  123  |  123 |   123   |   123  |
|    1  |    1 |     1   |     1  |

  : Demonstration of pipe table syntax.</code></pre>
<p>The syntax is identical to <a
href="https://michelf.ca/projects/php-markdown/extra/#table">PHP
Markdown Extra tables</a>. The beginning and ending pipe characters are
optional, but pipes are required between all columns. The colons
indicate column alignment as shown. The header cannot be omitted. To
simulate a headerless table, include a header with blank cells.</p>
<p>Since the pipes indicate column boundaries, columns need not be
vertically aligned, as they are in the above example. So, this is a
perfectly legal (though ugly) pipe table:</p>
<pre><code>fruit| price
-----|-----:
apple|2.05
pear|1.37
orange|3.09</code></pre>
<p>The cells of pipe tables cannot contain block elements like
paragraphs and lists, and cannot span multiple lines. If any line of the
markdown source is longer than the column width (see
<code>--columns</code>), then the table will take up the full text width
and the cell contents will wrap, with the relative cell widths
determined by the number of dashes in the line separating the table
header from the table body. (For example <code>---|-</code> would make
the first column 3/4 and the second column 1/4 of the full text width.)
On the other hand, if no lines are wider than column width, then cell
contents will not be wrapped, and the cells will be sized to their
contents.</p>
<p>Note: pandoc also recognizes pipe tables of the following form, as
can be produced by Emacs’ orgtbl-mode:</p>
<pre><code>| One | Two   |
|-----+-------|
| my  | table |
| is  | nice  |</code></pre>
<p>The difference is that <code>+</code> is used instead of
<code>|</code>. Other orgtbl features are not supported. In particular,
to get non-default column alignment, you’ll need to add colons as
above.</p>
<h2 id="metadata-blocks">Metadata blocks</h2>
<h4 id="extension-pandoc_title_block">Extension:
<code>pandoc_title_block</code></h4>
<p>If the file begins with a title block</p>
<pre><code>% title
% author(s) (separated by semicolons)
% date</code></pre>
<p>it will be parsed as bibliographic information, not regular text. (It
will be used, for example, in the title of standalone LaTeX or HTML
output.) The block may contain just a title, a title and an author, or
all three elements. If you want to include an author but no title, or a
title and a date but no author, you need a blank line:</p>
<pre><code>%
% Author</code></pre>
<pre><code>% My title
%
% June 15, 2006</code></pre>
<p>The title may occupy multiple lines, but continuation lines must
begin with leading space, thus:</p>
<pre><code>% My title
  on multiple lines</code></pre>
<p>If a document has multiple authors, the authors may be put on
separate lines with leading space, or separated by semicolons, or both.
So, all of the following are equivalent:</p>
<pre><code>% Author One
  Author Two</code></pre>
<pre><code>% Author One; Author Two</code></pre>
<pre><code>% Author One;
  Author Two</code></pre>
<p>The date must fit on one line.</p>
<p>All three metadata fields may contain standard inline formatting
(italics, links, footnotes, etc.).</p>
<p>Title blocks will always be parsed, but they will affect the output
only when the <code>--standalone</code> (<code>-s</code>) option is
chosen. In HTML output, titles will appear twice: once in the document
head – this is the title that will appear at the top of the window in a
browser – and once at the beginning of the document body. The title in
the document head can have an optional prefix attached
(<code>--title-prefix</code> or <code>-T</code> option). The title in
the body appears as an H1 element with class “title”, so it can be
suppressed or reformatted with CSS. If a title prefix is specified with
<code>-T</code> and no title block appears in the document, the title
prefix will be used by itself as the HTML title.</p>
<p>The man page writer extracts a title, man page section number, and
other header and footer information from the title line. The title is
assumed to be the first word on the title line, which may optionally end
with a (single-digit) section number in parentheses. (There should be no
space between the title and the parentheses.) Anything after this is
assumed to be additional footer and header text. A single pipe character
(<code>|</code>) should be used to separate the footer text from the
header text. Thus,</p>
<pre><code>% PANDOC(1)</code></pre>
<p>will yield a man page with the title <code>PANDOC</code> and section
1.</p>
<pre><code>% PANDOC(1) Pandoc User Manuals</code></pre>
<p>will also have “Pandoc User Manuals” in the footer.</p>
<pre><code>% PANDOC(1) Pandoc User Manuals | Version 4.0</code></pre>
<p>will also have “Version 4.0” in the header.</p>
<h4 id="extension-yaml_metadata_block">Extension:
<code>yaml_metadata_block</code></h4>
<p>A <a href="https://yaml.org/spec/1.2/spec.html"
title="YAML v1.2 Spec">YAML</a> metadata block is a valid YAML object,
delimited by a line of three hyphens (<code>---</code>) at the top and a
line of three hyphens (<code>---</code>) or three dots
(<code>...</code>) at the bottom. The initial line <code>---</code> must
not be followed by a blank line. A YAML metadata block may occur
anywhere in the document, but if it is not at the beginning, it must be
preceded by a blank line.</p>
<p>Note that, because of the way pandoc concatenates input files when
several are provided, you may also keep the metadata in a separate YAML
file and pass it to pandoc as an argument, along with your Markdown
files:</p>
<pre><code>pandoc chap1.md chap2.md chap3.md metadata.yaml -s -o book.html</code></pre>
<p>Just be sure that the YAML file begins with <code>---</code> and ends
with <code>---</code> or <code>...</code>. Alternatively, you can use
the <code>--metadata-file</code> option. Using that approach however,
you cannot reference content (like footnotes) from the main markdown
input document.</p>
<p>Metadata will be taken from the fields of the YAML object and added
to any existing document metadata. Metadata can contain lists and
objects (nested arbitrarily), but all string scalars will be interpreted
as Markdown. Fields with names ending in an underscore will be ignored
by pandoc. (They may be given a role by external processors.) Field
names must not be interpretable as YAML numbers or boolean values (so,
for example, <code>yes</code>, <code>True</code>, and <code>15</code>
cannot be used as field names).</p>
<p>A document may contain multiple metadata blocks. If two metadata
blocks attempt to set the same field, the value from the second block
will be taken.</p>
<p>Each metadata block is handled internally as an independent YAML
document. This means, for example, that any YAML anchors defined in a
block cannot be referenced in another block.</p>
<p>When pandoc is used with <code>-t markdown</code> to create a
Markdown document, a YAML metadata block will be produced only if the
<code>-s/--standalone</code> option is used. All of the metadata will
appear in a single block at the beginning of the document.</p>
<p>Note that <a href="https://yaml.org/spec/1.2/spec.html"
title="YAML v1.2 Spec">YAML</a> escaping rules must be followed. Thus,
for example, if a title contains a colon, it must be quoted, and if it
contains a backslash escape, then it must be ensured that it is not
treated as a <a
href="https://yaml.org/spec/1.2/spec.html#id2776092">YAML escape
sequence</a>. The pipe character (<code>|</code>) can be used to begin
an indented block that will be interpreted literally, without need for
escaping. This form is necessary when the field contains blank lines or
block-level formatting:</p>
<pre><code>---
title:  &#39;This is the title: it contains a colon&#39;
author:
- Author One
- Author Two
keywords: [nothing, nothingness]
abstract: |
  This is the abstract.

  It consists of two paragraphs.
...</code></pre>
<p>The literal block after the <code>|</code> must be indented relative
to the line containing the <code>|</code>. If it is not, the YAML will
be invalid and pandoc will not interpret it as metadata. For an overview
of the complex rules governing YAML, see the <a
href="https://en.wikipedia.org/wiki/YAML#Syntax">Wikipedia entry on YAML
syntax</a>.</p>
<p>Template variables will be set automatically from the metadata. Thus,
for example, in writing HTML, the variable <code>abstract</code> will be
set to the HTML equivalent of the Markdown in the <code>abstract</code>
field:</p>
<pre><code>&lt;p&gt;This is the abstract.&lt;/p&gt;
&lt;p&gt;It consists of two paragraphs.&lt;/p&gt;</code></pre>
<p>Variables can contain arbitrary YAML structures, but the template
must match this structure. The <code>author</code> variable in the
default templates expects a simple list or string, but can be changed to
support more complicated structures. The following combination, for
example, would add an affiliation to the author if one is given:</p>
<pre><code>---
title: The document title
author:
- name: Author One
  affiliation: University of Somewhere
- name: Author Two
  affiliation: University of Nowhere
...</code></pre>
<p>To use the structured authors in the example above, you would need a
custom template:</p>
<pre><code>$for(author)$
$if(author.name)$
$author.name$$if(author.affiliation)$ ($author.affiliation$)$endif$
$else$
$author$
$endif$
$endfor$</code></pre>
<p>Raw content to include in the document’s header may be specified
using <code>header-includes</code>; however, it is important to mark up
this content as raw code for a particular output format, using the <a
href="#extension-raw_attribute"><code>raw_attribute</code>
extension</a>, or it will be interpreted as markdown. For example:</p>
<pre><code>header-includes:
- |
  ```{=latex}
  \let\oldsection\section
  \renewcommand{\section}[1]{\clearpage\oldsection{#1}}
  ```</code></pre>
<p>Note: the <code>yaml_metadata_block</code> extension works with
<code>commonmark</code> as well as <code>markdown</code> (and it is
enabled by default in <code>gfm</code> and <code>commonmark_x</code>).
However, in these formats the following restrictions apply:</p>
<ul>
<li><p>The YAML metadata block must occur at the beginning of the
document (and there can be only one). If multiple files are given as
arguments to pandoc, only the first can be a YAML metadata
block.</p></li>
<li><p>The leaf nodes of the YAML structure are parsed in isolation from
each other and from the rest of the document. So, for example, you can’t
use a reference link in these contexts if the link definition is
somewhere else in the document.</p></li>
</ul>
<h2 id="backslash-escapes">Backslash escapes</h2>
<h4 id="extension-all_symbols_escapable">Extension:
<code>all_symbols_escapable</code></h4>
<p>Except inside a code block or inline code, any punctuation or space
character preceded by a backslash will be treated literally, even if it
would normally indicate formatting. Thus, for example, if one writes</p>
<pre><code>*\*hello\**</code></pre>
<p>one will get</p>
<pre><code>&lt;em&gt;*hello*&lt;/em&gt;</code></pre>
<p>instead of</p>
<pre><code>&lt;strong&gt;hello&lt;/strong&gt;</code></pre>
<p>This rule is easier to remember than original Markdown’s rule, which
allows only the following characters to be backslash-escaped:</p>
<pre><code>\`*_{}[]()&gt;#+-.!</code></pre>
<p>(However, if the <code>markdown_strict</code> format is used, the
original Markdown rule will be used.)</p>
<p>A backslash-escaped space is parsed as a nonbreaking space. In TeX
output, it will appear as <code>~</code>. In HTML and XML output, it
will appear as a literal unicode nonbreaking space character (note that
it will thus actually look “invisible” in the generated HTML source; you
can still use the <code>--ascii</code> command-line option to make it
appear as an explicit entity).</p>
<p>A backslash-escaped newline (i.e. a backslash occurring at the end of
a line) is parsed as a hard line break. It will appear in TeX output as
<code>\\</code> and in HTML as <code>&lt;br /&gt;</code>. This is a nice
alternative to Markdown’s “invisible” way of indicating hard line breaks
using two trailing spaces on a line.</p>
<p>Backslash escapes do not work in verbatim contexts.</p>
<h2 id="inline-formatting">Inline formatting</h2>
<h3 id="emphasis">Emphasis</h3>
<p>To <em>emphasize</em> some text, surround it with <code>*</code>s or
<code>_</code>, like this:</p>
<pre><code>This text is _emphasized with underscores_, and this
is *emphasized with asterisks*.</code></pre>
<p>Double <code>*</code> or <code>_</code> produces <strong>strong
emphasis</strong>:</p>
<pre><code>This is **strong emphasis** and __with underscores__.</code></pre>
<p>A <code>*</code> or <code>_</code> character surrounded by spaces, or
backslash-escaped, will not trigger emphasis:</p>
<pre><code>This is * not emphasized *, and \*neither is this\*.</code></pre>
<h4 id="extension-intraword_underscores">Extension:
<code>intraword_underscores</code></h4>
<p>Because <code>_</code> is sometimes used inside words and
identifiers, pandoc does not interpret a <code>_</code> surrounded by
alphanumeric characters as an emphasis marker. If you want to emphasize
just part of a word, use <code>*</code>:</p>
<pre><code>feas*ible*, not feas*able*.</code></pre>
<h3 id="strikeout">Strikeout</h3>
<h4 id="extension-strikeout">Extension: <code>strikeout</code></h4>
<p>To strike out a section of text with a horizontal line, begin and end
it with <code>~~</code>. Thus, for example,</p>
<pre><code>This ~~is deleted text.~~</code></pre>
<h3 id="superscripts-and-subscripts">Superscripts and subscripts</h3>
<h4 id="extension-superscript-subscript">Extension:
<code>superscript</code>, <code>subscript</code></h4>
<p>Superscripts may be written by surrounding the superscripted text by
<code>^</code> characters; subscripts may be written by surrounding the
subscripted text by <code>~</code> characters. Thus, for example,</p>
<pre><code>H~2~O is a liquid.  2^10^ is 1024.</code></pre>
<p>The text between <code>^...^</code> or <code>~...~</code> may not
contain spaces or newlines. If the superscripted or subscripted text
contains spaces, these spaces must be escaped with backslashes. (This is
to prevent accidental superscripting and subscripting through the
ordinary use of <code>~</code> and <code>^</code>, and also bad
interactions with footnotes.) Thus, if you want the letter P with ‘a
cat’ in subscripts, use <code>P~a\ cat~</code>, not
<code>P~a cat~</code>.</p>
<h3 id="verbatim">Verbatim</h3>
<p>To make a short span of text verbatim, put it inside backticks:</p>
<pre><code>What is the difference between `&gt;&gt;=` and `&gt;&gt;`?</code></pre>
<p>If the verbatim text includes a backtick, use double backticks:</p>
<pre><code>Here is a literal backtick `` ` ``.</code></pre>
<p>(The spaces after the opening backticks and before the closing
backticks will be ignored.)</p>
<p>The general rule is that a verbatim span starts with a string of
consecutive backticks (optionally followed by a space) and ends with a
string of the same number of backticks (optionally preceded by a
space).</p>
<p>Note that backslash-escapes (and other Markdown constructs) do not
work in verbatim contexts:</p>
<pre><code>This is a backslash followed by an asterisk: `\*`.</code></pre>
<h4 id="extension-inline_code_attributes">Extension:
<code>inline_code_attributes</code></h4>
<p>Attributes can be attached to verbatim text, just as with <a
href="#fenced-code-blocks">fenced code blocks</a>:</p>
<pre><code>`&lt;$&gt;`{.haskell}</code></pre>
<h3 id="underline">Underline</h3>
<p>To underline text, use the <code>underline</code> class:</p>
<pre><code>[Underline]{.underline}</code></pre>
<p>Or, without the <code>bracketed_spans</code> extension (but with
<code>native_spans</code>):</p>
<pre><code>&lt;span class=&quot;underline&quot;&gt;Underline&lt;/span&gt;</code></pre>
<p>This will work in all output formats that support underline.</p>
<h3 id="small-caps">Small caps</h3>
<p>To write small caps, use the <code>smallcaps</code> class:</p>
<pre><code>[Small caps]{.smallcaps}</code></pre>
<p>Or, without the <code>bracketed_spans</code> extension:</p>
<pre><code>&lt;span class=&quot;smallcaps&quot;&gt;Small caps&lt;/span&gt;</code></pre>
<p>For compatibility with other Markdown flavors, CSS is also
supported:</p>
<pre><code>&lt;span style=&quot;font-variant:small-caps;&quot;&gt;Small caps&lt;/span&gt;</code></pre>
<p>This will work in all output formats that support small caps.</p>
<h3 id="highlighting">Highlighting</h3>
<p>To highlight text, use the <code>mark</code> class:</p>
<pre><code>[Mark]{.mark}</code></pre>
<p>Or, without the <code>bracketed_spans</code> extension (but with
<code>native_spans</code>):</p>
<pre><code>&lt;span class=&quot;mark&quot;&gt;Mark&lt;/span&gt;</code></pre>
<p>This will work in all output formats that support highlighting.</p>
<h2 id="math">Math</h2>
<h4 id="extension-tex_math_dollars">Extension:
<code>tex_math_dollars</code></h4>
<p>Anything between two <code>$</code> characters will be treated as TeX
math. The opening <code>$</code> must have a non-space character
immediately to its right, while the closing <code>$</code> must have a
non-space character immediately to its left, and must not be followed
immediately by a digit. Thus, <code>$20,000 and $30,000</code> won’t
parse as math. If for some reason you need to enclose text in literal
<code>$</code> characters, backslash-escape them and they won’t be
treated as math delimiters.</p>
<p>For display math, use <code>$$</code> delimiters. (In this case, the
delimiters may be separated from the formula by whitespace. However,
there can be no blank lines between the opening and closing
<code>$$</code> delimiters.)</p>
<p>TeX math will be printed in all output formats. How it is rendered
depends on the output format:</p>
<dl>
<dt>LaTeX</dt>
<dd>
It will appear verbatim surrounded by <code>\(...\)</code> (for inline
math) or <code>\[...\]</code> (for display math).
</dd>
<dt>Markdown, Emacs Org mode, ConTeXt, ZimWiki</dt>
<dd>
It will appear verbatim surrounded by <code>$...$</code> (for inline
math) or <code>$$...$$</code> (for display math).
</dd>
<dt>XWiki</dt>
<dd>
It will appear verbatim surrounded by
<code>{{formula}}..{{/formula}}</code>.
</dd>
<dt>reStructuredText</dt>
<dd>
It will be rendered using an <a
href="https://docutils.sourceforge.io/docs/ref/rst/roles.html#math">interpreted
text role <code>:math:</code></a>.
</dd>
<dt>AsciiDoc</dt>
<dd>
For AsciiDoc output math will appear verbatim surrounded by
<code>latexmath:[...]</code>. For <code>asciidoc_legacy</code> the
bracketed material will also include inline or display math delimiters.
</dd>
<dt>Texinfo</dt>
<dd>
It will be rendered inside a <code>@math</code> command.
</dd>
<dt>roff man, Jira markup</dt>
<dd>
It will be rendered verbatim without <code>$</code>’s.
</dd>
<dt>MediaWiki, DokuWiki</dt>
<dd>
It will be rendered inside <code>&lt;math&gt;</code> tags.
</dd>
<dt>Textile</dt>
<dd>
It will be rendered inside <code>&lt;span class="math"&gt;</code> tags.
</dd>
<dt>RTF, OpenDocument</dt>
<dd>
It will be rendered, if possible, using Unicode characters, and will
otherwise appear verbatim.
</dd>
<dt>ODT</dt>
<dd>
It will be rendered, if possible, using MathML.
</dd>
<dt>DocBook</dt>
<dd>
If the <code>--mathml</code> flag is used, it will be rendered using
MathML in an <code>inlineequation</code> or
<code>informalequation</code> tag. Otherwise it will be rendered, if
possible, using Unicode characters.
</dd>
<dt>Docx and PowerPoint</dt>
<dd>
It will be rendered using OMML math markup.
</dd>
<dt>FictionBook2</dt>
<dd>
If the <code>--webtex</code> option is used, formulas are rendered as
images using CodeCogs or other compatible web service, downloaded and
embedded in the e-book. Otherwise, they will appear verbatim.
</dd>
<dt>HTML, Slidy, DZSlides, S5, EPUB</dt>
<dd>
The way math is rendered in HTML will depend on the command-line options
selected. Therefore see <a href="#math-rendering-in-html">Math rendering
in HTML</a> above.
</dd>
</dl>
<h2 id="raw-html">Raw HTML</h2>
<h4 id="extension-raw_html">Extension: <code>raw_html</code></h4>
<p>Markdown allows you to insert raw HTML (or DocBook) anywhere in a
document (except verbatim contexts, where <code>&lt;</code>,
<code>&gt;</code>, and <code>&amp;</code> are interpreted literally).
(Technically this is not an extension, since standard Markdown allows
it, but it has been made an extension so that it can be disabled if
desired.)</p>
<p>The raw HTML is passed through unchanged in HTML, S5, Slidy,
Slideous, DZSlides, EPUB, Markdown, CommonMark, Emacs Org mode, and
Textile output, and suppressed in other formats.</p>
<p>For a more explicit way of including raw HTML in a Markdown document,
see the <a href="#extension-raw_attribute"><code>raw_attribute</code>
extension</a>.</p>
<p>In the CommonMark format, if <code>raw_html</code> is enabled,
superscripts, subscripts, strikeouts and small capitals will be
represented as HTML. Otherwise, plain-text fallbacks will be used. Note
that even if <code>raw_html</code> is disabled, tables will be rendered
with HTML syntax if they cannot use pipe syntax.</p>
<h4 id="extension-markdown_in_html_blocks">Extension:
<code>markdown_in_html_blocks</code></h4>
<p>Original Markdown allows you to include HTML “blocks”: blocks of HTML
between balanced tags that are separated from the surrounding text with
blank lines, and start and end at the left margin. Within these blocks,
everything is interpreted as HTML, not Markdown; so (for example),
<code>*</code> does not signify emphasis.</p>
<p>Pandoc behaves this way when the <code>markdown_strict</code> format
is used; but by default, pandoc interprets material between HTML block
tags as Markdown. Thus, for example, pandoc will turn</p>
<pre><code>&lt;table&gt;
&lt;tr&gt;
&lt;td&gt;*one*&lt;/td&gt;
&lt;td&gt;[a link](https://google.com)&lt;/td&gt;
&lt;/tr&gt;
&lt;/table&gt;</code></pre>
<p>into</p>
<pre><code>&lt;table&gt;
&lt;tr&gt;
&lt;td&gt;&lt;em&gt;one&lt;/em&gt;&lt;/td&gt;
&lt;td&gt;&lt;a href=&quot;https://google.com&quot;&gt;a link&lt;/a&gt;&lt;/td&gt;
&lt;/tr&gt;
&lt;/table&gt;</code></pre>
<p>whereas <code>Markdown.pl</code> will preserve it as is.</p>
<p>There is one exception to this rule: text between
<code>&lt;script&gt;</code>, <code>&lt;style&gt;</code>, and
<code>&lt;textarea&gt;</code> tags is not interpreted as Markdown.</p>
<p>This departure from original Markdown should make it easier to mix
Markdown with HTML block elements. For example, one can surround a block
of Markdown text with <code>&lt;div&gt;</code> tags without preventing
it from being interpreted as Markdown.</p>
<h4 id="extension-native_divs">Extension: <code>native_divs</code></h4>
<p>Use native pandoc <code>Div</code> blocks for content inside
<code>&lt;div&gt;</code> tags. For the most part this should give the
same output as <code>markdown_in_html_blocks</code>, but it makes it
easier to write pandoc filters to manipulate groups of blocks.</p>
<h4 id="extension-native_spans">Extension:
<code>native_spans</code></h4>
<p>Use native pandoc <code>Span</code> blocks for content inside
<code>&lt;span&gt;</code> tags. For the most part this should give the
same output as <code>raw_html</code>, but it makes it easier to write
pandoc filters to manipulate groups of inlines.</p>
<h4 id="extension-raw_tex">Extension: <code>raw_tex</code></h4>
<p>In addition to raw HTML, pandoc allows raw LaTeX, TeX, and ConTeXt to
be included in a document. Inline TeX commands will be preserved and
passed unchanged to the LaTeX and ConTeXt writers. Thus, for example,
you can use LaTeX to include BibTeX citations:</p>
<pre><code>This result was proved in \cite{jones.1967}.</code></pre>
<p>Note that in LaTeX environments, like</p>
<pre><code>\begin{tabular}{|l|l|}\hline
Age &amp; Frequency \\ \hline
18--25  &amp; 15 \\
26--35  &amp; 33 \\
36--45  &amp; 22 \\ \hline
\end{tabular}</code></pre>
<p>the material between the begin and end tags will be interpreted as
raw LaTeX, not as Markdown.</p>
<p>For a more explicit and flexible way of including raw TeX in a
Markdown document, see the <a
href="#extension-raw_attribute"><code>raw_attribute</code>
extension</a>.</p>
<p>Inline LaTeX is ignored in output formats other than Markdown, LaTeX,
Emacs Org mode, and ConTeXt.</p>
<h3 id="generic-raw-attribute">Generic raw attribute</h3>
<h4 id="extension-raw_attribute">Extension:
<code>raw_attribute</code></h4>
<p>Inline spans and fenced code blocks with a special kind of attribute
will be parsed as raw content with the designated format. For example,
the following produces a raw roff <code>ms</code> block:</p>
<pre><code>```{=ms}
.MYMACRO
blah blah
```</code></pre>
<p>And the following produces a raw <code>html</code> inline
element:</p>
<pre><code>This is `&lt;a&gt;html&lt;/a&gt;`{=html}</code></pre>
<p>This can be useful to insert raw xml into <code>docx</code>
documents, e.g. a pagebreak:</p>
<pre><code>```{=openxml}
&lt;w:p&gt;
  &lt;w:r&gt;
    &lt;w:br w:type=&quot;page&quot;/&gt;
  &lt;/w:r&gt;
&lt;/w:p&gt;
```</code></pre>
<p>The format name should match the target format name (see
<code>-t/--to</code>, above, for a list, or use
<code>pandoc --list-output-formats</code>). Use <code>openxml</code> for
<code>docx</code> output, <code>opendocument</code> for <code>odt</code>
output, <code>html5</code> for <code>epub3</code> output,
<code>html4</code> for <code>epub2</code> output, and
<code>latex</code>, <code>beamer</code>, <code>ms</code>, or
<code>html5</code> for <code>pdf</code> output (depending on what you
use for <code>--pdf-engine</code>).</p>
<p>This extension presupposes that the relevant kind of inline code or
fenced code block is enabled. Thus, for example, to use a raw attribute
with a backtick code block, <code>backtick_code_blocks</code> must be
enabled.</p>
<p>The raw attribute cannot be combined with regular attributes.</p>
<h2 id="latex-macros">LaTeX macros</h2>
<h4 id="extension-latex_macros">Extension:
<code>latex_macros</code></h4>
<p>When this extension is enabled, pandoc will parse LaTeX macro
definitions and apply the resulting macros to all LaTeX math and raw
LaTeX. So, for example, the following will work in all output formats,
not just LaTeX:</p>
<pre><code>\newcommand{\tuple}[1]{\langle #1 \rangle}

$\tuple{a, b, c}$</code></pre>
<p>Note that LaTeX macros will not be applied if they occur inside a raw
span or block marked with the <a
href="#extension-raw_attribute"><code>raw_attribute</code>
extension</a>.</p>
<p>When <code>latex_macros</code> is disabled, the raw LaTeX and math
will not have macros applied. This is usually a better approach when you
are targeting LaTeX or PDF.</p>
<p>Macro definitions in LaTeX will be passed through as raw LaTeX only
if <code>latex_macros</code> is not enabled. Macro definitions in
Markdown source (or other formats allowing <code>raw_tex</code>) will be
passed through regardless of whether <code>latex_macros</code> is
enabled.</p>
<h2 id="links-1">Links</h2>
<p>Markdown allows links to be specified in several ways.</p>
<h3 id="automatic-links">Automatic links</h3>
<p>If you enclose a URL or email address in pointy brackets, it will
become a link:</p>
<pre><code>&lt;https://google.com&gt;
&lt;<EMAIL>&gt;</code></pre>
<h3 id="inline-links">Inline links</h3>
<p>An inline link consists of the link text in square brackets, followed
by the URL in parentheses. (Optionally, the URL can be followed by a
link title, in quotes.)</p>
<pre><code>This is an [inline link](/url), and here&#39;s [one with
a title](https://fsf.org &quot;click here for a good time!&quot;).</code></pre>
<p>There can be no space between the bracketed part and the
parenthesized part. The link text can contain formatting (such as
emphasis), but the title cannot.</p>
<p>Email addresses in inline links are not autodetected, so they have to
be prefixed with <code>mailto</code>:</p>
<pre><code>[Write me!](mailto:<EMAIL>)</code></pre>
<h3 id="reference-links">Reference links</h3>
<p>An <em>explicit</em> reference link has two parts, the link itself
and the link definition, which may occur elsewhere in the document
(either before or after the link).</p>
<p>The link consists of link text in square brackets, followed by a
label in square brackets. (There cannot be space between the two unless
the <code>spaced_reference_links</code> extension is enabled.) The link
definition consists of the bracketed label, followed by a colon and a
space, followed by the URL, and optionally (after a space) a link title
either in quotes or in parentheses. The label must not be parseable as a
citation (assuming the <code>citations</code> extension is enabled):
citations take precedence over link labels.</p>
<p>Here are some examples:</p>
<pre><code>[my label 1]: /foo/bar.html  &quot;My title, optional&quot;
[my label 2]: /foo
[my label 3]: https://fsf.org (The Free Software Foundation)
[my label 4]: /bar#special  &#39;A title in single quotes&#39;</code></pre>
<p>The URL may optionally be surrounded by angle brackets:</p>
<pre><code>[my label 5]: &lt;http://foo.bar.baz&gt;</code></pre>
<p>The title may go on the next line:</p>
<pre><code>[my label 3]: https://fsf.org
  &quot;The Free Software Foundation&quot;</code></pre>
<p>Note that link labels are not case sensitive. So, this will work:</p>
<pre><code>Here is [my link][FOO]

[Foo]: /bar/baz</code></pre>
<p>In an <em>implicit</em> reference link, the second pair of brackets
is empty:</p>
<pre><code>See [my website][].

[my website]: http://foo.bar.baz</code></pre>
<p>Note: In <code>Markdown.pl</code> and most other Markdown
implementations, reference link definitions cannot occur in nested
constructions such as list items or block quotes. Pandoc lifts this
arbitrary-seeming restriction. So the following is fine in pandoc,
though not in most other implementations:</p>
<pre><code>&gt; My block [quote].
&gt;
&gt; [quote]: /foo</code></pre>
<h4 id="extension-shortcut_reference_links">Extension:
<code>shortcut_reference_links</code></h4>
<p>In a <em>shortcut</em> reference link, the second pair of brackets
may be omitted entirely:</p>
<pre><code>See [my website].

[my website]: http://foo.bar.baz</code></pre>
<h3 id="internal-links">Internal links</h3>
<p>To link to another section of the same document, use the
automatically generated identifier (see <a
href="#heading-identifiers">Heading identifiers</a>). For example:</p>
<pre><code>See the [Introduction](#introduction).</code></pre>
<p>or</p>
<pre><code>See the [Introduction].

[Introduction]: #introduction</code></pre>
<p>Internal links are currently supported for HTML formats (including
HTML slide shows and EPUB), LaTeX, and ConTeXt.</p>
<h2 id="images">Images</h2>
<p>A link immediately preceded by a <code>!</code> will be treated as an
image. The link text will be used as the image’s alt text:</p>
<pre><code>![la lune](lalune.jpg &quot;Voyage to the moon&quot;)

![movie reel]

[movie reel]: movie.gif</code></pre>
<h4 id="extension-implicit_figures">Extension:
<code>implicit_figures</code></h4>
<p>An image with nonempty alt text, occurring by itself in a paragraph,
will be rendered as a figure with a caption. The image’s alt text will
be used as the caption.</p>
<pre><code>![This is the caption](/url/of/image.png)</code></pre>
<p>How this is rendered depends on the output format. Some output
formats (e.g. RTF) do not yet support figures. In those formats, you’ll
just get an image in a paragraph by itself, with no caption.</p>
<p>If you just want a regular inline image, just make sure it is not the
only thing in the paragraph. One way to do this is to insert a
nonbreaking space after the image:</p>
<pre><code>![This image won&#39;t be a figure](/url/of/image.png)\</code></pre>
<p>Note that in reveal.js slide shows, an image in a paragraph by itself
that has the <code>r-stretch</code> class will fill the screen, and the
caption and figure tags will be omitted.</p>
<h4 id="extension-link_attributes">Extension:
<code>link_attributes</code></h4>
<p>Attributes can be set on links and images:</p>
<pre><code>An inline ![image](foo.jpg){#id .class width=30 height=20px}
and a reference ![image][ref] with attributes.

[ref]: foo.jpg &quot;optional title&quot; {#id .class key=val key2=&quot;val 2&quot;}</code></pre>
<p>(This syntax is compatible with <a
href="https://michelf.ca/projects/php-markdown/extra/">PHP Markdown
Extra</a> when only <code>#id</code> and <code>.class</code> are
used.)</p>
<p>For HTML and EPUB, all known HTML5 attributes except
<code>width</code> and <code>height</code> (but including
<code>srcset</code> and <code>sizes</code>) are passed through as is.
Unknown attributes are passed through as custom attributes, with
<code>data-</code> prepended. The other writers ignore attributes that
are not specifically supported by their output format.</p>
<p>The <code>width</code> and <code>height</code> attributes on images
are treated specially. When used without a unit, the unit is assumed to
be pixels. However, any of the following unit identifiers can be used:
<code>px</code>, <code>cm</code>, <code>mm</code>, <code>in</code>,
<code>inch</code> and <code>%</code>. There must not be any spaces
between the number and the unit. For example:</p>
<pre><code>![](file.jpg){ width=50% }</code></pre>
<ul>
<li>Dimensions may be converted to a form that is compatible with the
output format (for example, dimensions given in pixels will be converted
to inches when converting HTML to LaTeX). Conversion between pixels and
physical measurements is affected by the <code>--dpi</code> option (by
default, 96 dpi is assumed, unless the image itself contains dpi
information).</li>
<li>The <code>%</code> unit is generally relative to some available
space. For example the above example will render to the following.
<ul>
<li>HTML:
<code>&lt;img href="file.jpg" style="width: 50%;" /&gt;</code></li>
<li>LaTeX:
<code>\includegraphics[width=0.5\textwidth,height=\textheight]{file.jpg}</code>
(If you’re using a custom template, you need to configure
<code>graphicx</code> as in the default template.)</li>
<li>ConTeXt:
<code>\externalfigure[file.jpg][width=0.5\textwidth]</code></li>
</ul></li>
<li>Some output formats have a notion of a class (<a
href="https://wiki.contextgarden.net/Using_Graphics#Multiple_Image_Settings">ConTeXt</a>)
or a unique identifier (LaTeX <code>\caption</code>), or both
(HTML).</li>
<li>When no <code>width</code> or <code>height</code> attributes are
specified, the fallback is to look at the image resolution and the dpi
metadata embedded in the image file.</li>
</ul>
<h2 id="divs-and-spans">Divs and Spans</h2>
<p>Using the <code>native_divs</code> and <code>native_spans</code>
extensions (see <a href="#extension-native_divs">above</a>), HTML syntax
can be used as part of markdown to create native <code>Div</code> and
<code>Span</code> elements in the pandoc AST (as opposed to raw HTML).
However, there is also nicer syntax available:</p>
<h4 id="extension-fenced_divs">Extension: <code>fenced_divs</code></h4>
<p>Allow special fenced syntax for native <code>Div</code> blocks. A Div
starts with a fence containing at least three consecutive colons plus
some attributes. The attributes may optionally be followed by another
string of consecutive colons.</p>
<p>Note: the <code>commonmark</code> parser doesn’t permit colons after
the attributes.</p>
<p>The attribute syntax is exactly as in fenced code blocks (see <a
href="#extension-fenced_code_attributes">Extension:
<code>fenced_code_attributes</code></a>). As with fenced code blocks,
one can use either attributes in curly braces or a single unbraced word,
which will be treated as a class name. The Div ends with another line
containing a string of at least three consecutive colons. The fenced Div
should be separated by blank lines from preceding and following
blocks.</p>
<p>Example:</p>
<pre><code>::::: {#special .sidebar}
Here is a paragraph.

And another.
:::::</code></pre>
<p>Fenced divs can be nested. Opening fences are distinguished because
they <em>must</em> have attributes:</p>
<pre><code>::: Warning ::::::
This is a warning.

::: Danger
This is a warning within a warning.
:::
::::::::::::::::::</code></pre>
<p>Fences without attributes are always closing fences. Unlike with
fenced code blocks, the number of colons in the closing fence need not
match the number in the opening fence. However, it can be helpful for
visual clarity to use fences of different lengths to distinguish nested
divs from their parents.</p>
<h4 id="extension-bracketed_spans">Extension:
<code>bracketed_spans</code></h4>
<p>A bracketed sequence of inlines, as one would use to begin a link,
will be treated as a <code>Span</code> with attributes if it is followed
immediately by attributes:</p>
<pre><code>[This is *some text*]{.class key=&quot;val&quot;}</code></pre>
<h2 id="footnotes">Footnotes</h2>
<h4 id="extension-footnotes">Extension: <code>footnotes</code></h4>
<p>Pandoc’s Markdown allows footnotes, using the following syntax:</p>
<pre><code>Here is a footnote reference,[^1] and another.[^longnote]

[^1]: Here is the footnote.

[^longnote]: Here&#39;s one with multiple blocks.

    Subsequent paragraphs are indented to show that they
belong to the previous footnote.

        { some.code }

    The whole paragraph can be indented, or just the first
    line.  In this way, multi-paragraph footnotes work like
    multi-paragraph list items.

This paragraph won&#39;t be part of the note, because it
isn&#39;t indented.</code></pre>
<p>The identifiers in footnote references may not contain spaces, tabs,
or newlines. These identifiers are used only to correlate the footnote
reference with the note itself; in the output, footnotes will be
numbered sequentially.</p>
<p>The footnotes themselves need not be placed at the end of the
document. They may appear anywhere except inside other block elements
(lists, block quotes, tables, etc.). Each footnote should be separated
from surrounding content (including other footnotes) by blank lines.</p>
<h4 id="extension-inline_notes">Extension:
<code>inline_notes</code></h4>
<p>Inline footnotes are also allowed (though, unlike regular notes, they
cannot contain multiple paragraphs). The syntax is as follows:</p>
<pre><code>Here is an inline note.^[Inline notes are easier to write, since
you don&#39;t have to pick an identifier and move down to type the
note.]</code></pre>
<p>Inline and regular footnotes may be mixed freely.</p>
<h2 id="citation-syntax">Citation syntax</h2>
<h4 id="extension-citations">Extension: <code>citations</code></h4>
<p>To cite a bibliographic item with an identifier foo, use the syntax
<code>@foo</code>. Normal citations should be included in square
brackets, with semicolons separating distinct items:</p>
<pre><code>Blah blah [@doe99; @smith2000; @smith2004].</code></pre>
<p>How this is rendered depends on the citation style. In an author-date
style, it might render as</p>
<pre><code>Blah blah (Doe 1999, Smith 2000, 2004).</code></pre>
<p>In a footnote style, it might render as</p>
<pre><code>Blah blah.[^1]

[^1]:  John Doe, &quot;Frogs,&quot; *Journal of Amphibians* 44 (1999);
Susan Smith, &quot;Flies,&quot; *Journal of Insects* (2000);
Susan Smith, &quot;Bees,&quot; *Journal of Insects* (2004).</code></pre>
<p>See the <a href="https://citationstyles.org/authors/">CSL user
documentation</a> for more information about CSL styles and how they
affect rendering.</p>
<p>Unless a citation key starts with a letter, digit, or <code>_</code>,
and contains only alphanumerics and single internal punctuation
characters (<code>:.#$%&amp;-+?&lt;&gt;~/</code>), it must be surrounded
by curly braces, which are not considered part of the key. In
<code>@Foo_bar.baz.</code>, the key is <code>Foo_bar.baz</code> because
the final period is not <em>internal</em> punctuation, so it is not
included in the key. In <code>@{Foo_bar.baz.}</code>, the key is
<code>Foo_bar.baz.</code>, including the final period. In
<code>@Foo_bar--baz</code>, the key is <code>Foo_bar</code> because the
repeated internal punctuation characters terminate the key. The curly
braces are recommended if you use URLs as keys:
<code>[@{https://example.com/bib?name=foobar&amp;date=2000}, p.  33]</code>.</p>
<p>Citation items may optionally include a prefix, a locator, and a
suffix. In</p>
<pre><code>Blah blah [see @doe99, pp. 33-35 and *passim*; @smith04, chap. 1].</code></pre>
<p>the first item (<code>doe99</code>) has prefix <code>see</code>,
locator <code>pp.  33-35</code>, and suffix <code>and *passim*</code>.
The second item (<code>smith04</code>) has locator <code>chap. 1</code>
and no prefix or suffix.</p>
<p>Pandoc uses some heuristics to separate the locator from the rest of
the subject. It is sensitive to the locator terms defined in the <a
href="https://github.com/citation-style-language/locales">CSL locale
files</a>. Either abbreviated or unabbreviated forms are accepted. In
the <code>en-US</code> locale, locator terms can be written in either
singular or plural forms, as <code>book</code>,
<code>bk.</code>/<code>bks.</code>; <code>chapter</code>,
<code>chap.</code>/<code>chaps.</code>; <code>column</code>,
<code>col.</code>/<code>cols.</code>; <code>figure</code>,
<code>fig.</code>/<code>figs.</code>; <code>folio</code>,
<code>fol.</code>/<code>fols.</code>; <code>number</code>,
<code>no.</code>/<code>nos.</code>; <code>line</code>,
<code>l.</code>/<code>ll.</code>; <code>note</code>,
<code>n.</code>/<code>nn.</code>; <code>opus</code>,
<code>op.</code>/<code>opp.</code>; <code>page</code>,
<code>p.</code>/<code>pp.</code>; <code>paragraph</code>,
<code>para.</code>/<code>paras.</code>; <code>part</code>,
<code>pt.</code>/<code>pts.</code>; <code>section</code>,
<code>sec.</code>/<code>secs.</code>; <code>sub verbo</code>,
<code>s.v.</code>/<code>s.vv.</code>; <code>verse</code>,
<code>v.</code>/<code>vv.</code>; <code>volume</code>,
<code>vol.</code>/<code>vols.</code>; <code>¶</code>/<code>¶¶</code>;
<code>§</code>/<code>§§</code>. If no locator term is used, “page” is
assumed.</p>
<p>In complex cases, you can force something to be treated as a locator
by enclosing it in curly braces or prevent parsing the suffix as locator
by prepending curly braces:</p>
<pre><code>[@smith{ii, A, D-Z}, with a suffix]
[@smith, {pp. iv, vi-xi, (xv)-(xvii)} with suffix here]
[@smith{}, 99 years later]</code></pre>
<p>A minus sign (<code>-</code>) before the <code>@</code> will suppress
mention of the author in the citation. This can be useful when the
author is already mentioned in the text:</p>
<pre><code>Smith says blah [-@smith04].</code></pre>
<p>You can also write an author-in-text citation, by omitting the square
brackets:</p>
<pre><code>@smith04 says blah.

@smith04 [p. 33] says blah.</code></pre>
<p>This will cause the author’s name to be rendered, followed by the
bibliographical details. Use this form when you want to make the
citation the subject of a sentence.</p>
<p>When you are using a note style, it is usually better to let citeproc
create the footnotes from citations rather than writing an explicit
note. If you do write an explicit note that contains a citation, note
that normal citations will be put in parentheses, while author-in-text
citations will not. For this reason, it is sometimes preferable to use
the author-in-text style inside notes when using a note style.</p>
<h2 id="non-default-extensions">Non-default extensions</h2>
<p>The following Markdown syntax extensions are not enabled by default
in pandoc, but may be enabled by adding <code>+EXTENSION</code> to the
format name, where <code>EXTENSION</code> is the name of the extension.
Thus, for example, <code>markdown+hard_line_breaks</code> is Markdown
with hard line breaks.</p>
<h4 id="extension-rebase_relative_paths">Extension:
<code>rebase_relative_paths</code></h4>
<p>Rewrite relative paths for Markdown links and images, depending on
the path of the file containing the link or image link. For each link or
image, pandoc will compute the directory of the containing file,
relative to the working directory, and prepend the resulting path to the
link or image path.</p>
<p>The use of this extension is best understood by example. Suppose you
have a subdirectory for each chapter of a book, <code>chap1</code>,
<code>chap2</code>, <code>chap3</code>. Each contains a file
<code>text.md</code> and a number of images used in the chapter. You
would like to have <code>![image](spider.jpg)</code> in
<code>chap1/text.md</code> refer to <code>chap1/spider.jpg</code> and
<code>![image](spider.jpg)</code> in <code>chap2/text.md</code> refer to
<code>chap2/spider.jpg</code>. To do this, use</p>
<pre><code>pandoc chap*/*.md -f markdown+rebase_relative_paths</code></pre>
<p>Without this extension, you would have to use
<code>![image](chap1/spider.jpg)</code> in <code>chap1/text.md</code>
and <code>![image](chap2/spider.jpg)</code> in
<code>chap2/text.md</code>. Links with relative paths will be rewritten
in the same way as images.</p>
<p>Absolute paths and URLs are not changed. Neither are empty paths or
paths consisting entirely of a fragment, e.g., <code>#foo</code>.</p>
<p>Note that relative paths in reference links and images will be
rewritten relative to the file containing the link reference definition,
not the file containing the reference link or image itself, if these
differ.</p>
<h4 id="extension-mark">Extension: <code>mark</code></h4>
<p>To highlight out a section of text, begin and end it with with
<code>==</code>. Thus, for example,</p>
<pre><code>This ==is deleted text.==</code></pre>
<h4 id="extension-attributes">Extension: <code>attributes</code></h4>
<p>Allows attributes to be attached to any inline or block-level element
when parsing <code>commonmark</code>. The syntax for the attributes is
the same as that used in <a
href="#extension-header_attributes"><code>header_attributes</code></a>.</p>
<ul>
<li>Attributes that occur immediately after an inline element affect
that element. If they follow a space, then they belong to the space.
(Hence, this option subsumes <code>inline_code_attributes</code> and
<code>link_attributes</code>.)</li>
<li>Attributes that occur immediately before a block element, on a line
by themselves, affect that element.</li>
<li>Consecutive attribute specifiers may be used, either for blocks or
for inlines. Their attributes will be combined.</li>
<li>Attributes that occur at the end of the text of a Setext or ATX
heading (separated by whitespace from the text) affect the heading
element. (Hence, this option subsumes
<code>header_attributes</code>.)</li>
<li>Attributes that occur after the opening fence in a fenced code block
affect the code block element. (Hence, this option subsumes
<code>fenced_code_attributes</code>.)</li>
<li>Attributes that occur at the end of a reference link definition
affect links that refer to that definition.</li>
</ul>
<p>Note that pandoc’s AST does not currently allow attributes to be
attached to arbitrary elements. Hence a Span or Div container will be
added if needed.</p>
<h4 id="extension-old_dashes">Extension: <code>old_dashes</code></h4>
<p>Selects the pandoc &lt;= 1.8.2.1 behavior for parsing smart dashes:
<code>-</code> before a numeral is an en-dash, and <code>--</code> is an
em-dash. This option only has an effect if <code>smart</code> is
enabled. It is selected automatically for <code>textile</code>
input.</p>
<h4 id="extension-angle_brackets_escapable">Extension:
<code>angle_brackets_escapable</code></h4>
<p>Allow <code>&lt;</code> and <code>&gt;</code> to be
backslash-escaped, as they can be in GitHub flavored Markdown but not
original Markdown. This is implied by pandoc’s default
<code>all_symbols_escapable</code>.</p>
<h4 id="extension-lists_without_preceding_blankline">Extension:
<code>lists_without_preceding_blankline</code></h4>
<p>Allow a list to occur right after a paragraph, with no intervening
blank space.</p>
<h4 id="extension-four_space_rule">Extension:
<code>four_space_rule</code></h4>
<p>Selects the pandoc &lt;= 2.0 behavior for parsing lists, so that four
spaces indent are needed for list item continuation paragraphs.</p>
<h4 id="extension-spaced_reference_links">Extension:
<code>spaced_reference_links</code></h4>
<p>Allow whitespace between the two components of a reference link, for
example,</p>
<pre><code>[foo] [bar].</code></pre>
<h4 id="extension-hard_line_breaks">Extension:
<code>hard_line_breaks</code></h4>
<p>Causes all newlines within a paragraph to be interpreted as hard line
breaks instead of spaces.</p>
<h4 id="extension-ignore_line_breaks">Extension:
<code>ignore_line_breaks</code></h4>
<p>Causes newlines within a paragraph to be ignored, rather than being
treated as spaces or as hard line breaks. This option is intended for
use with East Asian languages where spaces are not used between words,
but text is divided into lines for readability.</p>
<h4 id="extension-east_asian_line_breaks">Extension:
<code>east_asian_line_breaks</code></h4>
<p>Causes newlines within a paragraph to be ignored, rather than being
treated as spaces or as hard line breaks, when they occur between two
East Asian wide characters. This is a better choice than
<code>ignore_line_breaks</code> for texts that include a mix of East
Asian wide characters and other characters.</p>
<h4 id="extension-emoji">Extension: <code>emoji</code></h4>
<p>Parses textual emojis like <code>:smile:</code> as Unicode
emoticons.</p>
<h4 id="extension-tex_math_single_backslash">Extension:
<code>tex_math_single_backslash</code></h4>
<p>Causes anything between <code>\(</code> and <code>\)</code> to be
interpreted as inline TeX math, and anything between <code>\[</code> and
<code>\]</code> to be interpreted as display TeX math. Note: a drawback
of this extension is that it precludes escaping <code>(</code> and
<code>[</code>.</p>
<h4 id="extension-tex_math_double_backslash">Extension:
<code>tex_math_double_backslash</code></h4>
<p>Causes anything between <code>\\(</code> and <code>\\)</code> to be
interpreted as inline TeX math, and anything between <code>\\[</code>
and <code>\\]</code> to be interpreted as display TeX math.</p>
<h4 id="extension-markdown_attribute">Extension:
<code>markdown_attribute</code></h4>
<p>By default, pandoc interprets material inside block-level tags as
Markdown. This extension changes the behavior so that Markdown is only
parsed inside block-level tags if the tags have the attribute
<code>markdown=1</code>.</p>
<h4 id="extension-mmd_title_block">Extension:
<code>mmd_title_block</code></h4>
<p>Enables a <a
href="https://fletcherpenney.net/multimarkdown/">MultiMarkdown</a> style
title block at the top of the document, for example:</p>
<pre><code>Title:   My title
Author:  John Doe
Date:    September 1, 2008
Comment: This is a sample mmd title block, with
         a field spanning multiple lines.</code></pre>
<p>See the MultiMarkdown documentation for details. If
<code>pandoc_title_block</code> or <code>yaml_metadata_block</code> is
enabled, it will take precedence over <code>mmd_title_block</code>.</p>
<h4 id="extension-abbreviations">Extension:
<code>abbreviations</code></h4>
<p>Parses PHP Markdown Extra abbreviation keys, like</p>
<pre><code>*[HTML]: Hypertext Markup Language</code></pre>
<p>Note that the pandoc document model does not support abbreviations,
so if this extension is enabled, abbreviation keys are simply skipped
(as opposed to being parsed as paragraphs).</p>
<h4 id="extension-autolink_bare_uris">Extension:
<code>autolink_bare_uris</code></h4>
<p>Makes all absolute URIs into links, even when not surrounded by
pointy braces <code>&lt;...&gt;</code>.</p>
<h4 id="extension-mmd_link_attributes">Extension:
<code>mmd_link_attributes</code></h4>
<p>Parses multimarkdown style key-value attributes on link and image
references. This extension should not be confused with the <a
href="#extension-link_attributes"><code>link_attributes</code></a>
extension.</p>
<pre><code>This is a reference ![image][ref] with multimarkdown attributes.

[ref]: https://path.to/image &quot;Image title&quot; width=20px height=30px
       id=myId class=&quot;myClass1 myClass2&quot;</code></pre>
<h4 id="extension-mmd_header_identifiers">Extension:
<code>mmd_header_identifiers</code></h4>
<p>Parses multimarkdown style heading identifiers (in square brackets,
after the heading but before any trailing <code>#</code>s in an ATX
heading).</p>
<h4 id="extension-compact_definition_lists">Extension:
<code>compact_definition_lists</code></h4>
<p>Activates the definition list syntax of pandoc 1.12.x and earlier.
This syntax differs from the one described above under <a
href="#definition-lists">Definition lists</a> in several respects:</p>
<ul>
<li>No blank line is required between consecutive items of the
definition list.</li>
<li>To get a “tight” or “compact” list, omit space between consecutive
items; the space between a term and its definition does not affect
anything.</li>
<li>Lazy wrapping of paragraphs is not allowed: the entire definition
must be indented four spaces.<a href="#fn4" class="footnote-ref"
id="fnref4" role="doc-noteref"><sup>4</sup></a></li>
</ul>
<h4 id="extension-gutenberg">Extension: <code>gutenberg</code></h4>
<p>Use <a href="https://www.gutenberg.org">Project Gutenberg</a>
conventions for <code>plain</code> output: all-caps for strong emphasis,
surround by underscores for regular emphasis, add extra blank space
around headings.</p>
<h4 id="extension-sourcepos">Extension: <code>sourcepos</code></h4>
<p>Include source position attributes when parsing
<code>commonmark</code>. For elements that accept attributes, a
<code>data-pos</code> attribute is added; other elements are placed in a
surrounding Div or Span element with a <code>data-pos</code>
attribute.</p>
<h4 id="extension-short_subsuperscripts">Extension:
<code>short_subsuperscripts</code></h4>
<p>Parse multimarkdown style subscripts and superscripts, which start
with a ‘~’ or ‘^’ character, respectively, and include the alphanumeric
sequence that follows. For example:</p>
<pre><code>x^2 = 4</code></pre>
<p>or</p>
<pre><code>Oxygen is O~2.</code></pre>
<h4 id="extension-wikilinks_title_after_pipe">Extension:
<code>wikilinks_title_after_pipe</code></h4>
<p>Pandoc supports multiple markdown wikilink syntaxes, regardless of
whether the title is before or after the pipe.</p>
<p>Using <code>--from=markdown+wikilinks_title_after_pipe</code> results
in</p>
<pre class="[[wiki]]"><code>[[URL|title]]</code></pre>
<p>while using <code>--from=markdown+wikilinks_title_before_pipe</code>
results in</p>
<pre class="[[wiki]]"><code>[[title|URL]]</code></pre>
<h2 id="markdown-variants">Markdown variants</h2>
<p>In addition to pandoc’s extended Markdown, the following Markdown
variants are supported:</p>
<ul>
<li><code>markdown_phpextra</code> (PHP Markdown Extra)</li>
<li><code>markdown_github</code> (deprecated GitHub-Flavored
Markdown)</li>
<li><code>markdown_mmd</code> (MultiMarkdown)</li>
<li><code>markdown_strict</code> (Markdown.pl)</li>
<li><code>commonmark</code> (CommonMark)</li>
<li><code>gfm</code> (Github-Flavored Markdown)</li>
<li><code>commonmark_x</code> (CommonMark with many pandoc
extensions)</li>
</ul>
<p>To see which extensions are supported for a given format, and which
are enabled by default, you can use the command</p>
<pre><code>pandoc --list-extensions=FORMAT</code></pre>
<p>where <code>FORMAT</code> is replaced with the name of the
format.</p>
<p>Note that the list of extensions for <code>commonmark</code>,
<code>gfm</code>, and <code>commonmark_x</code> are defined relative to
default commonmark. So, for example, <code>backtick_code_blocks</code>
does not appear as an extension, since it is enabled by default and
cannot be disabled.</p>
<h1 id="citations">Citations</h1>
<p>When the <code>--citeproc</code> option is used, pandoc can
automatically generate citations and a bibliography in a number of
styles. Basic usage is</p>
<pre><code>pandoc --citeproc myinput.txt</code></pre>
<p>To use this feature, you will need to have</p>
<ul>
<li>a document containing citations (see <a
href="#citation-syntax">Citation syntax</a>);</li>
<li>a source of bibliographic data: either an external bibliography file
or a list of <code>references</code> in the document’s YAML
metadata;</li>
<li>optionally, a <a
href="https://docs.citationstyles.org/en/stable/specification.html">CSL</a>
citation style.</li>
</ul>
<h2 id="specifying-bibliographic-data">Specifying bibliographic
data</h2>
<p>You can specify an external bibliography using the
<code>bibliography</code> metadata field in a YAML metadata section or
the <code>--bibliography</code> command line argument. If you want to
use multiple bibliography files, you can supply multiple
<code>--bibliography</code> arguments or set <code>bibliography</code>
metadata field to YAML array. A bibliography may have any of these
formats:</p>
<table>
<thead>
<tr class="header">
<th style="text-align: left;">Format</th>
<th>File extension</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: left;">BibLaTeX</td>
<td>.bib</td>
</tr>
<tr class="even">
<td style="text-align: left;">BibTeX</td>
<td>.bibtex</td>
</tr>
<tr class="odd">
<td style="text-align: left;">CSL JSON</td>
<td>.json</td>
</tr>
<tr class="even">
<td style="text-align: left;">CSL YAML</td>
<td>.yaml</td>
</tr>
<tr class="odd">
<td style="text-align: left;">RIS</td>
<td>.ris</td>
</tr>
</tbody>
</table>
<p>Note that <code>.bib</code> can be used with both BibTeX and BibLaTeX
files; use the extension <code>.bibtex</code> to force interpretation as
BibTeX.</p>
<p>In BibTeX and BibLaTeX databases, pandoc parses LaTeX markup inside
fields such as <code>title</code>; in CSL YAML databases, pandoc
Markdown; and in CSL JSON databases, an <a
href="https://docs.citationstyles.org/en/1.0/release-notes.html#rich-text-markup-within-fields">HTML-like
markup</a>:</p>
<dl>
<dt><code>&lt;i&gt;...&lt;/i&gt;</code></dt>
<dd>
italics
</dd>
<dt><code>&lt;b&gt;...&lt;/b&gt;</code></dt>
<dd>
bold
</dd>
<dt><code>&lt;span style="font-variant:small-caps;"&gt;...&lt;/span&gt;</code>
or <code>&lt;sc&gt;...&lt;/sc&gt;</code></dt>
<dd>
small capitals
</dd>
<dt><code>&lt;sub&gt;...&lt;/sub&gt;</code></dt>
<dd>
subscript
</dd>
<dt><code>&lt;sup&gt;...&lt;/sup&gt;</code></dt>
<dd>
superscript
</dd>
<dt><code>&lt;span class="nocase"&gt;...&lt;/span&gt;</code></dt>
<dd>
prevent a phrase from being capitalized as title case
</dd>
</dl>
<p>As an alternative to specifying a bibliography file using
<code>--bibliography</code> or the YAML metadata field
<code>bibliography</code>, you can include the citation data directly in
the <code>references</code> field of the document’s YAML metadata. The
field should contain an array of YAML-encoded references, for
example:</p>
<pre><code>---
references:
- type: article-journal
  id: WatsonCrick1953
  author:
  - family: Watson
    given: J. D.
  - family: Crick
    given: F. H. C.
  issued:
    date-parts:
    - - 1953
      - 4
      - 25
  title: &#39;Molecular structure of nucleic acids: a structure for
    deoxyribose nucleic acid&#39;
  title-short: Molecular structure of nucleic acids
  container-title: Nature
  volume: 171
  issue: 4356
  page: 737-738
  DOI: 10.1038/171737a0
  URL: https://www.nature.com/articles/171737a0
  language: en-GB
...</code></pre>
<p>If both an external bibliography and inline (YAML metadata)
references are provided, both will be used. In case of conflicting
<code>id</code>s, the inline references will take precedence.</p>
<p>Note that pandoc can be used to produce such a YAML metadata section
from a BibTeX, BibLaTeX, or CSL JSON bibliography:</p>
<pre><code>pandoc chem.bib -s -f biblatex -t markdown
pandoc chem.json -s -f csljson -t markdown</code></pre>
<p>Indeed, pandoc can convert between any of these citation formats:</p>
<pre><code>pandoc chem.bib -s -f biblatex -t csljson
pandoc chem.yaml -s -f markdown -t biblatex</code></pre>
<p>Running pandoc on a bibliography file with the
<code>--citeproc</code> option will create a formatted bibliography in
the format of your choice:</p>
<pre><code>pandoc chem.bib -s --citeproc -o chem.html
pandoc chem.bib -s --citeproc -o chem.pdf</code></pre>
<h3 id="capitalization-in-titles">Capitalization in titles</h3>
<p>If you are using a bibtex or biblatex bibliography, then observe the
following rules:</p>
<ul>
<li><p>English titles should be in title case. Non-English titles should
be in sentence case, and the <code>langid</code> field in biblatex
should be set to the relevant language. (The following values are
treated as English: <code>american</code>, <code>british</code>,
<code>canadian</code>, <code>english</code>, <code>australian</code>,
<code>newzealand</code>, <code>USenglish</code>, or
<code>UKenglish</code>.)</p></li>
<li><p>As is standard with bibtex/biblatex, proper names should be
protected with curly braces so that they won’t be lowercased in styles
that call for sentence case. For example:</p>
<pre><code>title = {My Dinner with {Andre}}</code></pre></li>
<li><p>In addition, words that should remain lowercase (or camelCase)
should be protected:</p>
<pre><code>title = {Spin Wave Dispersion on the {nm} Scale}</code></pre>
<p>Though this is not necessary in bibtex/biblatex, it is necessary with
citeproc, which stores titles internally in sentence case, and converts
to title case in styles that require it. Here we protect “nm” so that it
doesn’t get converted to “Nm” at this stage.</p></li>
</ul>
<p>If you are using a CSL bibliography (either JSON or YAML), then
observe the following rules:</p>
<ul>
<li><p>All titles should be in sentence case.</p></li>
<li><p>Use the <code>language</code> field for non-English titles to
prevent their conversion to title case in styles that call for this.
(Conversion happens only if <code>language</code> begins with
<code>en</code> or is left empty.)</p></li>
<li><p>Protect words that should not be converted to title case using
this syntax:</p>
<pre><code>Spin wave dispersion on the &lt;span class=&quot;nocase&quot;&gt;nm&lt;/span&gt; scale</code></pre></li>
</ul>
<h3 id="conference-papers-published-vs.-unpublished">Conference Papers,
Published vs. Unpublished</h3>
<p>For a formally published conference paper, use the biblatex entry
type <code>inproceedings</code> (which will be mapped to CSL
<code>paper-conference</code>).</p>
<p>For an unpublished manuscript, use the biblatex entry type
<code>unpublished</code> without an <code>eventtitle</code> field (this
entry type will be mapped to CSL <code>manuscript</code>).</p>
<p>For a talk, an unpublished conference paper, or a poster
presentation, use the biblatex entry type <code>unpublished</code> with
an <code>eventtitle</code> field (this entry type will be mapped to CSL
<code>speech</code>). Use the biblatex <code>type</code> field to
indicate the type, e.g. “Paper”, or “Poster”. <code>venue</code> and
<code>eventdate</code> may be useful too, though <code>eventdate</code>
will not be rendered by most CSL styles. Note that <code>venue</code> is
for the event’s venue, unlike <code>location</code> which describes the
publisher’s location; do not use the latter for an unpublished
conference paper.</p>
<h2 id="specifying-a-citation-style">Specifying a citation style</h2>
<p>Citations and references can be formatted using any style supported
by the <a href="https://citationstyles.org">Citation Style Language</a>,
listed in the <a href="https://www.zotero.org/styles">Zotero Style
Repository</a>. These files are specified using the <code>--csl</code>
option or the <code>csl</code> (or <code>citation-style</code>) metadata
field. By default, pandoc will use the <a
href="https://chicagomanualofstyle.org">Chicago Manual of Style</a>
author-date format. (You can override this default by copying a CSL
style of your choice to <code>default.csl</code> in your user data
directory.) The CSL project provides further information on <a
href="https://citationstyles.org/authors/">finding and editing
styles</a>.</p>
<p>The <code>--citation-abbreviations</code> option (or the
<code>citation-abbreviations</code> metadata field) may be used to
specify a JSON file containing abbreviations of journals that should be
used in formatted bibliographies when <code>form="short"</code> is
specified. The format of the file can be illustrated with an
example:</p>
<pre><code>{ &quot;default&quot;: {
    &quot;container-title&quot;: {
            &quot;Lloyd&#39;s Law Reports&quot;: &quot;Lloyd&#39;s Rep&quot;,
            &quot;Estates Gazette&quot;: &quot;EG&quot;,
            &quot;Scots Law Times&quot;: &quot;SLT&quot;
    }
  }
}</code></pre>
<h2 id="citations-in-note-styles">Citations in note styles</h2>
<p>Pandoc’s citation processing is designed to allow you to move between
author-date, numerical, and note styles without modifying the markdown
source. When you’re using a note style, avoid inserting footnotes
manually. Instead, insert citations just as you would in an author-date
style—for example,</p>
<pre><code>Blah blah [@foo, p. 33].</code></pre>
<p>The footnote will be created automatically. Pandoc will take care of
removing the space and moving the note before or after the period,
depending on the setting of <code>notes-after-punctuation</code>, as
described below in <a href="#other-relevant-metadata-fields">Other
relevant metadata fields</a>.</p>
<p>In some cases you may need to put a citation inside a regular
footnote. Normal citations in footnotes (such as
<code>[@foo, p. 33]</code>) will be rendered in parentheses. In-text
citations (such as <code>@foo [p. 33]</code>) will be rendered without
parentheses. (A comma will be added if appropriate.) Thus:</p>
<pre><code>[^1]:  Some studies [@foo; @bar, p. 33] show that
frubulicious zoosnaps are quantical.  For a survey
of the literature, see @baz [chap. 1].</code></pre>
<h2 id="placement-of-the-bibliography">Placement of the
bibliography</h2>
<p>If the style calls for a list of works cited, it will be placed in a
div with id <code>refs</code>, if one exists:</p>
<pre><code>::: {#refs}
:::</code></pre>
<p>Otherwise, it will be placed at the end of the document. Generation
of the bibliography can be suppressed by setting
<code>suppress-bibliography: true</code> in the YAML metadata.</p>
<p>If you wish the bibliography to have a section heading, you can set
<code>reference-section-title</code> in the metadata, or put the heading
at the beginning of the div with id <code>refs</code> (if you are using
it) or at the end of your document:</p>
<pre><code>last paragraph...

# References</code></pre>
<p>The bibliography will be inserted after this heading. Note that the
<code>unnumbered</code> class will be added to this heading, so that the
section will not be numbered.</p>
<p>If you want to put the bibliography into a variable in your template,
one way to do that is to put the div with id <code>refs</code> into a
metadata field, e.g.</p>
<pre><code>---
refs: |
   ::: {#refs}
   :::
...</code></pre>
<p>You can then put the variable <code>$refs$</code> into your template
where you want the bibliography to be placed.</p>
<h2 id="including-uncited-items-in-the-bibliography">Including uncited
items in the bibliography</h2>
<p>If you want to include items in the bibliography without actually
citing them in the body text, you can define a dummy <code>nocite</code>
metadata field and put the citations there:</p>
<pre><code>---
nocite: |
  @item1, @item2
...

@item3</code></pre>
<p>In this example, the document will contain a citation for
<code>item3</code> only, but the bibliography will contain entries for
<code>item1</code>, <code>item2</code>, and <code>item3</code>.</p>
<p>It is possible to create a bibliography with all the citations,
whether or not they appear in the document, by using a wildcard:</p>
<pre><code>---
nocite: |
  @*
...</code></pre>
<p>For LaTeX output, you can also use <a
href="https://ctan.org/pkg/natbib"><code>natbib</code></a> or <a
href="https://ctan.org/pkg/biblatex"><code>biblatex</code></a> to render
the bibliography. In order to do so, specify bibliography files as
outlined above, and add <code>--natbib</code> or <code>--biblatex</code>
argument to pandoc invocation. Bear in mind that bibliography files have
to be in either BibTeX (for <code>--natbib</code>) or BibLaTeX (for
<code>--biblatex</code>) format.</p>
<h2 id="other-relevant-metadata-fields">Other relevant metadata
fields</h2>
<p>A few other metadata fields affect bibliography formatting:</p>
<dl>
<dt><code>link-citations</code></dt>
<dd>
If true, citations will be hyperlinked to the corresponding bibliography
entries (for author-date and numerical styles only). Defaults to false.
</dd>
<dt><code>link-bibliography</code></dt>
<dd>
If true, DOIs, PMCIDs, PMID, and URLs in bibliographies will be rendered
as hyperlinks. (If an entry contains a DOI, PMCID, PMID, or URL, but
none of these fields are rendered by the style, then the title, or in
the absence of a title the whole entry, will be hyperlinked.) Defaults
to true.
</dd>
<dt><code>lang</code></dt>
<dd>
<p>The <code>lang</code> field will affect how the style is localized,
for example in the translation of labels, the use of quotation marks,
and the way items are sorted. (For backwards compatibility,
<code>locale</code> may be used instead of <code>lang</code>, but this
use is deprecated.)</p>
<p>A BCP 47 language tag is expected: for example, <code>en</code>,
<code>de</code>, <code>en-US</code>, <code>fr-CA</code>,
<code>ug-Cyrl</code>. The unicode extension syntax (after
<code>-u-</code>) may be used to specify options for collation (sorting)
more precisely. Here are some examples:</p>
<ul>
<li><code>zh-u-co-pinyin</code> – Chinese with the Pinyin
collation.</li>
<li><code>es-u-co-trad</code> – Spanish with the traditional collation
(with <code>Ch</code> sorting after <code>C</code>).</li>
<li><code>fr-u-kb</code> – French with “backwards” accent sorting (with
<code>coté</code> sorting after <code>côte</code>).</li>
<li><code>en-US-u-kf-upper</code> – English with uppercase letters
sorting before lower (default is lower before upper).</li>
</ul>
</dd>
<dt><code>notes-after-punctuation</code></dt>
<dd>
If true (the default for note styles), pandoc will put footnote
references or superscripted numerical citations after following
punctuation. For example, if the source contains
<code>blah blah [@jones99].</code>, the result will look like
<code>blah blah.[^1]</code>, with the note moved after the period and
the space collapsed. If false, the space will still be collapsed, but
the footnote will not be moved after the punctuation. The option may
also be used in numerical styles that use superscripts for citation
numbers (but for these styles the default is not to move the citation).
</dd>
</dl>
<h1 id="slide-shows">Slide shows</h1>
<p>You can use pandoc to produce an HTML + JavaScript slide presentation
that can be viewed via a web browser. There are five ways to do this,
using <a href="https://meyerweb.com/eric/tools/s5/">S5</a>, <a
href="https://paulrouget.com/dzslides/">DZSlides</a>, <a
href="https://www.w3.org/Talks/Tools/Slidy2/">Slidy</a>, <a
href="https://goessner.net/articles/slideous/">Slideous</a>, or <a
href="https://revealjs.com/">reveal.js</a>. You can also produce a PDF
slide show using LaTeX <a
href="https://ctan.org/pkg/beamer"><code>beamer</code></a>, or slide
shows in Microsoft <a
href="https://en.wikipedia.org/wiki/Microsoft_PowerPoint">PowerPoint</a>
format.</p>
<p>Here’s the Markdown source for a simple slide show,
<code>habits.txt</code>:</p>
<pre><code>% Habits
% John Doe
% March 22, 2005

# In the morning

## Getting up

- Turn off alarm
- Get out of bed

## Breakfast

- Eat eggs
- Drink coffee

# In the evening

## Dinner

- Eat spaghetti
- Drink wine

------------------

![picture of spaghetti](images/spaghetti.jpg)

## Going to sleep

- Get in bed
- Count sheep</code></pre>
<p>To produce an HTML/JavaScript slide show, simply type</p>
<pre><code>pandoc -t FORMAT -s habits.txt -o habits.html</code></pre>
<p>where <code>FORMAT</code> is either <code>s5</code>,
<code>slidy</code>, <code>slideous</code>, <code>dzslides</code>, or
<code>revealjs</code>.</p>
<p>For Slidy, Slideous, reveal.js, and S5, the file produced by pandoc
with the <code>-s/--standalone</code> option embeds a link to JavaScript
and CSS files, which are assumed to be available at the relative path
<code>s5/default</code> (for S5), <code>slideous</code> (for Slideous),
<code>reveal.js</code> (for reveal.js), or at the Slidy website at
<code>w3.org</code> (for Slidy). (These paths can be changed by setting
the <code>slidy-url</code>, <code>slideous-url</code>,
<code>revealjs-url</code>, or <code>s5-url</code> variables; see <a
href="#variables-for-html-slides">Variables for HTML slides</a>, above.)
For DZSlides, the (relatively short) JavaScript and CSS are included in
the file by default.</p>
<p>With all HTML slide formats, the <code>--self-contained</code> option
can be used to produce a single file that contains all of the data
necessary to display the slide show, including linked scripts,
stylesheets, images, and videos.</p>
<p>To produce a PDF slide show using beamer, type</p>
<pre><code>pandoc -t beamer habits.txt -o habits.pdf</code></pre>
<p>Note that a reveal.js slide show can also be converted to a PDF by
printing it to a file from the browser.</p>
<p>To produce a PowerPoint slide show, type</p>
<pre><code>pandoc habits.txt -o habits.pptx</code></pre>
<h2 id="structuring-the-slide-show">Structuring the slide show</h2>
<p>By default, the <em>slide level</em> is the highest heading level in
the hierarchy that is followed immediately by content, and not another
heading, somewhere in the document. In the example above, level-1
headings are always followed by level-2 headings, which are followed by
content, so the slide level is 2. This default can be overridden using
the <code>--slide-level</code> option.</p>
<p>The document is carved up into slides according to the following
rules:</p>
<ul>
<li><p>A horizontal rule always starts a new slide.</p></li>
<li><p>A heading at the slide level always starts a new slide.</p></li>
<li><p>Headings <em>below</em> the slide level in the hierarchy create
headings <em>within</em> a slide. (In beamer, a “block” will be created.
If the heading has the class <code>example</code>, an
<code>exampleblock</code> environment will be used; if it has the class
<code>alert</code>, an <code>alertblock</code> will be used; otherwise a
regular <code>block</code> will be used.)</p></li>
<li><p>Headings <em>above</em> the slide level in the hierarchy create
“title slides,” which just contain the section title and help to break
the slide show into sections. Non-slide content under these headings
will be included on the title slide (for HTML slide shows) or in a
subsequent slide with the same title (for beamer).</p></li>
<li><p>A title page is constructed automatically from the document’s
title block, if present. (In the case of beamer, this can be disabled by
commenting out some lines in the default template.)</p></li>
</ul>
<p>These rules are designed to support many different styles of slide
show. If you don’t care about structuring your slides into sections and
subsections, you can either just use level-1 headings for all slides (in
that case, level 1 will be the slide level) or you can set
<code>--slide-level=0</code>.</p>
<p>Note: in reveal.js slide shows, if slide level is 2, a
two-dimensional layout will be produced, with level-1 headings building
horizontally and level-2 headings building vertically. It is not
recommended that you use deeper nesting of section levels with reveal.js
unless you set <code>--slide-level=0</code> (which lets reveal.js
produce a one-dimensional layout and only interprets horizontal rules as
slide boundaries).</p>
<h3 id="powerpoint-layout-choice">PowerPoint layout choice</h3>
<p>When creating slides, the pptx writer chooses from a number of
pre-defined layouts, based on the content of the slide:</p>
<dl>
<dt>Title Slide</dt>
<dd>
This layout is used for the initial slide, which is generated and filled
from the metadata fields <code>date</code>, <code>author</code>, and
<code>title</code>, if they are present.
</dd>
<dt>Section Header</dt>
<dd>
This layout is used for what pandoc calls “title slides”, i.e. slides
which start with a header which is above the slide level in the
hierarchy.
</dd>
<dt>Two Content</dt>
<dd>
This layout is used for two-column slides, i.e. slides containing a div
with class <code>columns</code> which contains at least two divs with
class <code>column</code>.
</dd>
<dt>Comparison</dt>
<dd>
This layout is used instead of “Two Content” for any two-column slides
in which at least one column contains text followed by non-text (e.g. an
image or a table).
</dd>
<dt>Content with Caption</dt>
<dd>
This layout is used for any non-two-column slides which contain text
followed by non-text (e.g. an image or a table).
</dd>
<dt>Blank</dt>
<dd>
This layout is used for any slides which only contain blank content,
e.g. a slide containing only speaker notes, or a slide containing only a
non-breaking space.
</dd>
<dt>Title and Content</dt>
<dd>
This layout is used for all slides which do not match the criteria for
another layout.
</dd>
</dl>
<p>These layouts are chosen from the default pptx reference doc included
with pandoc, unless an alternative reference doc is specified using
<code>--reference-doc</code>.</p>
<h2 id="incremental-lists">Incremental lists</h2>
<p>By default, these writers produce lists that display “all at once.”
If you want your lists to display incrementally (one item at a time),
use the <code>-i</code> option. If you want a particular list to depart
from the default, put it in a <code>div</code> block with class
<code>incremental</code> or <code>nonincremental</code>. So, for
example, using the <code>fenced div</code> syntax, the following would
be incremental regardless of the document default:</p>
<pre><code>::: incremental

- Eat spaghetti
- Drink wine

:::</code></pre>
<p>or</p>
<pre><code>::: nonincremental

- Eat spaghetti
- Drink wine

:::</code></pre>
<p>While using <code>incremental</code> and <code>nonincremental</code>
divs is the recommended method of setting incremental lists on a
per-case basis, an older method is also supported: putting lists inside
a blockquote will depart from the document default (that is, it will
display incrementally without the <code>-i</code> option and all at once
with the <code>-i</code> option):</p>
<pre><code>&gt; - Eat spaghetti
&gt; - Drink wine</code></pre>
<p>Both methods allow incremental and nonincremental lists to be mixed
in a single document.</p>
<p>If you want to include a block-quoted list, you can work around this
behavior by putting the list inside a fenced div, so that it is not the
direct child of the block quote:</p>
<pre><code>&gt; ::: wrapper
&gt; - a
&gt; - list in a quote
&gt; :::</code></pre>
<h2 id="inserting-pauses">Inserting pauses</h2>
<p>You can add “pauses” within a slide by including a paragraph
containing three dots, separated by spaces:</p>
<pre><code># Slide with a pause

content before the pause

. . .

content after the pause</code></pre>
<p>Note: this feature is not yet implemented for PowerPoint output.</p>
<h2 id="styling-the-slides">Styling the slides</h2>
<p>You can change the style of HTML slides by putting customized CSS
files in <code>$DATADIR/s5/default</code> (for S5),
<code>$DATADIR/slidy</code> (for Slidy), or
<code>$DATADIR/slideous</code> (for Slideous), where
<code>$DATADIR</code> is the user data directory (see
<code>--data-dir</code>, above). The originals may be found in pandoc’s
system data directory (generally
<code>$CABALDIR/pandoc-VERSION/s5/default</code>). Pandoc will look
there for any files it does not find in the user data directory.</p>
<p>For dzslides, the CSS is included in the HTML file itself, and may be
modified there.</p>
<p>All <a href="https://revealjs.com/config/">reveal.js configuration
options</a> can be set through variables. For example, themes can be
used by setting the <code>theme</code> variable:</p>
<pre><code>-V theme=moon</code></pre>
<p>Or you can specify a custom stylesheet using the <code>--css</code>
option.</p>
<p>To style beamer slides, you can specify a <code>theme</code>,
<code>colortheme</code>, <code>fonttheme</code>,
<code>innertheme</code>, and <code>outertheme</code>, using the
<code>-V</code> option:</p>
<pre><code>pandoc -t beamer habits.txt -V theme:Warsaw -o habits.pdf</code></pre>
<p>Note that heading attributes will turn into slide attributes (on a
<code>&lt;div&gt;</code> or <code>&lt;section&gt;</code>) in HTML slide
formats, allowing you to style individual slides. In beamer, a number of
heading classes and attributes are recognized as frame options and will
be passed through as options to the frame: see <a
href="#frame-attributes-in-beamer">Frame attributes in beamer</a>,
below.</p>
<h2 id="speaker-notes">Speaker notes</h2>
<p>Speaker notes are supported in reveal.js, PowerPoint (pptx), and
beamer output. You can add notes to your Markdown document thus:</p>
<pre><code>::: notes

This is my note.

- It can contain Markdown
- like this list

:::</code></pre>
<p>To show the notes window in reveal.js, press <code>s</code> while
viewing the presentation. Speaker notes in PowerPoint will be available,
as usual, in handouts and presenter view.</p>
<p>Notes are not yet supported for other slide formats, but the notes
will not appear on the slides themselves.</p>
<h2 id="columns">Columns</h2>
<p>To put material in side by side columns, you can use a native div
container with class <code>columns</code>, containing two or more div
containers with class <code>column</code> and a <code>width</code>
attribute:</p>
<pre><code>:::::::::::::: {.columns}
::: {.column width=&quot;40%&quot;}
contents...
:::
::: {.column width=&quot;60%&quot;}
contents...
:::
::::::::::::::</code></pre>
<h3 id="additional-columns-attributes-in-beamer">Additional columns
attributes in beamer</h3>
<p>The div containers with classes <code>columns</code> and
<code>column</code> can optionally have an <code>align</code> attribute.
The class <code>columns</code> can optionally have a
<code>totalwidth</code> attribute or an <code>onlytextwidth</code>
class.</p>
<pre><code>:::::::::::::: {.columns align=center totalwidth=8em}
::: {.column width=&quot;40%&quot;}
contents...
:::
::: {.column width=&quot;60%&quot; align=bottom}
contents...
:::
::::::::::::::</code></pre>
<p>The <code>align</code> attributes on <code>columns</code> and
<code>column</code> can be used with the values <code>top</code>,
<code>top-baseline</code>, <code>center</code> and <code>bottom</code>
to vertically align the columns. It defaults to <code>top</code> in
<code>columns</code>.</p>
<p>The <code>totalwidth</code> attribute limits the width of the columns
to the given value.</p>
<pre><code>:::::::::::::: {.columns align=top .onlytextwidth}
::: {.column width=&quot;40%&quot; align=center}
contents...
:::
::: {.column width=&quot;60%&quot;}
contents...
:::
::::::::::::::</code></pre>
<p>The class <code>onlytextwidth</code> sets the <code>totalwidth</code>
to <code>\textwidth</code>.</p>
<p>See Section 12.7 of the <a
href="http://mirrors.ctan.org/macros/latex/contrib/beamer/doc/beameruserguide.pdf">Beamer
User’s Guide</a> for more details.</p>
<h2 id="frame-attributes-in-beamer">Frame attributes in beamer</h2>
<p>Sometimes it is necessary to add the LaTeX <code>[fragile]</code>
option to a frame in beamer (for example, when using the
<code>minted</code> environment). This can be forced by adding the
<code>fragile</code> class to the heading introducing the slide:</p>
<pre><code># Fragile slide {.fragile}</code></pre>
<p>All of the other frame attributes described in Section 8.1 of the <a
href="http://mirrors.ctan.org/macros/latex/contrib/beamer/doc/beameruserguide.pdf">Beamer
User’s Guide</a> may also be used: <code>allowdisplaybreaks</code>,
<code>allowframebreaks</code>, <code>b</code>, <code>c</code>,
<code>s</code>, <code>t</code>, <code>environment</code>,
<code>label</code>, <code>plain</code>, <code>shrink</code>,
<code>standout</code>, <code>noframenumbering</code>,
<code>squeeze</code>. <code>allowframebreaks</code> is recommended
especially for bibliographies, as it allows multiple slides to be
created if the content overfills the frame:</p>
<pre><code># References {.allowframebreaks}</code></pre>
<p>In addition, the <code>frameoptions</code> attribute may be used to
pass arbitrary frame options to a beamer slide:</p>
<pre><code># Heading {frameoptions=&quot;squeeze,shrink,customoption=foobar&quot;}</code></pre>
<h2 id="background-in-reveal.js-beamer-and-pptx">Background in
reveal.js, beamer, and pptx</h2>
<p>Background images can be added to self-contained reveal.js slide
shows, beamer slide shows, and pptx slide shows.</p>
<h3 id="on-all-slides-beamer-reveal.js-pptx">On all slides (beamer,
reveal.js, pptx)</h3>
<p>With beamer and reveal.js, the configuration option
<code>background-image</code> can be used either in the YAML metadata
block or as a command-line variable to get the same image on every
slide.</p>
<p>Note that for reveal.js, the <code>background-image</code> will be
used as a <code>parallaxBackgroundImage</code> (see below).</p>
<p>For pptx, you can use a <a href="#option--reference-doc">reference
doc</a> in which background images have been set on the <a
href="#powerpoint-layout-choice">relevant layouts</a>.</p>
<h4
id="parallaxbackgroundimage-reveal.js"><code>parallaxBackgroundImage</code>
(reveal.js)</h4>
<p>For reveal.js, there is also the reveal.js-native option
<code>parallaxBackgroundImage</code>, which produces a parallax
scrolling background. You must also set
<code>parallaxBackgroundSize</code>, and can optionally set
<code>parallaxBackgroundHorizontal</code> and
<code>parallaxBackgroundVertical</code> to configure the scrolling
behaviour. See the <a
href="https://revealjs.com/backgrounds/#parallax-background">reveal.js
documentation</a> for more details about the meaning of these
options.</p>
<p>In reveal.js’s overview mode, the parallaxBackgroundImage will show
up only on the first slide.</p>
<h3 id="on-individual-slides-reveal.js-pptx">On individual slides
(reveal.js, pptx)</h3>
<p>To set an image for a particular reveal.js or pptx slide, add
<code>{background-image="/path/to/image"}</code> to the first
slide-level heading on the slide (which may even be empty).</p>
<p>As the <a href="#extension-link_attributes">HTML writers pass unknown
attributes through</a>, other reveal.js background settings also work on
individual slides, including <code>background-size</code>,
<code>background-repeat</code>, <code>background-color</code>,
<code>transition</code>, and <code>transition-speed</code>. (The
<code>data-</code> prefix will automatically be added.)</p>
<p>Note: <code>data-background-image</code> is also supported in pptx
for consistency with reveal.js – if <code>background-image</code> isn’t
found, <code>data-background-image</code> will be checked.</p>
<h3 id="on-the-title-slide-reveal.js-pptx">On the title slide
(reveal.js, pptx)</h3>
<p>To add a background image to the automatically generated title slide
for reveal.js, use the <code>title-slide-attributes</code> variable in
the YAML metadata block. It must contain a map of attribute names and
values. (Note that the <code>data-</code> prefix is required here, as it
isn’t added automatically.)</p>
<p>For pptx, pass a <a href="#option--reference-doc">reference doc</a>
with the background image set on the “Title Slide” layout.</p>
<h3 id="example-reveal.js">Example (reveal.js)</h3>
<pre><code>---
title: My Slide Show
parallaxBackgroundImage: /path/to/my/background_image.png
title-slide-attributes:
    data-background-image: /path/to/title_image.png
    data-background-size: contain
---

## Slide One

Slide 1 has background_image.png as its background.

## {background-image=&quot;/path/to/special_image.jpg&quot;}

Slide 2 has a special image for its background, even though the heading has no content.</code></pre>
<h1 id="epubs">EPUBs</h1>
<h2 id="epub-metadata">EPUB Metadata</h2>
<p>EPUB metadata may be specified using the <code>--epub-metadata</code>
option, but if the source document is Markdown, it is better to use a <a
href="#extension-yaml_metadata_block">YAML metadata block</a>. Here is
an example:</p>
<pre><code>---
title:
- type: main
  text: My Book
- type: subtitle
  text: An investigation of metadata
creator:
- role: author
  text: John Smith
- role: editor
  text: Sarah Jones
identifier:
- scheme: DOI
  text: doi:10.234234.234/33
publisher:  My Press
rights: © 2007 John Smith, CC BY-NC
ibooks:
  version: 1.3.4
...</code></pre>
<p>The following fields are recognized:</p>
<dl>
<dt><code>identifier</code></dt>
<dd>
Either a string value or an object with fields <code>text</code> and
<code>scheme</code>. Valid values for <code>scheme</code> are
<code>ISBN-10</code>, <code>GTIN-13</code>, <code>UPC</code>,
<code>ISMN-10</code>, <code>DOI</code>, <code>LCCN</code>,
<code>GTIN-14</code>, <code>ISBN-13</code>,
<code>Legal deposit number</code>, <code>URN</code>, <code>OCLC</code>,
<code>ISMN-13</code>, <code>ISBN-A</code>, <code>JP</code>,
<code>OLCC</code>.
</dd>
<dt><code>title</code></dt>
<dd>
Either a string value, or an object with fields <code>file-as</code> and
<code>type</code>, or a list of such objects. Valid values for
<code>type</code> are <code>main</code>, <code>subtitle</code>,
<code>short</code>, <code>collection</code>, <code>edition</code>,
<code>extended</code>.
</dd>
<dt><code>creator</code></dt>
<dd>
Either a string value, or an object with fields <code>role</code>,
<code>file-as</code>, and <code>text</code>, or a list of such objects.
Valid values for <code>role</code> are <a
href="https://loc.gov/marc/relators/relaterm.html">MARC relators</a>,
but pandoc will attempt to translate the human-readable versions (like
“author” and “editor”) to the appropriate marc relators.
</dd>
<dt><code>contributor</code></dt>
<dd>
Same format as <code>creator</code>.
</dd>
<dt><code>date</code></dt>
<dd>
A string value in <code>YYYY-MM-DD</code> format. (Only the year is
necessary.) Pandoc will attempt to convert other common date formats.
</dd>
<dt><code>lang</code> (or legacy: <code>language</code>)</dt>
<dd>
A string value in <a href="https://tools.ietf.org/html/bcp47">BCP 47</a>
format. Pandoc will default to the local language if nothing is
specified.
</dd>
<dt><code>subject</code></dt>
<dd>
Either a string value, or an object with fields <code>text</code>,
<code>authority</code>, and <code>term</code>, or a list of such
objects. Valid values for <code>authority</code> are either a <a
href="https://idpf.github.io/epub-registries/authorities/">reserved
authority value</a> (currently <code>AAT</code>, <code>BIC</code>,
<code>BISAC</code>, <code>CLC</code>, <code>DDC</code>,
<code>CLIL</code>, <code>EuroVoc</code>, <code>MEDTOP</code>,
<code>LCSH</code>, <code>NDC</code>, <code>Thema</code>,
<code>UDC</code>, and <code>WGS</code>) or an absolute IRI identifying a
custom scheme. Valid values for <code>term</code> are defined by the
scheme.
</dd>
<dt><code>description</code></dt>
<dd>
A string value.
</dd>
<dt><code>type</code></dt>
<dd>
A string value.
</dd>
<dt><code>format</code></dt>
<dd>
A string value.
</dd>
<dt><code>relation</code></dt>
<dd>
A string value.
</dd>
<dt><code>coverage</code></dt>
<dd>
A string value.
</dd>
<dt><code>rights</code></dt>
<dd>
A string value.
</dd>
<dt><code>belongs-to-collection</code></dt>
<dd>
A string value. Identifies the name of a collection to which the EPUB
Publication belongs.
</dd>
<dt><code>group-position</code></dt>
<dd>
The <code>group-position</code> field indicates the numeric position in
which the EPUB Publication belongs relative to other works belonging to
the same <code>belongs-to-collection</code> field.
</dd>
<dt><code>cover-image</code></dt>
<dd>
A string value (path to cover image).
</dd>
<dt><code>css</code> (or legacy: <code>stylesheet</code>)</dt>
<dd>
A string value (path to CSS stylesheet).
</dd>
<dt><code>page-progression-direction</code></dt>
<dd>
Either <code>ltr</code> or <code>rtl</code>. Specifies the
<code>page-progression-direction</code> attribute for the <a
href="http://idpf.org/epub/301/spec/epub-publications.html#sec-spine-elem"><code>spine</code>
element</a>.
</dd>
<dt><code>ibooks</code></dt>
<dd>
<p>iBooks-specific metadata, with the following fields:</p>
<ul>
<li><code>version</code>: (string)</li>
<li><code>specified-fonts</code>: <code>true</code>|<code>false</code>
(default <code>false</code>)</li>
<li><code>ipad-orientation-lock</code>:
<code>portrait-only</code>|<code>landscape-only</code></li>
<li><code>iphone-orientation-lock</code>:
<code>portrait-only</code>|<code>landscape-only</code></li>
<li><code>binding</code>: <code>true</code>|<code>false</code> (default
<code>true</code>)</li>
<li><code>scroll-axis</code>:
<code>vertical</code>|<code>horizontal</code>|<code>default</code></li>
</ul>
</dd>
</dl>
<h2 id="the-epubtype-attribute">The <code>epub:type</code>
attribute</h2>
<p>For <code>epub3</code> output, you can mark up the heading that
corresponds to an EPUB chapter using the <a
href="http://www.idpf.org/epub/31/spec/epub-contentdocs.html#sec-epub-type-attribute"><code>epub:type</code>
attribute</a>. For example, to set the attribute to the value
<code>prologue</code>, use this markdown:</p>
<pre><code># My chapter {epub:type=prologue}</code></pre>
<p>Which will result in:</p>
<pre><code>&lt;body epub:type=&quot;frontmatter&quot;&gt;
  &lt;section epub:type=&quot;prologue&quot;&gt;
    &lt;h1&gt;My chapter&lt;/h1&gt;</code></pre>
<p>Pandoc will output <code>&lt;body epub:type="bodymatter"&gt;</code>,
unless you use one of the following values, in which case either
<code>frontmatter</code> or <code>backmatter</code> will be output.</p>
<table>
<thead>
<tr class="header">
<th><code>epub:type</code> of first section</th>
<th><code>epub:type</code> of body</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td>prologue</td>
<td>frontmatter</td>
</tr>
<tr class="even">
<td>abstract</td>
<td>frontmatter</td>
</tr>
<tr class="odd">
<td>acknowledgments</td>
<td>frontmatter</td>
</tr>
<tr class="even">
<td>copyright-page</td>
<td>frontmatter</td>
</tr>
<tr class="odd">
<td>dedication</td>
<td>frontmatter</td>
</tr>
<tr class="even">
<td>credits</td>
<td>frontmatter</td>
</tr>
<tr class="odd">
<td>keywords</td>
<td>frontmatter</td>
</tr>
<tr class="even">
<td>imprint</td>
<td>frontmatter</td>
</tr>
<tr class="odd">
<td>contributors</td>
<td>frontmatter</td>
</tr>
<tr class="even">
<td>other-credits</td>
<td>frontmatter</td>
</tr>
<tr class="odd">
<td>errata</td>
<td>frontmatter</td>
</tr>
<tr class="even">
<td>revision-history</td>
<td>frontmatter</td>
</tr>
<tr class="odd">
<td>titlepage</td>
<td>frontmatter</td>
</tr>
<tr class="even">
<td>halftitlepage</td>
<td>frontmatter</td>
</tr>
<tr class="odd">
<td>seriespage</td>
<td>frontmatter</td>
</tr>
<tr class="even">
<td>foreword</td>
<td>frontmatter</td>
</tr>
<tr class="odd">
<td>preface</td>
<td>frontmatter</td>
</tr>
<tr class="even">
<td>frontispiece</td>
<td>frontmatter</td>
</tr>
<tr class="odd">
<td>appendix</td>
<td>backmatter</td>
</tr>
<tr class="even">
<td>colophon</td>
<td>backmatter</td>
</tr>
<tr class="odd">
<td>bibliography</td>
<td>backmatter</td>
</tr>
<tr class="even">
<td>index</td>
<td>backmatter</td>
</tr>
</tbody>
</table>
<h2 id="linked-media">Linked media</h2>
<p>By default, pandoc will download media referenced from any
<code>&lt;img&gt;</code>, <code>&lt;audio&gt;</code>,
<code>&lt;video&gt;</code> or <code>&lt;source&gt;</code> element
present in the generated EPUB, and include it in the EPUB container,
yielding a completely self-contained EPUB. If you want to link to
external media resources instead, use raw HTML in your source and add
<code>data-external="1"</code> to the tag with the <code>src</code>
attribute. For example:</p>
<pre><code>&lt;audio controls=&quot;1&quot;&gt;
  &lt;source src=&quot;https://example.com/music/toccata.mp3&quot;
          data-external=&quot;1&quot; type=&quot;audio/mpeg&quot;&gt;
  &lt;/source&gt;
&lt;/audio&gt;</code></pre>
<p>If the input format already is HTML then
<code>data-external="1"</code> will work as expected for
<code>&lt;img&gt;</code> elements. Similarly, for Markdown, external
images can be declared with <code>![img](url){external=1}</code>. Note
that this only works for images; the other media elements have no native
representation in pandoc’s AST and require the use of raw HTML.</p>
<h2 id="epub-styling">EPUB styling</h2>
<p>By default, pandoc will include some basic styling contained in its
<code>epub.css</code> data file. (To see this, use
<code>pandoc --print-default-data-file epub.css</code>.) To use a
different CSS file, just use the <code>--css</code> command line option.
A few inline styles are defined in addition; these are essential for
correct formatting of pandoc’s HTML output.</p>
<p>The <code>document-css</code> variable may be set if the more
opinionated styling of pandoc’s default HTML templates is desired (and
in that case the variables defined in <a
href="#variables-for-html">Variables for HTML</a> may be used to
fine-tune the style).</p>
<h1 id="chunked-html">Chunked HTML</h1>
<p><code>pandoc -t chunkedhtml</code> will produce a zip archive of
linked HTML files, one for each section of the original document.
Internal links will automatically be adjusted to point to the right
place, images linked to under the working directory will be
incorporated, and navigation links will be added. In addition, a JSON
file <code>sitemap.json</code> will be included describing the
hierarchical structure of the files.</p>
<p>If an output file without an extension is specified, then it will be
interpreted as a directory and the zip archive will be automatically
unpacked into it (unless it already exists, in which case an error will
be raised). Otherwise a <code>.zip</code> file will be produced.</p>
<p>The navigation links can be customized by adjusting the template. By
default, a table of contents is included only on the top page. To
include it on every page, set the <code>toc</code> variable
manually.</p>
<h1 id="jupyter-notebooks">Jupyter notebooks</h1>
<p>When creating a <a
href="https://nbformat.readthedocs.io/en/latest/">Jupyter notebook</a>,
pandoc will try to infer the notebook structure. Code blocks with the
class <code>code</code> will be taken as code cells, and intervening
content will be taken as Markdown cells. Attachments will automatically
be created for images in Markdown cells. Metadata will be taken from the
<code>jupyter</code> metadata field. For example:</p>
<pre><code>---
title: My notebook
jupyter:
  nbformat: 4
  nbformat_minor: 5
  kernelspec:
     display_name: Python 2
     language: python
     name: python2
  language_info:
     codemirror_mode:
       name: ipython
       version: 2
     file_extension: &quot;.py&quot;
     mimetype: &quot;text/x-python&quot;
     name: &quot;python&quot;
     nbconvert_exporter: &quot;python&quot;
     pygments_lexer: &quot;ipython2&quot;
     version: &quot;2.7.15&quot;
---

# Lorem ipsum

**Lorem ipsum** dolor sit amet, consectetur adipiscing elit. Nunc luctus
bibendum felis dictum sodales.

``` code
print(&quot;hello&quot;)
```

## Pyout

``` code
from IPython.display import HTML
HTML(&quot;&quot;&quot;
&lt;script&gt;
console.log(&quot;hello&quot;);
&lt;/script&gt;
&lt;b&gt;HTML&lt;/b&gt;
&quot;&quot;&quot;)
```

## Image

This image ![image](myimage.png) will be
included as a cell attachment.</code></pre>
<p>If you want to add cell attributes, group cells differently, or add
output to code cells, then you need to include divs to indicate the
structure. You can use either <a href="#extension-fenced_divs">fenced
divs</a> or <a href="#extension-native_divs">native divs</a> for this.
Here is an example:</p>
<pre><code>:::::: {.cell .markdown}
# Lorem

**Lorem ipsum** dolor sit amet, consectetur adipiscing elit. Nunc luctus
bibendum felis dictum sodales.
::::::

:::::: {.cell .code execution_count=1}
``` {.python}
print(&quot;hello&quot;)
```

::: {.output .stream .stdout}
```
hello
```
:::
::::::

:::::: {.cell .code execution_count=2}
``` {.python}
from IPython.display import HTML
HTML(&quot;&quot;&quot;
&lt;script&gt;
console.log(&quot;hello&quot;);
&lt;/script&gt;
&lt;b&gt;HTML&lt;/b&gt;
&quot;&quot;&quot;)
```

::: {.output .execute_result execution_count=2}
```{=html}
&lt;script&gt;
console.log(&quot;hello&quot;);
&lt;/script&gt;
&lt;b&gt;HTML&lt;/b&gt;
hello
```
:::
::::::</code></pre>
<p>If you include raw HTML or TeX in an output cell, use the <a
href="#extension-raw_attribute">raw attribute</a>, as shown in the last
cell of the example above. Although pandoc can process “bare” raw HTML
and TeX, the result is often interspersed raw elements and normal
textual elements, and in an output cell pandoc expects a single,
connected raw block. To avoid using raw HTML or TeX except when marked
explicitly using raw attributes, we recommend specifying the extensions
<code>-raw_html-raw_tex+raw_attribute</code> when translating between
Markdown and ipynb notebooks.</p>
<p>Note that options and extensions that affect reading and writing of
Markdown will also affect Markdown cells in ipynb notebooks. For
example, <code>--wrap=preserve</code> will preserve soft line breaks in
Markdown cells; <code>--markdown-headings=setext</code> will cause
Setext-style headings to be used; and <code>--preserve-tabs</code> will
prevent tabs from being turned to spaces.</p>
<h1 id="syntax-highlighting">Syntax highlighting</h1>
<p>Pandoc will automatically highlight syntax in <a
href="#fenced-code-blocks">fenced code blocks</a> that are marked with a
language name. The Haskell library <a
href="https://github.com/jgm/skylighting">skylighting</a> is used for
highlighting. Currently highlighting is supported only for HTML, EPUB,
Docx, Ms, and LaTeX/PDF output. To see a list of language names that
pandoc will recognize, type
<code>pandoc --list-highlight-languages</code>.</p>
<p>The color scheme can be selected using the
<code>--highlight-style</code> option. The default color scheme is
<code>pygments</code>, which imitates the default color scheme used by
the Python library pygments (though pygments is not actually used to do
the highlighting). To see a list of highlight styles, type
<code>pandoc --list-highlight-styles</code>.</p>
<p>If you are not satisfied with the predefined styles, you can use
<code>--print-highlight-style</code> to generate a JSON
<code>.theme</code> file which can be modified and used as the argument
to <code>--highlight-style</code>. To get a JSON version of the
<code>pygments</code> style, for example:</p>
<pre><code>pandoc --print-highlight-style pygments &gt; my.theme</code></pre>
<p>Then edit <code>my.theme</code> and use it like this:</p>
<pre><code>pandoc --highlight-style my.theme</code></pre>
<p>If you are not satisfied with the built-in highlighting, or you want
to highlight a language that isn’t supported, you can use the
<code>--syntax-definition</code> option to load a <a
href="https://docs.kde.org/stable5/en/kate/katepart/highlight.html">KDE-style
XML syntax definition file</a>. Before writing your own, have a look at
KDE’s <a
href="https://github.com/KDE/syntax-highlighting/tree/master/data/syntax">repository
of syntax definitions</a>.</p>
<p>To disable highlighting, use the <code>--no-highlight</code>
option.</p>
<h1 id="custom-styles">Custom Styles</h1>
<p>Custom styles can be used in the docx and ICML formats.</p>
<h2 id="output">Output</h2>
<p>By default, pandoc’s docx and ICML output applies a predefined set of
styles for blocks such as paragraphs and block quotes, and uses largely
default formatting (italics, bold) for inlines. This will work for most
purposes, especially alongside a <code>reference.docx</code> file.
However, if you need to apply your own styles to blocks, or match a
preexisting set of styles, pandoc allows you to define custom styles for
blocks and text using <code>div</code>s and <code>span</code>s,
respectively.</p>
<p>If you define a <code>div</code> or <code>span</code> with the
attribute <code>custom-style</code>, pandoc will apply your specified
style to the contained elements (with the exception of elements whose
function depends on a style, like headings, code blocks, block quotes,
or links). So, for example, using the <code>bracketed_spans</code>
syntax,</p>
<pre><code>[Get out]{custom-style=&quot;Emphatically&quot;}, he said.</code></pre>
<p>would produce a docx file with “Get out” styled with character style
<code>Emphatically</code>. Similarly, using the <code>fenced_divs</code>
syntax,</p>
<pre><code>Dickinson starts the poem simply:

::: {custom-style=&quot;Poetry&quot;}
| A Bird came down the Walk---
| He did not know I saw---
:::</code></pre>
<p>would style the two contained lines with the <code>Poetry</code>
paragraph style.</p>
<p>For docx output, styles will be defined in the output file as
inheriting from normal text, if the styles are not yet in your
reference.docx. If they are already defined, pandoc will not alter the
definition.</p>
<p>This feature allows for greatest customization in conjunction with <a
href="https://pandoc.org/filters.html">pandoc filters</a>. If you want
all paragraphs after block quotes to be indented, you can write a filter
to apply the styles necessary. If you want all italics to be transformed
to the <code>Emphasis</code> character style (perhaps to change their
color), you can write a filter which will transform all italicized
inlines to inlines within an <code>Emphasis</code> custom-style
<code>span</code>.</p>
<p>For docx output, you don’t need to enable any extensions for custom
styles to work.</p>
<h2 id="input">Input</h2>
<p>The docx reader, by default, only reads those styles that it can
convert into pandoc elements, either by direct conversion or
interpreting the derivation of the input document’s styles.</p>
<p>By enabling the <a href="#ext-styles"><code>styles</code>
extension</a> in the docx reader (<code>-f docx+styles</code>), you can
produce output that maintains the styles of the input document, using
the <code>custom-style</code> class. Paragraph styles are interpreted as
divs, while character styles are interpreted as spans.</p>
<p>For example, using the <code>custom-style-reference.docx</code> file
in the test directory, we have the following different outputs:</p>
<p>Without the <code>+styles</code> extension:</p>
<pre><code>$ pandoc test/docx/custom-style-reference.docx -f docx -t markdown
This is some text.

This is text with an *emphasized* text style. And this is text with a
**strengthened** text style.

&gt; Here is a styled paragraph that inherits from Block Text.</code></pre>
<p>And with the extension:</p>
<pre><code>$ pandoc test/docx/custom-style-reference.docx -f docx+styles -t markdown

::: {custom-style=&quot;First Paragraph&quot;}
This is some text.
:::

::: {custom-style=&quot;Body Text&quot;}
This is text with an [emphasized]{custom-style=&quot;Emphatic&quot;} text style.
And this is text with a [strengthened]{custom-style=&quot;Strengthened&quot;}
text style.
:::

::: {custom-style=&quot;My Block Style&quot;}
&gt; Here is a styled paragraph that inherits from Block Text.
:::</code></pre>
<p>With these custom styles, you can use your input document as a
reference-doc while creating docx output (see below), and maintain the
same styles in your input and output files.</p>
<h1 id="custom-readers-and-writers">Custom readers and writers</h1>
<p>Pandoc can be extended with custom readers and writers written in <a
href="https://www.lua.org">Lua</a>. (Pandoc includes a Lua interpreter,
so Lua need not be installed separately.)</p>
<p>To use a custom reader or writer, simply specify the path to the Lua
script in place of the input or output format. For example:</p>
<pre><code>pandoc -t data/sample.lua
pandoc -f my_custom_markup_language.lua -t latex -s</code></pre>
<p>If the script is not found relative to the working directory, it will
be sought in the <code>custom</code> subdirectory of the user data
directory (see <code>--data-dir</code>).</p>
<p>A custom reader is a Lua script that defines one function, Reader,
which takes a string as input and returns a Pandoc AST. See the <a
href="https://pandoc.org/lua-filters.html">Lua filters documentation</a>
for documentation of the functions that are available for creating
pandoc AST elements. For parsing, the <a
href="http://www.inf.puc-rio.br/~roberto/lpeg/">lpeg</a> parsing library
is available by default. To see a sample custom reader:</p>
<pre><code>pandoc --print-default-data-file creole.lua</code></pre>
<p>If you want your custom reader to have access to reader options
(e.g. the tab stop setting), you give your Reader function a second
<code>options</code> parameter.</p>
<p>A custom writer is a Lua script that defines a function that
specifies how to render each element in a Pandoc AST. See the <a
href="https://github.com/jgm/djot.lua/blob/main/djot-writer.lua">djot-writer.lua</a>
for a full-featured example.</p>
<p>Note that custom writers have no default template. If you want to use
<code>--standalone</code> with a custom writer, you will need to specify
a template manually using <code>--template</code> or add a new default
template with the name <code>default.NAME_OF_CUSTOM_WRITER.lua</code> to
the <code>templates</code> subdirectory of your user data directory (see
<a href="#templates">Templates</a>).</p>
<h1 id="reproducible-builds">Reproducible builds</h1>
<p>Some of the document formats pandoc targets (such as EPUB, docx, and
ODT) include build timestamps in the generated document. That means that
the files generated on successive builds will differ, even if the source
does not. To avoid this, set the <code>SOURCE_DATE_EPOCH</code>
environment variable, and the timestamp will be taken from it instead of
the current time. <code>SOURCE_DATE_EPOCH</code> should contain an
integer unix timestamp (specifying the number of seconds since midnight
UTC January 1, 1970).</p>
<p>Some document formats also include a unique identifier. For EPUB,
this can be set explicitly by setting the <code>identifier</code>
metadata field (see <a href="#epub-metadata">EPUB Metadata</a>,
above).</p>
<h1 id="accessible-pdfs-and-pdf-archiving-standards">Accessible PDFs and
PDF archiving standards</h1>
<p>PDF is a flexible format, and using PDF in certain contexts requires
additional conventions. For example, PDFs are not accessible by default;
they define how characters are placed on a page but do not contain
semantic information on the content. However, it is possible to generate
accessible PDFs, which use tagging to add semantic information to the
document.</p>
<p>Pandoc defaults to LaTeX to generate PDF. Tagging support in LaTeX is
in development and not readily available, so PDFs generated in this way
will always be untagged and not accessible. This means that alternative
engines must be used to generate accessible PDFs.</p>
<p>The PDF standards PDF/A and PDF/UA define further restrictions
intended to optimize PDFs for archiving and accessibility. Tagging is
commonly used in combination with these standards to ensure best
results.</p>
<p>Note, however, that standard compliance depends on many things,
including the colorspace of embedded images. Pandoc cannot check this,
and external programs must be used to ensure that generated PDFs are in
compliance.</p>
<h2 id="context">ConTeXt</h2>
<p>ConTeXt always produces tagged PDFs, but the quality depends on the
input. The default ConTeXt markup generated by pandoc is optimized for
readability and reuse, not tagging. Enable the <a
href="#extension--tagging"><code>tagging</code></a> format extension to
force markup that is optimized for tagging. This can be combined with
the <code>pdfa</code> variable to generate standard-compliant PDFs.
E.g.:</p>
<pre><code>pandoc --to=context+tagging -V pdfa=3a</code></pre>
<p>A recent <code>context</code> version should be used, as older
versions contained a bug that lead to invalid PDF metadata.</p>
<h2 id="weasyprint">WeasyPrint</h2>
<p>The HTML-based engine WeasyPrint includes experimental support for
PDF/A and PDF/UA since version 57. Tagged PDFs can created with</p>
<pre><code>pandoc --pdf-engine=weasyprint \
       --pdf-engine-opt=--pdf-variant=pdf/ua-1 ...</code></pre>
<p>The feature is experimental and standard compliance should not be
assumed.</p>
<h2 id="prince-xml">Prince XML</h2>
<p>The non-free HTML-to-PDf converter <code>prince</code> has extensive
support for various PDF standards as well as tagging. E.g.:</p>
<pre><code>pandoc --pdf-engine=prince \
       --pdf-engine-opt=--tagged-pdf ...</code></pre>
<p>See the prince documentation for more info.</p>
<h2 id="word-processors">Word Processors</h2>
<p>Word processors like LibreOffice and MS Word can also be used to
generate standardized and tagged PDF output. Pandoc does not support
direct conversions via these tools. However, pandoc can convert a
document to a <code>docx</code> or <code>odt</code> file, which can then
be opened and converted to PDF with the respective word processor. See
the documentation for <a
href="https://support.microsoft.com/en-us/office/create-accessible-pdfs-064625e0-56ea-4e16-ad71-3aa33bb4b7ed">Word</a>
and <a
href="https://help.libreoffice.org/7.1/en-US/text/shared/01/ref_pdf_export_general.html">LibreOffice</a>.</p>
<h1 id="running-pandoc-as-a-web-server">Running pandoc as a web
server</h1>
<p>If you rename (or symlink) the pandoc executable to
<code>pandoc-server</code>, or if you call pandoc with
<code>server</code> as the first argument, it will start up a web server
with a JSON API. This server exposes most of the conversion
functionality of pandoc. For full documentation, see the <a
href="https://github.com/jgm/pandoc/blob/master/doc/pandoc-server.md">pandoc-server</a>
man page.</p>
<p>If you rename (or symlink) the pandoc executable to
<code>pandoc-server.cgi</code>, it will function as a CGI program
exposing the same API as <code>pandoc-server</code>.</p>
<p><code>pandoc-server</code> is designed to be maximally secure; it
uses Haskell’s type system to provide strong guarantees that no I/O will
be performed on the server during pandoc conversions.</p>
<h1 id="running-pandoc-as-a-lua-interpreter">Running pandoc as a Lua
interpreter</h1>
<p>Calling the pandoc executable under the name <code>pandoc-lua</code>
or with <code>lua</code> as the first argument will make it function as
a standalone Lua interpreter. The behavior is mostly identical to that
of the <a href="https://www.lua.org/manual/5.4/manual.html#7">standalone
<code>lua</code> executable</a>, version 5.4. However, there is no REPL
yet, and the <code>-i</code> option has no effect. For full
documentation, see the <a
href="https://github.com/jgm/pandoc/blob/master/doc/pandoc-lua.md">pandoc-lua</a>
man page.</p>
<h1 id="a-note-on-security">A note on security</h1>
<ol type="1">
<li><p>Although pandoc itself will not create or modify any files other
than those you explicitly ask it create (with the exception of temporary
files used in producing PDFs), a filter or custom writer could in
principle do anything on your file system. Please audit filters and
custom writers very carefully before using them.</p></li>
<li><p>Several input formats (including HTML, Org, and RST) support
<code>include</code> directives that allow the contents of a file to be
included in the output. An untrusted attacker could use these to view
the contents of files on the file system. (Using the
<code>--sandbox</code> option can protect against this threat.)</p></li>
<li><p>Several output formats (including RTF, FB2, HTML with
<code>--self-contained</code>, EPUB, Docx, and ODT) will embed encoded
or raw images into the output file. An untrusted attacker could exploit
this to view the contents of non-image files on the file system. (Using
the <code>--sandbox</code> option can protect against this threat, but
will also prevent including images in these formats.)</p></li>
<li><p>If your application uses pandoc as a Haskell library (rather than
shelling out to the executable), it is possible to use it in a mode that
fully isolates pandoc from your file system, by running the pandoc
operations in the <code>PandocPure</code> monad. See the document <a
href="https://pandoc.org/using-the-pandoc-api.html">Using the pandoc
API</a> for more details. (This corresponds to the use of the
<code>--sandbox</code> option on the command line.)</p></li>
<li><p>Pandoc’s parsers can exhibit pathological performance on some
corner cases. It is wise to put any pandoc operations under a timeout,
to avoid DOS attacks that exploit these issues. If you are using the
pandoc executable, you can add the command line options
<code>+RTS -M512M -RTS</code> (for example) to limit the heap size to
512MB. Note that the <code>commonmark</code> parser (including
<code>commonmark_x</code> and <code>gfm</code>) is much less vulnerable
to pathological performance than the <code>markdown</code> parser, so it
is a better choice when processing untrusted input.</p></li>
<li><p>The HTML generated by pandoc is not guaranteed to be safe. If
<code>raw_html</code> is enabled for the Markdown input, users can
inject arbitrary HTML. Even if <code>raw_html</code> is disabled, users
can include dangerous content in URLs and attributes. To be safe, you
should run all HTML generated from untrusted user input through an HTML
sanitizer.</p></li>
</ol>
<h1 id="authors">Authors</h1>
<p>Copyright 2006–2022 John MacFarlane (<EMAIL>). Released
under the <a href="https://www.gnu.org/copyleft/gpl.html"
title="GNU General Public License">GPL</a>, version 2 or greater. This
software carries no warranty of any kind. (See COPYRIGHT for full
copyright and warranty notices.) For a full list of contributors, see
the file AUTHORS.md in the pandoc source code.</p>
<aside id="footnotes" class="footnotes footnotes-end-of-document"
role="doc-endnotes">
<hr />
<ol>
<li id="fn1"><p>The point of this rule is to ensure that normal
paragraphs starting with people’s initials, like</p>
<pre><code>B. Russell won a Nobel Prize (but not for &quot;On Denoting&quot;).</code></pre>
<p>do not get treated as list items.</p>
<p>This rule will not prevent</p>
<pre><code>(C) 2007 Joe Smith</code></pre>
<p>from being interpreted as a list item. In this case, a backslash
escape can be used:</p>
<pre><code>(C\) 2007 Joe Smith</code></pre>
<a href="#fnref1" class="footnote-back" role="doc-backlink">↩︎</a></li>
<li id="fn2"><p>I have been influenced by the suggestions of <a
href="https://justatheory.com/2009/02/modest-markdown-proposal/">David
Wheeler</a>.<a href="#fnref2" class="footnote-back"
role="doc-backlink">↩︎</a></p></li>
<li id="fn3"><p>This scheme is due to Michel Fortin, who proposed it on
the <a
href="http://six.pairlist.net/pipermail/markdown-discuss/2005-March/001097.html">Markdown
discussion list</a>.<a href="#fnref3" class="footnote-back"
role="doc-backlink">↩︎</a></p></li>
<li id="fn4"><p>To see why laziness is incompatible with relaxing the
requirement of a blank line between items, consider the following
example:</p>
<pre><code>bar
:    definition
foo
:    definition</code></pre>
<p>Is this a single list item with two definitions of “bar,” the first
of which is lazily wrapped, or two list items? To remove the ambiguity
we must either disallow lazy wrapping or require a blank line between
list items.<a href="#fnref4" class="footnote-back"
role="doc-backlink">↩︎</a></p></li>
</ol>
</aside>
</body>
</html>
