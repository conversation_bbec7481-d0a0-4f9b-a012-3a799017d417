"""
測試 EPUB → PDF 完整修正
驗證是否解決了 PDF 只有一頁內容的問題
"""

import zipfile
from pathlib import Path


def create_test_epub():
    """建立測試 EPUB"""
    epub_path = "pdf_complete_test.epub"
    
    files = {
        'mimetype': 'application/epub+zip',
        'META-INF/container.xml': '''<?xml version="1.0"?>
<container version="1.0" xmlns="urn:oasis:names:tc:opendocument:xmlns:container">
  <rootfiles>
    <rootfile full-path="content.opf" media-type="application/oebps-package+xml"/>
  </rootfiles>
</container>''',
        'content.opf': '''<?xml version="1.0" encoding="UTF-8"?>
<package xmlns="http://www.idpf.org/2007/opf" unique-identifier="BookId" version="2.0">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <dc:title>PDF 完整測試</dc:title>
    <dc:creator>測試工具</dc:creator>
  </metadata>
  <manifest>
    <item id="ch1" href="chapter1.html" media-type="application/xhtml+xml"/>
    <item id="ch2" href="chapter2.html" media-type="application/xhtml+xml"/>
    <item id="ch3" href="chapter3.html" media-type="application/xhtml+xml"/>
  </manifest>
  <spine>
    <itemref idref="ch1"/>
    <itemref idref="ch2"/>
    <itemref idref="ch3"/>
  </spine>
</package>''',
        'chapter1.html': '''<!DOCTYPE html>
<html><head><title>第一章</title></head>
<body>
    <h1>第一章：開始</h1>
    <p>這是第一章內容。PDF測試標記：第一章</p>
</body></html>''',
        'chapter2.html': '''<!DOCTYPE html>
<html><head><title>第二章</title></head>
<body>
    <h1>第二章：發展</h1>
    <p>這是第二章內容。PDF測試標記：第二章</p>
</body></html>''',
        'chapter3.html': '''<!DOCTYPE html>
<html><head><title>第三章</title></head>
<body>
    <h1>第三章：結束</h1>
    <p>這是第三章內容。PDF測試標記：第三章</p>
</body></html>'''
    }
    
    with zipfile.ZipFile(epub_path, 'w', zipfile.ZIP_DEFLATED) as epub_zip:
        for file_path, content in files.items():
            epub_zip.writestr(file_path, content.encode('utf-8'))
    
    return epub_path


def test_txt_vs_pdf():
    """比較 TXT 和 PDF 轉換結果"""
    print("🧪 比較 EPUB → TXT vs EPUB → PDF")
    print("=" * 50)
    
    epub_file = create_test_epub()
    print(f"✅ 建立測試 EPUB: {epub_file}")
    
    try:
        from main import EbookConverter, ConversionStatus
        
        converter = EbookConverter()
        
        # 測試 TXT 轉換
        print(f"\n📝 測試 EPUB → TXT:")
        txt_output = "test_output.txt"
        
        def txt_callback(status, message):
            print(f"  📊 {status.value}: {message}")
        
        txt_success = converter._extract_epub_text(epub_file, txt_output, 'txt', txt_callback)
        
        txt_content = ""
        if txt_success and Path(txt_output).exists():
            with open(txt_output, 'r', encoding='utf-8') as f:
                txt_content = f.read()
            print(f"  ✅ TXT 成功: {len(txt_content)} 字符")
        else:
            print(f"  ❌ TXT 失敗")
        
        # 測試 PDF 轉換
        print(f"\n📄 測試 EPUB → PDF:")
        pdf_output = "test_output.pdf"
        
        def pdf_callback(status, message):
            print(f"  📊 {status.value}: {message}")
        
        pdf_success = converter._convert_epub_to_pdf(epub_file, pdf_output, pdf_callback)
        
        pdf_size = 0
        if pdf_success and Path(pdf_output).exists():
            pdf_size = Path(pdf_output).stat().st_size
            print(f"  ✅ PDF 成功: {pdf_size} bytes")
        else:
            print(f"  ❌ PDF 失敗")
        
        # 檢查內容完整性
        print(f"\n🔍 內容完整性檢查:")
        
        markers = ["第一章", "第二章", "第三章"]
        
        txt_markers = [m for m in markers if m in txt_content]
        print(f"  TXT 包含章節: {len(txt_markers)}/3 ({txt_markers})")
        
        # 分析結果
        print(f"\n📊 結果分析:")
        print(f"  TXT 轉換: {'✅ 成功' if txt_success else '❌ 失敗'}")
        print(f"  PDF 轉換: {'✅ 成功' if pdf_success else '❌ 失敗'}")
        print(f"  TXT 章節: {len(txt_markers)}/3")
        
        if txt_success and len(txt_markers) == 3:
            print(f"  ✅ TXT 包含完整內容（基準正常）")
        else:
            print(f"  ⚠️ TXT 內容不完整（基準有問題）")
        
        if pdf_success:
            print(f"  ✅ PDF 生成成功")
            print(f"  💡 請手動檢查 PDF 是否包含所有 3 章內容")
        else:
            print(f"  ❌ PDF 生成失敗")
        
        return txt_success, pdf_success, len(txt_markers)
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False, False, 0
    
    finally:
        # 清理
        for file in [epub_file, "test_output.txt", "test_output.pdf"]:
            if Path(file).exists():
                Path(file).unlink()


def main():
    """主程式"""
    print("🔍 EPUB → PDF 完整修正驗證")
    print("=" * 60)
    print("比較 TXT 和 PDF 轉換，確認 PDF 問題是否解決")
    print()
    
    txt_ok, pdf_ok, txt_chapters = test_txt_vs_pdf()
    
    print(f"\n📊 最終結果:")
    print("=" * 30)
    
    if txt_ok and txt_chapters == 3 and pdf_ok:
        print("🎉 完美！PDF 問題已解決")
        print("💡 現在您可以:")
        print("  • 直接使用 EPUB → PDF 轉換")
        print("  • 獲得包含完整內容的 PDF")
        print("  • 重新啟動程式測試您的檔案")
        
    elif txt_ok and txt_chapters == 3 and not pdf_ok:
        print("⚠️ TXT 正常但 PDF 仍有問題")
        print("💡 建議:")
        print("  • 檢查 ReportLab 安裝")
        print("  • 或使用 EPUB → TXT，再手動轉 PDF")
        
    elif not txt_ok or txt_chapters < 3:
        print("❌ 基本的 EPUB 解析有問題")
        print("💡 需要:")
        print("  • 檢查 EPUB 檔案格式")
        print("  • 或使用其他轉換工具")
        
    else:
        print("⚠️ 結果不明確")
        print("💡 建議手動測試和檢查")


if __name__ == '__main__':
    main()
