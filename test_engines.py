"""
測試多轉檔引擎功能
驗證不同轉檔引擎的可用性和功能
"""

from main import EbookConverter, ConversionStatus
from pathlib import Path
import time


def test_engine_detection():
    """測試轉檔引擎檢測"""
    print("🔍 檢測可用的轉檔引擎...")
    print("-" * 40)
    
    converter = EbookConverter()
    engines = converter.available_engines
    
    for engine_name, available in engines.items():
        status = "✅ 可用" if available else "❌ 不可用"
        engine_display = {
            'calibre': 'Calibre (最佳)',
            'pandoc': 'Pandoc (良好)', 
            'python_libs': 'Python 庫 (基本)'
        }.get(engine_name, engine_name)
        
        print(f"  {engine_display}: {status}")
    
    available_count = sum(engines.values())
    print(f"\n📊 總計: {available_count}/3 個引擎可用")
    
    return available_count > 0


def test_conversion_with_callback():
    """測試轉檔功能（帶回調）"""
    print("\n🔄 測試轉檔功能...")
    print("-" * 40)
    
    # 檢查測試檔案
    input_file = "test_sample.txt"
    if not Path(input_file).exists():
        print(f"❌ 測試檔案 {input_file} 不存在")
        return False
    
    output_file = "test_sample_converted.html"
    
    # 建立轉換器
    converter = EbookConverter()
    
    # 狀態追蹤
    conversion_status = {"completed": False, "success": False, "message": ""}
    
    def progress_callback(status: ConversionStatus, message: str):
        """進度回調函數"""
        print(f"  狀態: {status.value} - {message}")
        if status in [ConversionStatus.SUCCESS, ConversionStatus.FAILED]:
            conversion_status["completed"] = True
            conversion_status["success"] = (status == ConversionStatus.SUCCESS)
            conversion_status["message"] = message
    
    # 開始轉檔
    print(f"輸入檔案: {input_file}")
    print(f"輸出檔案: {output_file}")
    
    converter.convert_ebook_async(input_file, output_file, progress_callback)
    
    # 等待完成
    timeout = 30  # 30 秒超時
    start_time = time.time()
    
    while not conversion_status["completed"] and (time.time() - start_time) < timeout:
        time.sleep(0.5)
    
    if conversion_status["completed"]:
        if conversion_status["success"]:
            print(f"✅ 轉檔成功: {conversion_status['message']}")
            
            # 檢查輸出檔案
            if Path(output_file).exists():
                file_size = Path(output_file).stat().st_size
                print(f"📄 輸出檔案大小: {file_size} bytes")
                return True
            else:
                print("❌ 輸出檔案未生成")
                return False
        else:
            print(f"❌ 轉檔失敗: {conversion_status['message']}")
            return False
    else:
        print("⏰ 轉檔超時")
        return False


def test_engine_priority():
    """測試引擎優先順序"""
    print("\n🎯 測試引擎優先順序...")
    print("-" * 40)
    
    converter = EbookConverter()
    engines = converter.available_engines
    
    print("引擎優先順序:")
    print("1. Calibre (最完整的功能)")
    print("2. Pandoc (良好的格式支援)")
    print("3. Python 庫 (基本轉換)")
    
    print("\n當前可用引擎:")
    priority_order = ['calibre', 'pandoc', 'python_libs']
    
    for i, engine in enumerate(priority_order, 1):
        available = engines.get(engine, False)
        status = "✅ 將被使用" if available else "❌ 跳過"
        print(f"{i}. {engine}: {status}")
        
        if available:
            print(f"   → 轉檔時會優先使用此引擎")
            break
    
    return True


def test_format_support():
    """測試格式支援"""
    print("\n📋 測試格式支援...")
    print("-" * 40)
    
    converter = EbookConverter()
    
    # 測試不同格式的支援情況
    test_cases = [
        ("txt", "html", "TXT → HTML"),
        ("html", "txt", "HTML → TXT"),
        ("txt", "pdf", "TXT → PDF (需要 Calibre/Pandoc)"),
        ("epub", "pdf", "EPUB → PDF (需要 Calibre)"),
    ]
    
    print("格式支援測試:")
    for input_fmt, output_fmt, description in test_cases:
        # 這裡只是示意，實際支援取決於可用的引擎
        if converter.available_engines.get('calibre'):
            support = "✅ 支援 (Calibre)"
        elif converter.available_engines.get('pandoc') and input_fmt in ['txt', 'html', 'epub'] and output_fmt in ['txt', 'html', 'pdf']:
            support = "✅ 支援 (Pandoc)"
        elif converter.available_engines.get('python_libs') and input_fmt in ['txt', 'html'] and output_fmt in ['txt', 'html']:
            support = "✅ 支援 (Python)"
        else:
            support = "❌ 不支援"
        
        print(f"  {description}: {support}")
    
    return True


def main():
    """主測試程式"""
    print("🧪 電子書轉檔工具 - 多引擎測試")
    print("=" * 50)
    
    tests = [
        ("引擎檢測", test_engine_detection),
        ("轉檔功能", test_conversion_with_callback),
        ("引擎優先順序", test_engine_priority),
        ("格式支援", test_format_support),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔬 執行測試: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 測試通過")
            else:
                print(f"❌ {test_name} 測試失敗")
        except Exception as e:
            print(f"❌ {test_name} 測試例外: {e}")
    
    print(f"\n📊 測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！多引擎功能正常運作")
    elif passed > 0:
        print("⚠️ 部分測試通過，基本功能可用")
    else:
        print("❌ 所有測試失敗，請檢查安裝")
    
    print("\n💡 使用建議:")
    if passed > 0:
        print("  • 可以正常使用電子書轉檔工具")
        print("  • 程式會自動選擇最佳的轉檔引擎")
        print("  • 如需更多格式支援，建議安裝 Calibre")
    else:
        print("  • 請執行 python install_alternatives.py 安裝替代工具")
        print("  • 或手動安裝 Pandoc 和相關 Python 庫")


if __name__ == '__main__':
    main()
