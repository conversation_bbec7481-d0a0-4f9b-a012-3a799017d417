"""
最終測試 EPUB → PDF 一次轉檔完成
"""

from main import EbookConverter, ConversionStatus
from pathlib import Path
import zipfile
import time


def create_final_test_epub():
    """建立最終測試 EPUB"""
    print("📚 建立最終測試 EPUB...")
    
    epub_path = "final_test.epub"
    
    files = {
        'mimetype': 'application/epub+zip',
        'META-INF/container.xml': '''<?xml version="1.0"?>
<container version="1.0" xmlns="urn:oasis:names:tc:opendocument:xmlns:container">
  <rootfiles>
    <rootfile full-path="content.opf" media-type="application/oebps-package+xml"/>
  </rootfiles>
</container>''',
        'content.opf': '''<?xml version="1.0" encoding="UTF-8"?>
<package xmlns="http://www.idpf.org/2007/opf" unique-identifier="BookId" version="2.0">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <dc:title>🎉 一次轉檔完成測試</dc:title>
    <dc:creator>電子書轉檔工具</dc:creator>
  </metadata>
  <manifest>
    <item id="chapter1" href="chapter1.html" media-type="application/xhtml+xml"/>
  </manifest>
  <spine>
    <itemref idref="chapter1"/>
  </spine>
</package>''',
        'chapter1.html': '''<!DOCTYPE html>
<html>
<head>
    <title>一次轉檔完成</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>🎉 一次轉檔完成測試</h1>
    
    <h2>功能驗證</h2>
    <p>這個測試驗證以下功能：</p>
    <ul>
        <li>✅ EPUB 檔案解析</li>
        <li>✅ 文字內容提取</li>
        <li>✅ ReportLab PDF 生成</li>
        <li>✅ 完全自動化流程</li>
    </ul>
    
    <h2>轉換流程</h2>
    <ol>
        <li>選擇 EPUB 檔案</li>
        <li>選擇 PDF 輸出格式</li>
        <li>點擊「開始轉檔」</li>
        <li>等待完成 - 無需手動步驟！</li>
    </ol>
    
    <h2>技術特色</h2>
    <p><strong>使用 ReportLab 引擎：</strong></p>
    <ul>
        <li>純 Python 實現</li>
        <li>無需外部依賴</li>
        <li>高品質 PDF 輸出</li>
        <li>完全自動化</li>
    </ul>
    
    <p><em>如果您看到這個 PDF，表示一次轉檔功能已經成功實現！</em></p>
</body>
</html>'''
    }
    
    try:
        with zipfile.ZipFile(epub_path, 'w', zipfile.ZIP_DEFLATED) as epub_zip:
            for file_path, content in files.items():
                epub_zip.writestr(file_path, content.encode('utf-8'))
        
        print(f"✅ 建立最終測試 EPUB: {epub_path}")
        return epub_path
        
    except Exception as e:
        print(f"❌ 建立 EPUB 失敗: {e}")
        return None


def test_one_click_conversion():
    """測試一次轉檔完成"""
    print("\n🚀 測試一次轉檔完成功能")
    print("-" * 50)
    
    # 建立測試檔案
    epub_file = create_final_test_epub()
    if not epub_file:
        return False
    
    output_file = "final_test_output.pdf"
    
    # 檢查引擎狀態
    converter = EbookConverter()
    engines = converter.available_engines
    
    print("🔍 可用引擎:")
    for name, available in engines.items():
        status = "✅" if available else "❌"
        print(f"  {name}: {status}")
    
    # 執行轉換
    print(f"\n🔄 開始轉換: {epub_file} → {output_file}")
    
    result = {"completed": False, "success": False, "message": ""}
    
    def callback(status: ConversionStatus, message: str):
        print(f"  📊 {status.value}: {message}")
        if status in [ConversionStatus.SUCCESS, ConversionStatus.FAILED]:
            result["completed"] = True
            result["success"] = (status == ConversionStatus.SUCCESS)
            result["message"] = message
    
    # 開始轉換
    converter.convert_ebook_async(epub_file, output_file, callback)
    
    # 等待完成
    timeout = 30
    start_time = time.time()
    
    while not result["completed"] and (time.time() - start_time) < timeout:
        time.sleep(0.1)
    
    # 檢查結果
    if result["completed"] and result["success"]:
        if Path(output_file).exists():
            file_size = Path(output_file).stat().st_size
            print(f"\n🎉 一次轉檔完成！")
            print(f"   輸出檔案: {output_file}")
            print(f"   檔案大小: {file_size} bytes")
            
            # 檢查是否使用了 ReportLab
            if "ReportLab" in result["message"]:
                print(f"   使用引擎: ReportLab (完全自動化)")
                return True
            else:
                print(f"   使用引擎: 其他")
                return True
        else:
            print(f"\n❌ 轉換報告成功但檔案未生成")
            return False
    else:
        print(f"\n❌ 轉換失敗: {result.get('message', '未知錯誤')}")
        return False


def cleanup_test_files():
    """清理測試檔案"""
    print(f"\n🗑️ 清理測試檔案...")
    
    test_files = [
        'final_test.epub',
        'final_test_output.pdf'
    ]
    
    cleaned = 0
    for file in test_files:
        if Path(file).exists():
            Path(file).unlink()
            cleaned += 1
            print(f"  清理: {file}")
    
    print(f"清理了 {cleaned} 個檔案")


def main():
    """主程式"""
    print("🎯 最終測試：EPUB → PDF 一次轉檔完成")
    print("=" * 60)
    print("驗證 ReportLab 優先的完全自動化轉換")
    print()
    
    try:
        success = test_one_click_conversion()
        
        print(f"\n📊 最終測試結果:")
        print("=" * 30)
        
        if success:
            print("🎉 完美成功！一次轉檔完成功能已實現")
            print()
            print("💡 現在您可以:")
            print("1. 啟動程式: python main.py")
            print("2. 選擇任何 EPUB 檔案")
            print("3. 選擇 PDF 輸出格式")
            print("4. 點擊「開始轉檔」")
            print("5. 等待完成 - 完全自動化！")
            print()
            print("🌟 特色:")
            print("• 無需手動步驟")
            print("• 使用 ReportLab 引擎")
            print("• 高品質 PDF 輸出")
            print("• 完全自動化流程")
            print()
            print("🎯 問題完全解決！")
        else:
            print("❌ 測試失敗")
            print("可能需要檢查 ReportLab 安裝或程式碼")
        
        cleanup_test_files()
        
    except Exception as e:
        print(f"💥 測試過程中發生例外: {e}")
        cleanup_test_files()


if __name__ == '__main__':
    main()
