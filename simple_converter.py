"""
簡單電子書轉換器 - 內建版本
支援基本的文字格式轉換
"""

import re
from pathlib import Path

class SimpleConverter:
    """簡單轉換器"""
    
    @staticmethod
    def txt_to_html(input_file, output_file):
        """TXT 轉 HTML"""
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{Path(input_file).stem}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        pre {{ white-space: pre-wrap; }}
    </style>
</head>
<body>
    <h1>{Path(input_file).stem}</h1>
    <pre>{content}</pre>
</body>
</html>"""
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html)
    
    @staticmethod
    def html_to_txt(input_file, output_file):
        """HTML 轉 TXT"""
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 移除 HTML 標籤
        text = re.sub(r'<[^>]+>', '', content)
        text = re.sub(r'\s+', ' ', text).strip()
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(text)
    
    @staticmethod
    def txt_to_markdown(input_file, output_file):
        """TXT 轉 Markdown"""
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 簡單的 Markdown 格式化
        lines = content.split('\n')
        markdown_lines = []
        
        for line in lines:
            line = line.strip()
            if line:
                # 如果是標題（全大寫或特殊格式）
                if line.isupper() or line.startswith('第') and line.endswith('章'):
                    markdown_lines.append(f"# {line}")
                else:
                    markdown_lines.append(line)
            else:
                markdown_lines.append("")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(markdown_lines))

if __name__ == '__main__':
    print("簡單轉換器已準備就緒")
