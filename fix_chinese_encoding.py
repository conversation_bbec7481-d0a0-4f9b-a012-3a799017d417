"""
修正中文亂碼問題
專門解決 PDF 中文顯示問題
"""

import os
from pathlib import Path


def test_chinese_fonts():
    """測試中文字體可用性"""
    print("🔤 測試中文字體可用性")
    print("-" * 40)
    
    try:
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        
        # Windows 系統字體路徑
        font_candidates = [
            ('微軟雅黑', 'C:/Windows/Fonts/msyh.ttc'),
            ('宋體', 'C:/Windows/Fonts/simsun.ttc'),
            ('黑體', 'C:/Windows/Fonts/simhei.ttf'),
            ('標楷體', 'C:/Windows/Fonts/kaiu.ttf'),
            ('新細明體', 'C:/Windows/Fonts/mingliu.ttc'),
        ]
        
        available_fonts = []
        
        for name, path in font_candidates:
            if os.path.exists(path):
                try:
                    # 嘗試註冊字體
                    if path.endswith('.ttc'):
                        pdfmetrics.registerFont(TTFont(f'Test{name}', path, subfontIndex=0))
                    else:
                        pdfmetrics.registerFont(TTFont(f'Test{name}', path))
                    
                    available_fonts.append((name, path))
                    print(f"  ✅ {name}: 可用")
                except Exception as e:
                    print(f"  ❌ {name}: 註冊失敗 - {e}")
            else:
                print(f"  ❌ {name}: 檔案不存在")
        
        if available_fonts:
            print(f"\n📊 可用字體: {len(available_fonts)}/{len(font_candidates)}")
            return available_fonts[0]  # 返回第一個可用字體
        else:
            print("\n⚠️ 沒有找到可用的中文字體")
            return None
            
    except Exception as e:
        print(f"❌ 字體測試失敗: {e}")
        return None


def create_chinese_pdf_test():
    """建立中文 PDF 測試"""
    print("\n📄 建立中文 PDF 測試")
    print("-" * 40)
    
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.enums import TA_CENTER
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        
        # 測試字體
        font_info = test_chinese_fonts()
        
        output_path = "chinese_encoding_test.pdf"
        doc = SimpleDocTemplate(output_path, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # 如果有中文字體，使用中文字體
        if font_info:
            font_name, font_path = font_info
            try:
                if font_path.endswith('.ttc'):
                    pdfmetrics.registerFont(TTFont('ChineseFont', font_path, subfontIndex=0))
                else:
                    pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                
                # 建立中文樣式
                chinese_style = ParagraphStyle(
                    'Chinese',
                    parent=styles['Normal'],
                    fontName='ChineseFont',
                    fontSize=12,
                    leading=18
                )
                
                title_style = ParagraphStyle(
                    'ChineseTitle',
                    parent=styles['Heading1'],
                    fontName='ChineseFont',
                    fontSize=16,
                    alignment=TA_CENTER,
                    leading=24
                )
                
                use_chinese_font = True
                print(f"  ✅ 使用中文字體: {font_name}")
                
            except Exception as e:
                print(f"  ❌ 中文字體設定失敗: {e}")
                use_chinese_font = False
        else:
            use_chinese_font = False
        
        # 如果沒有中文字體，使用默認字體
        if not use_chinese_font:
            chinese_style = styles['Normal']
            title_style = styles['Heading1']
            print("  ⚠️ 使用默認字體")
        
        # 測試內容
        test_contents = [
            ("標題", "中文字體測試報告"),
            ("繁體中文", "這是繁體中文測試：電腦、軟體、網路、資訊"),
            ("簡體中文", "这是简体中文测试：电脑、软件、网络、信息"),
            ("特殊符號", "特殊符號測試：「」『』""''—…※"),
            ("數字混合", "數字混合測試：Windows 10、macOS 11、Ubuntu 20.04"),
            ("標點符號", "標點符號測試：，。！？；：（）【】《》"),
        ]
        
        for label, content in test_contents:
            try:
                if label == "標題":
                    para = Paragraph(content, title_style)
                else:
                    # 處理文字以避免特殊字符問題
                    safe_content = content.encode('utf-8', errors='replace').decode('utf-8')
                    para = Paragraph(f"{label}：{safe_content}", chinese_style)
                
                story.append(para)
                story.append(Spacer(1, 12))
                print(f"  ✅ 添加內容: {label}")
                
            except Exception as e:
                print(f"  ❌ 處理內容失敗 {label}: {e}")
        
        # 生成 PDF
        doc.build(story)
        
        if Path(output_path).exists():
            file_size = Path(output_path).stat().st_size
            print(f"\n🎉 中文 PDF 測試檔案生成成功！")
            print(f"   檔案: {output_path}")
            print(f"   大小: {file_size} bytes")
            print(f"   字體: {'中文字體' if use_chinese_font else '默認字體'}")
            
            return output_path
        else:
            print("❌ PDF 檔案生成失敗")
            return None
            
    except Exception as e:
        print(f"❌ PDF 生成失敗: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_encoding_conversion():
    """測試編碼轉換"""
    print("\n🔄 測試編碼轉換")
    print("-" * 40)
    
    test_strings = [
        "正常中文文字",
        "包含HTML標籤的文字",
        "包含特殊引號的文字",
        "包含長破折號的文字",
        "包含省略號的文字",
        "混合English和中文的text",
    ]
    
    for text in test_strings:
        try:
            # 模擬處理流程
            # 1. 確保是 UTF-8
            utf8_text = text.encode('utf-8', errors='replace').decode('utf-8')
            
            # 2. 處理 HTML 實體
            import html
            decoded_text = html.unescape(utf8_text)
            
            # 3. 轉義特殊字符
            import xml.sax.saxutils as saxutils
            escaped_text = saxutils.escape(decoded_text)
            
            print(f"  ✅ {text[:20]}... → {escaped_text[:20]}...")
            
        except Exception as e:
            print(f"  ❌ {text[:20]}... → 處理失敗: {e}")


def provide_solutions():
    """提供解決方案"""
    print("\n💡 中文亂碼解決方案")
    print("-" * 40)
    
    print("如果 PDF 中仍有亂碼，請嘗試以下解決方案：")
    print()
    
    print("1. 字體問題解決：")
    print("   • 確認系統已安裝中文字體")
    print("   • 重新啟動電子書轉檔工具")
    print("   • 檢查字體檔案權限")
    print()
    
    print("2. 編碼問題解決：")
    print("   • 確認原始 EPUB 檔案編碼正確")
    print("   • 檢查檔案名稱是否包含特殊字符")
    print("   • 嘗試將 EPUB 先轉為 TXT 再轉 PDF")
    print()
    
    print("3. PDF 閱讀器問題：")
    print("   • 嘗試用不同的 PDF 閱讀器開啟")
    print("   • 更新 PDF 閱讀器到最新版本")
    print("   • 檢查閱讀器的字體設定")
    print()
    
    print("4. 替代方案：")
    print("   • 使用 EPUB → HTML，然後瀏覽器列印為 PDF")
    print("   • 安裝 Pandoc 或 Calibre 獲得更好支援")
    print("   • 使用線上轉換工具")


def main():
    """主程式"""
    print("🔤 中文亂碼問題修正工具")
    print("=" * 50)
    print("專門解決 PDF 轉檔後中文顯示問題")
    print()
    
    # 測試編碼轉換
    test_encoding_conversion()
    
    # 建立測試 PDF
    pdf_file = create_chinese_pdf_test()
    
    # 提供解決方案
    provide_solutions()
    
    # 總結
    print(f"\n📊 診斷總結:")
    print("=" * 30)
    
    if pdf_file:
        print("✅ PDF 生成功能正常")
        print(f"✅ 測試檔案已生成: {pdf_file}")
        print()
        print("💡 請開啟測試檔案檢查中文顯示：")
        print(f"   {pdf_file}")
        print()
        print("如果測試檔案中文顯示正常，但轉檔結果有亂碼：")
        print("• 問題可能出在原始 EPUB 檔案的編碼")
        print("• 或者是特定內容的字符處理問題")
        print("• 建議重新啟動轉檔工具後再試")
    else:
        print("❌ PDF 生成有問題")
        print("建議檢查 ReportLab 安裝或系統字體")
    
    # 清理測試檔案
    if pdf_file and Path(pdf_file).exists():
        try:
            # 不自動刪除，讓用戶檢查
            print(f"\n📝 測試檔案保留供檢查: {pdf_file}")
            print("檢查完畢後可手動刪除")
        except:
            pass


if __name__ == '__main__':
    main()
