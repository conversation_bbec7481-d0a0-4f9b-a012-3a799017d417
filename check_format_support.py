"""
格式支援檢查工具
顯示當前可用引擎支援的轉換格式
"""

from main import EbookConverter, AppConstants
from pathlib import Path


def show_format_support():
    """顯示格式支援情況"""
    print("📋 電子書轉檔工具 - 格式支援檢查")
    print("=" * 50)
    
    # 檢測可用引擎
    converter = EbookConverter()
    engines = converter.available_engines
    
    print("🔍 可用引擎:")
    engine_names = {
        'calibre': 'Calibre (最完整)',
        'pandoc': 'Pandoc (常見格式)',
        'python_libs': 'Python 庫 (部分格式)',
        'builtin': '內建轉換器 (基本格式)'
    }
    
    available_engines = []
    for name, available in engines.items():
        status = "✅" if available else "❌"
        display_name = engine_names.get(name, name)
        print(f"  {status} {display_name}")
        if available:
            available_engines.append(name)
    
    print(f"\n📊 可用引擎數量: {len(available_engines)}/{len(engines)}")
    
    # 顯示支援的轉換
    print("\n🎯 支援的格式轉換:")
    print("-" * 30)
    
    if 'calibre' in available_engines:
        print("✅ Calibre 支援 (完整功能):")
        print("   所有格式互轉: EPUB, PDF, MOBI, AZW3, TXT, HTML, DOCX, FB2, CBZ, CBR")
        print()
    
    if 'pandoc' in available_engines:
        print("✅ Pandoc 支援:")
        pandoc_formats = ['TXT', 'HTML', 'EPUB', 'DOCX', 'PDF', 'MD']
        print(f"   格式: {' ↔ '.join(pandoc_formats)}")
        print()
    
    if 'python_libs' in available_engines:
        print("✅ Python 庫支援:")
        print("   PDF 處理、HTML 解析、文檔處理等")
        print()
    
    if 'builtin' in available_engines:
        print("✅ 內建轉換器支援:")
        builtin_formats = AppConstants.BUILTIN_SUPPORTED
        print(f"   格式: {' ↔ '.join([f.upper() for f in builtin_formats])}")
        print("   具體轉換:")
        print("   • TXT → HTML")
        print("   • HTML → TXT")
        print("   • TXT → Markdown")
        print("   • Markdown → HTML")
        print()
    
    # 推薦的轉換組合
    print("💡 推薦的轉換組合:")
    print("-" * 30)
    
    if available_engines:
        recommendations = []
        
        if 'builtin' in available_engines:
            recommendations.extend([
                "TXT → HTML (製作網頁版電子書)",
                "HTML → TXT (提取純文字內容)",
                "TXT → Markdown (製作 Markdown 文檔)",
                "Markdown → HTML (預覽 Markdown)"
            ])
        
        if 'pandoc' in available_engines:
            recommendations.extend([
                "TXT → EPUB (製作標準電子書)",
                "HTML → PDF (製作 PDF 文檔)",
                "Markdown → DOCX (製作 Word 文檔)"
            ])
        
        if 'calibre' in available_engines:
            recommendations.extend([
                "EPUB → MOBI (Kindle 格式)",
                "PDF → EPUB (提高可讀性)",
                "任何格式互轉"
            ])
        
        for i, rec in enumerate(recommendations[:8], 1):  # 最多顯示 8 個
            print(f"  {i}. {rec}")
    else:
        print("  ❌ 無可用的轉換引擎")
    
    print()


def test_specific_conversion(input_format: str, output_format: str):
    """測試特定格式轉換是否支援"""
    print(f"🧪 測試轉換: {input_format.upper()} → {output_format.upper()}")
    print("-" * 30)
    
    converter = EbookConverter()
    engines = converter.available_engines
    
    # 檢查各引擎支援情況
    support_info = []
    
    # Calibre
    if engines.get('calibre', False):
        calibre_formats = ['epub', 'pdf', 'mobi', 'azw3', 'txt', 'html', 'docx', 'fb2', 'cbz', 'cbr']
        if input_format in calibre_formats and output_format in calibre_formats:
            support_info.append("✅ Calibre 支援")
        else:
            support_info.append("❌ Calibre 不支援")
    else:
        support_info.append("❌ Calibre 不可用")
    
    # Pandoc
    if engines.get('pandoc', False):
        pandoc_formats = ['txt', 'html', 'epub', 'docx', 'pdf', 'md', 'markdown']
        if input_format in pandoc_formats and output_format in pandoc_formats:
            support_info.append("✅ Pandoc 支援")
        else:
            support_info.append("❌ Pandoc 不支援")
    else:
        support_info.append("❌ Pandoc 不可用")
    
    # 內建轉換器
    if engines.get('builtin', False):
        builtin_formats = ['txt', 'html', 'md', 'markdown']
        if input_format in builtin_formats and output_format in builtin_formats:
            support_info.append("✅ 內建轉換器支援")
        else:
            support_info.append("❌ 內建轉換器不支援")
    else:
        support_info.append("❌ 內建轉換器不可用")
    
    for info in support_info:
        print(f"  {info}")
    
    # 總結
    supported = any("✅" in info for info in support_info)
    if supported:
        print(f"\n🎉 結論: {input_format.upper()} → {output_format.upper()} 轉換 ✅ 支援")
    else:
        print(f"\n❌ 結論: {input_format.upper()} → {output_format.upper()} 轉換 ❌ 不支援")
        print("💡 建議: 安裝 Pandoc 或 Calibre 獲得更多格式支援")
    
    print()


def interactive_format_check():
    """互動式格式檢查"""
    print("🎮 互動式格式支援檢查")
    print("=" * 30)
    print("輸入要檢查的格式轉換 (例如: txt html)")
    print("輸入 'quit' 結束")
    
    while True:
        try:
            user_input = input("\n請輸入格式 (輸入格式 輸出格式): ").strip().lower()
            
            if user_input == 'quit':
                print("👋 再見！")
                break
            
            parts = user_input.split()
            if len(parts) != 2:
                print("❌ 請輸入兩個格式，例如: txt html")
                continue
            
            input_fmt, output_fmt = parts
            test_specific_conversion(input_fmt, output_fmt)
            
        except KeyboardInterrupt:
            print("\n👋 再見！")
            break
        except Exception as e:
            print(f"❌ 錯誤: {e}")


def main():
    """主程式"""
    print("🔍 電子書轉檔工具 - 格式支援檢查工具")
    print("=" * 60)
    
    # 顯示整體支援情況
    show_format_support()
    
    # 測試常見轉換
    print("🧪 常見轉換測試:")
    print("-" * 30)
    
    common_tests = [
        ('txt', 'html'),
        ('html', 'txt'),
        ('txt', 'md'),
        ('md', 'html'),
        ('txt', 'pdf'),
        ('epub', 'pdf')
    ]
    
    for input_fmt, output_fmt in common_tests:
        test_specific_conversion(input_fmt, output_fmt)
    
    # 互動式檢查
    print("想要檢查其他格式組合嗎？")
    response = input("輸入 'y' 進入互動模式，其他鍵結束: ").strip().lower()
    
    if response == 'y':
        interactive_format_check()


if __name__ == '__main__':
    main()
