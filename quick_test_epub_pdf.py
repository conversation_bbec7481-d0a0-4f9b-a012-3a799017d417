"""
快速測試 EPUB → PDF 修正
"""

import sys
import os
from pathlib import Path

# 加入當前目錄到 Python 路徑
sys.path.insert(0, os.getcwd())

def test_format_compatibility():
    """測試格式相容性檢查"""
    print("🧪 測試 EPUB → PDF 格式相容性")
    print("-" * 40)
    
    try:
        from main import EbookConverter
        
        converter = EbookConverter()
        engines = converter.available_engines
        
        print("🔍 可用引擎:")
        for name, available in engines.items():
            status = "✅" if available else "❌"
            print(f"  {name}: {status}")
        
        # 測試內建轉換器是否支援 EPUB
        builtin_available = engines.get('builtin', False)
        print(f"\n📋 內建轉換器: {'✅ 可用' if builtin_available else '❌ 不可用'}")
        
        if builtin_available:
            print("  支援格式:")
            from main import AppConstants
            print(f"    基本格式: {AppConstants.BUILTIN_SUPPORTED}")
            print(f"    擴展格式: {AppConstants.EXTENDED_SUPPORTED}")
        
        return builtin_available
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False


def test_epub_conversion_logic():
    """測試 EPUB 轉換邏輯"""
    print("\n🔧 測試 EPUB 轉換邏輯")
    print("-" * 40)
    
    try:
        from main import EbookConverter
        
        converter = EbookConverter()
        
        # 檢查是否有 _convert_epub_to_pdf 方法
        has_epub_pdf_method = hasattr(converter, '_convert_epub_to_pdf')
        print(f"EPUB → PDF 方法: {'✅ 存在' if has_epub_pdf_method else '❌ 不存在'}")
        
        # 檢查是否有 _extract_epub_text 方法
        has_epub_text_method = hasattr(converter, '_extract_epub_text')
        print(f"EPUB 文字提取方法: {'✅ 存在' if has_epub_text_method else '❌ 不存在'}")
        
        return has_epub_pdf_method and has_epub_text_method
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False


def test_pdf_libraries():
    """測試 PDF 處理庫"""
    print("\n📚 測試 PDF 處理庫")
    print("-" * 40)
    
    libraries = {
        'weasyprint': 'WeasyPrint (HTML → PDF)',
        'reportlab': 'ReportLab (文字 → PDF)',
        'pdfkit': 'PDFKit (HTML → PDF)'
    }
    
    available_libs = []
    
    for lib_name, description in libraries.items():
        try:
            __import__(lib_name)
            print(f"  ✅ {description}")
            available_libs.append(lib_name)
        except ImportError:
            print(f"  ❌ {description}")
    
    print(f"\n📊 可用 PDF 庫: {len(available_libs)}/{len(libraries)}")
    
    if available_libs:
        print(f"  已安裝: {', '.join(available_libs)}")
        return True
    else:
        print("  建議安裝: pip install weasyprint reportlab")
        return False


def simulate_conversion_check():
    """模擬轉換相容性檢查"""
    print("\n🎯 模擬 EPUB → PDF 相容性檢查")
    print("-" * 40)
    
    try:
        # 模擬格式檢查邏輯
        input_ext = 'epub'
        output_ext = 'pdf'
        
        from main import EbookConverter
        converter = EbookConverter()
        engines = converter.available_engines
        
        print(f"輸入格式: {input_ext.upper()}")
        print(f"輸出格式: {output_ext.upper()}")
        
        # 檢查各引擎支援
        if engines.get('calibre', False):
            print("✅ Calibre 支援 EPUB → PDF")
            return True
        
        if engines.get('pandoc', False):
            print("✅ Pandoc 支援 EPUB → PDF")
            return True
        
        if engines.get('builtin', False):
            print("⚠️ 內建轉換器支援 EPUB → PDF (透過 HTML)")
            return True
        
        print("❌ 沒有可用的引擎支援 EPUB → PDF")
        return False
        
    except Exception as e:
        print(f"❌ 模擬失敗: {e}")
        return False


def main():
    """主程式"""
    print("🚀 EPUB → PDF 修正快速測試")
    print("=" * 50)
    
    tests = [
        ("格式相容性", test_format_compatibility),
        ("轉換邏輯", test_epub_conversion_logic),
        ("PDF 庫", test_pdf_libraries),
        ("相容性檢查", simulate_conversion_check)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 測試: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"💥 測試例外: {e}")
            results.append((test_name, False))
    
    # 總結
    print(f"\n📊 測試結果總結:")
    print("-" * 30)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總計: {passed}/{len(results)} 個測試通過")
    
    if passed >= 3:
        print("\n🎉 修正成功！EPUB → PDF 功能應該可以使用了")
        print("\n💡 使用方式:")
        print("  1. 啟動程式: python main.py")
        print("  2. 選擇 EPUB 檔案")
        print("  3. 選擇 PDF 輸出格式")
        print("  4. 開始轉檔")
        print("\n⚠️ 注意:")
        print("  • 如果無法直接生成 PDF，程式會生成 HTML 檔案")
        print("  • 您可以用瀏覽器開啟 HTML 並列印為 PDF")
    elif passed >= 2:
        print("\n⚠️ 部分功能可用")
        print("  建議安裝更多 PDF 處理庫以獲得更好的支援")
    else:
        print("\n❌ 修正失敗")
        print("  請檢查程式碼或聯繫技術支援")


if __name__ == '__main__':
    main()
