"""
快速排序算法演示 - Clean Code 實現
展示如何使用 Clean Code 原則實現快速排序
"""

from typing import List, TypeVar, Callable
import random
import time

T = TypeVar('T')


class QuickSortAlgorithm:
    """快速排序算法實現類別"""
    
    @staticmethod
    def sort(arr: List[T], compare_func: Callable[[T, T], bool] = None) -> List[T]:
        """
        快速排序主函數
        
        Args:
            arr: 要排序的陣列
            compare_func: 比較函數，預設為小於比較
            
        Returns:
            排序後的新陣列
        """
        if compare_func is None:
            compare_func = lambda x, y: x < y
        
        # 建立副本避免修改原陣列
        arr_copy = arr.copy()
        QuickSortAlgorithm._quick_sort_recursive(arr_copy, 0, len(arr_copy) - 1, compare_func)
        return arr_copy
    
    @staticmethod
    def _quick_sort_recursive(arr: List[T], low: int, high: int, compare_func: Callable[[T, T], bool]) -> None:
        """
        遞迴快速排序實現
        
        Args:
            arr: 要排序的陣列
            low: 起始索引
            high: 結束索引
            compare_func: 比較函數
        """
        if low < high:
            # 分割陣列並取得分割點
            pivot_index = QuickSortAlgorithm._partition(arr, low, high, compare_func)
            
            # 遞迴排序分割點左右兩部分
            QuickSortAlgorithm._quick_sort_recursive(arr, low, pivot_index - 1, compare_func)
            QuickSortAlgorithm._quick_sort_recursive(arr, pivot_index + 1, high, compare_func)
    
    @staticmethod
    def _partition(arr: List[T], low: int, high: int, compare_func: Callable[[T, T], bool]) -> int:
        """
        分割函數 - 將陣列分為小於和大於基準值的兩部分
        
        Args:
            arr: 要分割的陣列
            low: 起始索引
            high: 結束索引
            compare_func: 比較函數
            
        Returns:
            分割點索引
        """
        # 選擇最後一個元素作為基準值
        pivot = arr[high]
        
        # 小於基準值的元素索引
        smaller_index = low - 1
        
        for current_index in range(low, high):
            # 如果當前元素小於或等於基準值
            if compare_func(arr[current_index], pivot) or arr[current_index] == pivot:
                smaller_index += 1
                QuickSortAlgorithm._swap(arr, smaller_index, current_index)
        
        # 將基準值放到正確位置
        QuickSortAlgorithm._swap(arr, smaller_index + 1, high)
        return smaller_index + 1
    
    @staticmethod
    def _swap(arr: List[T], i: int, j: int) -> None:
        """
        交換陣列中兩個元素的位置
        
        Args:
            arr: 陣列
            i: 第一個元素索引
            j: 第二個元素索引
        """
        arr[i], arr[j] = arr[j], arr[i]


class SortingDemo:
    """排序演示類別"""
    
    def __init__(self):
        self.algorithm = QuickSortAlgorithm()
    
    def demonstrate_basic_sorting(self) -> None:
        """演示基本排序功能"""
        print("🔢 基本快速排序演示")
        print("-" * 30)
        
        # 整數排序
        numbers = [64, 34, 25, 12, 22, 11, 90, 5]
        print(f"原始陣列: {numbers}")
        
        sorted_numbers = self.algorithm.sort(numbers)
        print(f"排序結果: {sorted_numbers}")
        print()
        
        # 字串排序
        words = ["banana", "apple", "cherry", "date", "elderberry"]
        print(f"原始字串: {words}")
        
        sorted_words = self.algorithm.sort(words)
        print(f"排序結果: {sorted_words}")
        print()
    
    def demonstrate_custom_comparison(self) -> None:
        """演示自訂比較函數"""
        print("🎯 自訂比較函數演示")
        print("-" * 30)
        
        numbers = [64, 34, 25, 12, 22, 11, 90, 5]
        print(f"原始陣列: {numbers}")
        
        # 降序排序
        descending_sorted = self.algorithm.sort(numbers, lambda x, y: x > y)
        print(f"降序排序: {descending_sorted}")
        
        # 按絕對值排序
        mixed_numbers = [-5, 3, -1, 8, -2, 7]
        print(f"混合數字: {mixed_numbers}")
        
        abs_sorted = self.algorithm.sort(mixed_numbers, lambda x, y: abs(x) < abs(y))
        print(f"按絕對值排序: {abs_sorted}")
        print()
    
    def demonstrate_performance(self) -> None:
        """演示效能測試"""
        print("⚡ 效能測試演示")
        print("-" * 30)
        
        sizes = [100, 1000, 5000]
        
        for size in sizes:
            # 生成隨機陣列
            random_array = [random.randint(1, 1000) for _ in range(size)]
            
            # 測量排序時間
            start_time = time.time()
            sorted_array = self.algorithm.sort(random_array)
            end_time = time.time()
            
            elapsed_time = (end_time - start_time) * 1000  # 轉換為毫秒
            
            print(f"陣列大小: {size:5d} | 排序時間: {elapsed_time:6.2f} ms")
            
            # 驗證排序正確性
            is_sorted = all(sorted_array[i] <= sorted_array[i + 1] 
                          for i in range(len(sorted_array) - 1))
            print(f"排序正確性: {'✅' if is_sorted else '❌'}")
            print()
    
    def run_all_demos(self) -> None:
        """執行所有演示"""
        print("🚀 快速排序算法 - Clean Code 實現演示")
        print("=" * 50)
        print()
        
        self.demonstrate_basic_sorting()
        self.demonstrate_custom_comparison()
        self.demonstrate_performance()
        
        print("✨ 演示完成！")
        print("\n📝 Clean Code 特色:")
        print("  • 單一職責原則 - 每個方法只負責一個功能")
        print("  • 清晰的命名 - 函數和變數名稱具有描述性")
        print("  • 適當的註解 - 解釋複雜邏輯的目的")
        print("  • 型別提示 - 提高程式碼可讀性和維護性")
        print("  • 模組化設計 - 易於測試和重用")


def main():
    """主程式進入點"""
    demo = SortingDemo()
    demo.run_all_demos()


if __name__ == '__main__':
    main()
