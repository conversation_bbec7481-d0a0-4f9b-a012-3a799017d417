"""
手動安裝 Pandoc 指南
提供多種安裝方式實現一次轉檔完成
"""

import subprocess
import sys
import os
import urllib.request
import zipfile
from pathlib import Path
import shutil


def method_1_winget():
    """方法 1: 使用 winget 安裝"""
    print("📦 方法 1: 使用 winget 安裝 Pandoc")
    print("-" * 40)
    print("1. 按 Win+R 開啟執行對話框")
    print("2. 輸入 'cmd' 並按 Enter")
    print("3. 在命令提示字元中輸入以下命令:")
    print("   winget install --id=JohnMacFarlane.Pandoc -e")
    print("4. 等待安裝完成")
    print("5. 重新啟動命令提示字元")
    print("6. 輸入 'pandoc --version' 驗證安裝")
    print()


def method_2_chocolatey():
    """方法 2: 使用 Chocolatey 安裝"""
    print("🍫 方法 2: 使用 Chocolatey 安裝 Pandoc")
    print("-" * 40)
    print("1. 先安裝 Chocolatey (如果還沒有):")
    print("   - 以管理員身分開啟 PowerShell")
    print("   - 執行: Set-ExecutionPolicy Bypass -Scope Process -Force")
    print("   - 執行: [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072")
    print("   - 執行: iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))")
    print()
    print("2. 安裝 Pandoc:")
    print("   choco install pandoc -y")
    print()


def method_3_manual_download():
    """方法 3: 手動下載安裝"""
    print("📥 方法 3: 手動下載安裝 Pandoc")
    print("-" * 40)
    print("1. 前往 Pandoc 官方網站:")
    print("   https://pandoc.org/installing.html")
    print()
    print("2. 點擊 'Windows' 下載 MSI 安裝檔")
    print("   (選擇 pandoc-x.x.x-windows-x86_64.msi)")
    print()
    print("3. 執行下載的 MSI 檔案")
    print("4. 按照安裝精靈完成安裝")
    print("5. 重新啟動命令提示字元")
    print("6. 輸入 'pandoc --version' 驗證安裝")
    print()


def method_4_portable():
    """方法 4: 可攜版安裝"""
    print("💼 方法 4: 可攜版 Pandoc")
    print("-" * 40)
    
    try:
        pandoc_version = "3.1.8"
        download_url = f"https://github.com/jgm/pandoc/releases/download/{pandoc_version}/pandoc-{pandoc_version}-windows-x86_64.zip"
        
        print(f"正在下載 Pandoc {pandoc_version} 可攜版...")
        print(f"下載網址: {download_url}")
        
        # 建立下載目錄
        download_dir = Path("pandoc_portable")
        download_dir.mkdir(exist_ok=True)
        
        zip_file = download_dir / "pandoc.zip"
        
        print("開始下載...")
        urllib.request.urlretrieve(download_url, zip_file)
        print("✅ 下載完成")
        
        print("正在解壓縮...")
        with zipfile.ZipFile(zip_file, 'r') as zip_ref:
            zip_ref.extractall(download_dir)
        
        # 找到解壓縮的目錄
        extracted_dirs = [d for d in download_dir.iterdir() if d.is_dir() and d.name.startswith('pandoc-')]
        if extracted_dirs:
            pandoc_dir = extracted_dirs[0]
            pandoc_exe = pandoc_dir / "pandoc.exe"
            
            if pandoc_exe.exists():
                print("✅ 解壓縮完成")
                print(f"Pandoc 位置: {pandoc_exe}")
                
                # 測試 Pandoc
                result = subprocess.run([str(pandoc_exe), '--version'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print("✅ Pandoc 測試成功")
                    
                    # 建立設定 PATH 的批次檔
                    batch_content = f'''@echo off
echo 設定 Pandoc PATH...
set PANDOC_PATH={pandoc_dir.absolute()}
setx PATH "%PATH%;%PANDOC_PATH%"
echo ✅ PATH 設定完成
echo 請重新啟動命令提示字元
pause
'''
                    batch_file = Path("setup_pandoc_path.bat")
                    with open(batch_file, 'w', encoding='utf-8') as f:
                        f.write(batch_content)
                    
                    print(f"✅ 已建立 PATH 設定檔: {batch_file}")
                    print("執行此檔案可將 Pandoc 加入系統 PATH")
                    
                    return True
        
        # 清理下載檔案
        zip_file.unlink()
        
    except Exception as e:
        print(f"❌ 可攜版安裝失敗: {e}")
        return False


def test_pandoc_installation():
    """測試 Pandoc 安裝"""
    print("🧪 測試 Pandoc 安裝")
    print("-" * 40)
    
    try:
        result = subprocess.run(['pandoc', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_info = result.stdout.split('\n')[0]
            print(f"✅ Pandoc 已安裝: {version_info}")
            
            # 測試 EPUB → PDF 轉換
            print("\n🔄 測試 EPUB → PDF 轉換能力...")
            help_result = subprocess.run(['pandoc', '--help'], 
                                       capture_output=True, text=True, timeout=10)
            if 'epub' in help_result.stdout.lower() and 'pdf' in help_result.stdout.lower():
                print("✅ 支援 EPUB → PDF 轉換")
                return True
            else:
                print("⚠️ 可能需要額外的 PDF 引擎 (如 LaTeX)")
                return False
        else:
            print("❌ Pandoc 未正確安裝")
            return False
            
    except FileNotFoundError:
        print("❌ 找不到 Pandoc 命令")
        print("請確認 Pandoc 已安裝並加入 PATH")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False


def main():
    """主程式"""
    print("🚀 Pandoc 安裝指南 - 實現一次轉檔完成")
    print("=" * 60)
    print("安裝 Pandoc 後，您就可以直接進行 EPUB → PDF 轉換")
    print()
    
    # 先測試是否已安裝
    if test_pandoc_installation():
        print("\n🎉 Pandoc 已經可用！")
        print("現在您可以直接使用 EPUB → PDF 轉換功能")
        return
    
    print("\n📋 選擇安裝方式:")
    print("=" * 30)
    
    # 提供多種安裝方式
    method_1_winget()
    method_2_chocolatey()
    method_3_manual_download()
    
    # 嘗試可攜版安裝
    print("🔄 嘗試自動安裝可攜版...")
    if method_4_portable():
        print("\n🎉 可攜版安裝成功！")
        print("執行 setup_pandoc_path.bat 將 Pandoc 加入 PATH")
    else:
        print("\n⚠️ 自動安裝失敗，請使用上述手動方式")
    
    print("\n💡 安裝完成後:")
    print("1. 重新啟動電子書轉檔工具")
    print("2. 選擇 EPUB 檔案")
    print("3. 選擇 PDF 輸出格式")
    print("4. 一次轉檔完成！")


if __name__ == '__main__':
    main()
