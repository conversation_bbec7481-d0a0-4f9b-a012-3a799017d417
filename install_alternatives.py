"""
電子書轉檔工具 - 替代方案安裝器
當 Calibre 無法安裝時的替代解決方案
"""

import subprocess
import sys
import os
from pathlib import Path
import urllib.request
import zipfile
import shutil


class AlternativeInstaller:
    """替代轉檔工具安裝器"""
    
    def __init__(self):
        self.python_executable = sys.executable
        
    def install_python_libraries(self):
        """安裝 Python 電子書處理庫"""
        print("🐍 安裝 Python 電子書處理庫...")
        
        libraries = [
            'pypdf2',      # PDF 處理
            'ebooklib',    # EPUB 處理
            'python-docx', # DOCX 處理
            'beautifulsoup4', # HTML 處理
            'lxml',        # XML 處理
            'markdown',    # Markdown 處理
        ]
        
        installed = []
        failed = []
        
        for lib in libraries:
            try:
                print(f"  正在安裝 {lib}...")
                result = subprocess.run([
                    self.python_executable, '-m', 'pip', 'install', lib
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    installed.append(lib)
                    print(f"  ✅ {lib} 安裝成功")
                else:
                    failed.append(lib)
                    print(f"  ❌ {lib} 安裝失敗: {result.stderr}")
                    
            except Exception as e:
                failed.append(lib)
                print(f"  ❌ {lib} 安裝例外: {e}")
        
        print(f"\n📊 安裝結果:")
        print(f"  成功: {len(installed)} 個")
        print(f"  失敗: {len(failed)} 個")
        
        return len(installed) > 0
    
    def install_pandoc_portable(self):
        """安裝 Pandoc 可攜版"""
        print("📄 安裝 Pandoc 可攜版...")
        
        try:
            # 檢查是否已安裝
            result = subprocess.run(['pandoc', '--version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("  ✅ Pandoc 已經安裝")
                return True
        except FileNotFoundError:
            pass
        
        # 下載 Pandoc 可攜版
        pandoc_url = "https://github.com/jgm/pandoc/releases/download/3.1.8/pandoc-3.1.8-windows-x86_64.zip"
        pandoc_dir = Path("./pandoc_portable")
        
        try:
            print("  正在下載 Pandoc...")
            urllib.request.urlretrieve(pandoc_url, "pandoc.zip")
            
            print("  正在解壓縮...")
            with zipfile.ZipFile("pandoc.zip", 'r') as zip_ref:
                zip_ref.extractall(".")
            
            # 移動到指定目錄
            extracted_dir = Path("pandoc-3.1.8")
            if extracted_dir.exists():
                if pandoc_dir.exists():
                    shutil.rmtree(pandoc_dir)
                extracted_dir.rename(pandoc_dir)
            
            # 清理
            os.remove("pandoc.zip")
            
            # 測試安裝
            pandoc_exe = pandoc_dir / "pandoc.exe"
            if pandoc_exe.exists():
                print("  ✅ Pandoc 可攜版安裝成功")
                print(f"  📁 安裝位置: {pandoc_dir.absolute()}")
                return True
            else:
                print("  ❌ Pandoc 安裝失敗")
                return False
                
        except Exception as e:
            print(f"  ❌ Pandoc 安裝例外: {e}")
            return False
    
    def create_simple_converter(self):
        """建立簡單的內建轉換器"""
        print("🔧 建立內建轉換器...")
        
        converter_code = '''"""
簡單電子書轉換器 - 內建版本
支援基本的文字格式轉換
"""

import re
from pathlib import Path

class SimpleConverter:
    """簡單轉換器"""
    
    @staticmethod
    def txt_to_html(input_file, output_file):
        """TXT 轉 HTML"""
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{Path(input_file).stem}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        pre {{ white-space: pre-wrap; }}
    </style>
</head>
<body>
    <h1>{Path(input_file).stem}</h1>
    <pre>{content}</pre>
</body>
</html>"""
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html)
    
    @staticmethod
    def html_to_txt(input_file, output_file):
        """HTML 轉 TXT"""
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 移除 HTML 標籤
        text = re.sub(r'<[^>]+>', '', content)
        text = re.sub(r'\\s+', ' ', text).strip()
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(text)
    
    @staticmethod
    def txt_to_markdown(input_file, output_file):
        """TXT 轉 Markdown"""
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 簡單的 Markdown 格式化
        lines = content.split('\\n')
        markdown_lines = []
        
        for line in lines:
            line = line.strip()
            if line:
                # 如果是標題（全大寫或特殊格式）
                if line.isupper() or line.startswith('第') and line.endswith('章'):
                    markdown_lines.append(f"# {line}")
                else:
                    markdown_lines.append(line)
            else:
                markdown_lines.append("")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\\n'.join(markdown_lines))

if __name__ == '__main__':
    print("簡單轉換器已準備就緒")
'''
        
        try:
            with open('simple_converter.py', 'w', encoding='utf-8') as f:
                f.write(converter_code)
            print("  ✅ 內建轉換器建立成功")
            return True
        except Exception as e:
            print(f"  ❌ 內建轉換器建立失敗: {e}")
            return False
    
    def run_installation(self):
        """執行完整安裝流程"""
        print("🚀 電子書轉檔工具 - 替代方案安裝")
        print("=" * 50)
        
        success_count = 0
        
        # 1. 安裝 Python 庫
        if self.install_python_libraries():
            success_count += 1
        
        print()
        
        # 2. 安裝 Pandoc 可攜版
        if self.install_pandoc_portable():
            success_count += 1
        
        print()
        
        # 3. 建立內建轉換器
        if self.create_simple_converter():
            success_count += 1
        
        print()
        print("🎉 安裝完成！")
        print(f"成功安裝 {success_count}/3 個替代方案")
        
        if success_count > 0:
            print("\n✅ 您現在可以使用電子書轉檔工具了！")
            print("支援的轉換:")
            print("  • TXT ↔ HTML")
            print("  • TXT → Markdown")
            if success_count >= 2:
                print("  • 更多格式（透過 Pandoc 或 Python 庫）")
        else:
            print("\n❌ 所有替代方案都安裝失敗")
            print("建議手動安裝 Calibre 或聯繫技術支援")


def main():
    """主程式"""
    installer = AlternativeInstaller()
    installer.run_installation()


if __name__ == '__main__':
    main()
