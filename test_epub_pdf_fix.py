"""
測試 EPUB → PDF 修正
驗證新的轉換功能是否正常工作
"""

from main import EbookConverter, ConversionStatus
from pathlib import Path
import time
import zipfile


def create_test_epub():
    """建立測試 EPUB 檔案"""
    print("📚 建立測試 EPUB 檔案...")
    
    epub_path = "test_epub_for_pdf.epub"
    
    # 簡化的 EPUB 結構
    files = {
        'mimetype': 'application/epub+zip',
        'META-INF/container.xml': '''<?xml version="1.0"?>
<container version="1.0" xmlns="urn:oasis:names:tc:opendocument:xmlns:container">
  <rootfiles>
    <rootfile full-path="content.opf" media-type="application/oebps-package+xml"/>
  </rootfiles>
</container>''',
        'content.opf': '''<?xml version="1.0" encoding="UTF-8"?>
<package xmlns="http://www.idpf.org/2007/opf" unique-identifier="BookId" version="2.0">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <dc:title>EPUB 轉 PDF 測試</dc:title>
    <dc:creator>電子書轉檔工具</dc:creator>
  </metadata>
  <manifest>
    <item id="chapter1" href="chapter1.html" media-type="application/xhtml+xml"/>
  </manifest>
  <spine>
    <itemref idref="chapter1"/>
  </spine>
</package>''',
        'chapter1.html': '''<!DOCTYPE html>
<html>
<head>
    <title>測試章節</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>EPUB 轉 PDF 測試</h1>
    <p>這是一個測試 EPUB 檔案，用來驗證 EPUB → PDF 轉換功能。</p>
    
    <h2>功能特色</h2>
    <ul>
        <li>自動 EPUB 解析</li>
        <li>HTML 中介轉換</li>
        <li>多種 PDF 生成方式</li>
        <li>友善的使用者介面</li>
    </ul>
    
    <h2>轉換流程</h2>
    <ol>
        <li>解析 EPUB 結構</li>
        <li>提取文字內容</li>
        <li>生成 HTML 格式</li>
        <li>轉換為 PDF 檔案</li>
    </ol>
    
    <p><strong>測試完成！</strong></p>
</body>
</html>'''
    }
    
    try:
        with zipfile.ZipFile(epub_path, 'w', zipfile.ZIP_DEFLATED) as epub_zip:
            for file_path, content in files.items():
                epub_zip.writestr(file_path, content.encode('utf-8'))
        
        print(f"  ✅ 建立測試 EPUB: {epub_path}")
        return epub_path
        
    except Exception as e:
        print(f"  ❌ 建立 EPUB 失敗: {e}")
        return None


def test_epub_pdf_conversion():
    """測試 EPUB → PDF 轉換"""
    print("\n🔄 測試 EPUB → PDF 轉換")
    print("-" * 40)
    
    # 建立測試檔案
    epub_file = create_test_epub()
    if not epub_file:
        return False
    
    output_file = "test_epub_to_pdf.pdf"
    
    # 檢查引擎狀態
    converter = EbookConverter()
    engines = converter.available_engines
    
    print("🔍 可用引擎:")
    for name, available in engines.items():
        status = "✅" if available else "❌"
        print(f"  {name}: {status}")
    
    # 測試格式相容性檢查
    print(f"\n🧪 測試格式相容性檢查...")
    
    # 模擬 GUI 的格式檢查
    from main import EbookConverterGUI
    import tkinter as tk
    
    # 建立臨時 GUI 實例來測試
    root = tk.Tk()
    root.withdraw()  # 隱藏主視窗
    
    try:
        gui = EbookConverterGUI()
        gui.root.withdraw()  # 隱藏 GUI 視窗
        
        # 測試格式相容性
        compatible = gui.check_format_compatibility(epub_file, output_file)
        print(f"  格式相容性: {'✅ 支援' if compatible else '❌ 不支援'}")
        
        if not compatible:
            print("  ❌ 格式檢查失敗，無法進行轉換測試")
            return False
        
    except Exception as e:
        print(f"  ⚠️ GUI 測試跳過: {e}")
    finally:
        try:
            root.destroy()
        except:
            pass
    
    # 執行轉換
    print(f"\n🚀 開始轉換: {epub_file} → {output_file}")
    
    result = {"completed": False, "success": False, "message": ""}
    
    def callback(status: ConversionStatus, message: str):
        print(f"  📊 {status.value}: {message}")
        if status in [ConversionStatus.SUCCESS, ConversionStatus.FAILED]:
            result["completed"] = True
            result["success"] = (status == ConversionStatus.SUCCESS)
            result["message"] = message
    
    # 開始轉換
    converter.convert_ebook_async(epub_file, output_file, callback)
    
    # 等待完成
    timeout = 30
    start_time = time.time()
    
    while not result["completed"] and (time.time() - start_time) < timeout:
        time.sleep(0.1)
    
    # 檢查結果
    if result["completed"] and result["success"]:
        if Path(output_file).exists():
            file_size = Path(output_file).stat().st_size
            print(f"  ✅ 轉換成功！檔案大小: {file_size} bytes")
            return True
        else:
            # 檢查是否生成了 HTML 檔案
            html_file = output_file.replace('.pdf', '_temp.html')
            if Path(html_file).exists():
                print(f"  ⚠️ 生成了 HTML 檔案: {html_file}")
                print(f"  💡 請手動用瀏覽器開啟並列印為 PDF")
                return True
            else:
                print(f"  ❌ 轉換報告成功但檔案未生成")
                return False
    else:
        print(f"  ❌ 轉換失敗: {result.get('message', '未知錯誤')}")
        return False


def cleanup_test_files():
    """清理測試檔案"""
    print(f"\n🗑️ 清理測試檔案...")
    
    test_files = [
        'test_epub_for_pdf.epub',
        'test_epub_to_pdf.pdf',
        'test_epub_to_pdf_temp.html'
    ]
    
    cleaned = 0
    for file in test_files:
        if Path(file).exists():
            Path(file).unlink()
            cleaned += 1
    
    print(f"  清理了 {cleaned} 個檔案")


def main():
    """主程式"""
    print("🧪 EPUB → PDF 修正測試")
    print("=" * 50)
    
    try:
        success = test_epub_pdf_conversion()
        
        print(f"\n📊 測試結果:")
        if success:
            print("  ✅ EPUB → PDF 轉換功能正常")
            print("\n💡 現在您可以:")
            print("  • 直接選擇 EPUB 檔案")
            print("  • 選擇 PDF 輸出格式")
            print("  • 程式會自動處理轉換")
            print("  • 如果無法直接生成 PDF，會提供 HTML 檔案供手動轉換")
        else:
            print("  ❌ EPUB → PDF 轉換功能異常")
            print("\n🔧 建議:")
            print("  • 檢查是否安裝了 PDF 處理庫")
            print("  • 嘗試手動安裝: pip install weasyprint reportlab")
            print("  • 或使用 EPUB → HTML 然後手動轉 PDF")
        
        cleanup_test_files()
        
    except Exception as e:
        print(f"💥 測試過程中發生例外: {e}")
        cleanup_test_files()


if __name__ == '__main__':
    main()
