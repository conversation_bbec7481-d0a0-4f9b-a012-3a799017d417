"""
調試 EPUB 解析問題
找出為什麼只轉換一頁內容
"""

import zipfile
import xml.etree.ElementTree as ET
from pathlib import Path


def create_multi_chapter_test_epub():
    """建立多章節測試 EPUB"""
    epub_path = "multi_chapter_test.epub"
    
    files = {
        'mimetype': 'application/epub+zip',
        'META-INF/container.xml': '''<?xml version="1.0"?>
<container version="1.0" xmlns="urn:oasis:names:tc:opendocument:xmlns:container">
  <rootfiles>
    <rootfile full-path="content.opf" media-type="application/oebps-package+xml"/>
  </rootfiles>
</container>''',
        'content.opf': '''<?xml version="1.0" encoding="UTF-8"?>
<package xmlns="http://www.idpf.org/2007/opf" unique-identifier="BookId" version="2.0">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <dc:title>多章節測試書</dc:title>
    <dc:creator>調試工具</dc:creator>
  </metadata>
  <manifest>
    <item id="chapter1" href="chapter1.html" media-type="application/xhtml+xml"/>
    <item id="chapter2" href="chapter2.html" media-type="application/xhtml+xml"/>
    <item id="chapter3" href="chapter3.html" media-type="application/xhtml+xml"/>
    <item id="chapter4" href="chapter4.html" media-type="application/xhtml+xml"/>
  </manifest>
  <spine>
    <itemref idref="chapter1"/>
    <itemref idref="chapter2"/>
    <itemref idref="chapter3"/>
    <itemref idref="chapter4"/>
  </spine>
</package>''',
        'chapter1.html': '''<!DOCTYPE html>
<html>
<head><title>第一章</title></head>
<body>
    <h1>第一章：開始</h1>
    <p>這是第一章的內容。如果只看到這一章，表示有問題。</p>
    <p>第一章應該是完整內容的一部分，不是全部。</p>
</body>
</html>''',
        'chapter2.html': '''<!DOCTYPE html>
<html>
<head><title>第二章</title></head>
<body>
    <h1>第二章：發展</h1>
    <p>這是第二章的重要內容。這章必須出現在轉檔結果中。</p>
    <p>如果看不到第二章，表示多章節解析失敗。</p>
</body>
</html>''',
        'chapter3.html': '''<!DOCTYPE html>
<html>
<head><title>第三章</title></head>
<body>
    <h1>第三章：高潮</h1>
    <p>這是第三章的關鍵內容。這是測試的重點章節。</p>
    <p>第三章包含重要的情節發展。</p>
</body>
</html>''',
        'chapter4.html': '''<!DOCTYPE html>
<html>
<head><title>第四章</title></head>
<body>
    <h1>第四章：結局</h1>
    <p>這是第四章的結尾內容。這是最後一章。</p>
    <p>如果看到這章，表示多章節解析成功。</p>
</body>
</html>'''
    }
    
    with zipfile.ZipFile(epub_path, 'w', zipfile.ZIP_DEFLATED) as epub_zip:
        for file_path, content in files.items():
            epub_zip.writestr(file_path, content.encode('utf-8'))
    
    return epub_path


def debug_epub_structure(epub_path):
    """詳細調試 EPUB 結構"""
    print(f"🔍 調試 EPUB 結構: {epub_path}")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(epub_path, 'r') as epub_zip:
            # 1. 列出所有檔案
            all_files = epub_zip.namelist()
            print(f"📁 EPUB 包含檔案 ({len(all_files)} 個):")
            for i, file in enumerate(all_files, 1):
                print(f"  {i}. {file}")
            
            # 2. 解析 container.xml
            print(f"\n📄 解析 container.xml:")
            try:
                container_content = epub_zip.read('META-INF/container.xml').decode('utf-8')
                print(f"  ✅ 讀取成功，長度: {len(container_content)} 字符")
                
                container_root = ET.fromstring(container_content)
                opf_path = None
                for rootfile in container_root.findall('.//{urn:oasis:names:tc:opendocument:xmlns:container}rootfile'):
                    if rootfile.get('media-type') == 'application/oebps-package+xml':
                        opf_path = rootfile.get('full-path')
                        print(f"  📋 找到 OPF 檔案: {opf_path}")
                        break
                
                if not opf_path:
                    print(f"  ❌ 未找到 OPF 檔案路徑")
                    return False
                    
            except Exception as e:
                print(f"  ❌ container.xml 解析失敗: {e}")
                return False
            
            # 3. 解析 OPF 檔案
            print(f"\n📋 解析 OPF 檔案: {opf_path}")
            try:
                opf_content = epub_zip.read(opf_path).decode('utf-8')
                print(f"  ✅ 讀取成功，長度: {len(opf_content)} 字符")
                
                opf_root = ET.fromstring(opf_content)
                namespaces = {'opf': 'http://www.idpf.org/2007/opf'}
                
                # 解析 manifest
                manifest_items = {}
                manifest_elements = opf_root.findall('.//opf:item', namespaces)
                print(f"  📦 Manifest 項目 ({len(manifest_elements)} 個):")
                
                for item in manifest_elements:
                    item_id = item.get('id')
                    href = item.get('href')
                    media_type = item.get('media-type')
                    if item_id and href:
                        manifest_items[item_id] = href
                        print(f"    • {item_id} → {href} ({media_type})")
                
                # 解析 spine
                spine_elements = opf_root.findall('.//opf:itemref', namespaces)
                print(f"  📚 Spine 順序 ({len(spine_elements)} 個):")
                
                spine_order = []
                for i, itemref in enumerate(spine_elements, 1):
                    idref = itemref.get('idref')
                    if idref in manifest_items:
                        href = manifest_items[idref]
                        spine_order.append(href)
                        print(f"    {i}. {idref} → {href}")
                    else:
                        print(f"    {i}. ❌ {idref} (在 manifest 中未找到)")
                
                return spine_order
                
            except Exception as e:
                print(f"  ❌ OPF 檔案解析失敗: {e}")
                return False
                
    except Exception as e:
        print(f"❌ EPUB 結構調試失敗: {e}")
        return False


def test_current_extraction_step_by_step(epub_path):
    """逐步測試當前的提取邏輯"""
    print(f"\n🧪 逐步測試當前提取邏輯")
    print("-" * 50)
    
    try:
        from main import EbookConverter
        
        converter = EbookConverter()
        
        # 手動模擬提取過程
        import zipfile
        import xml.etree.ElementTree as ET
        
        with zipfile.ZipFile(epub_path, 'r') as epub_zip:
            print("步驟 1: 解析 container.xml")
            try:
                container_content = epub_zip.read('META-INF/container.xml').decode('utf-8')
                container_root = ET.fromstring(container_content)
                
                opf_path = None
                for rootfile in container_root.findall('.//{urn:oasis:names:tc:opendocument:xmlns:container}rootfile'):
                    if rootfile.get('media-type') == 'application/oebps-package+xml':
                        opf_path = rootfile.get('full-path')
                        break
                
                print(f"  ✅ OPF 路徑: {opf_path}")
                
            except Exception as e:
                print(f"  ❌ 失敗: {e}")
                return False
            
            print("步驟 2: 解析 OPF 檔案")
            try:
                opf_content = epub_zip.read(opf_path).decode('utf-8')
                opf_root = ET.fromstring(opf_content)
                namespaces = {'opf': 'http://www.idpf.org/2007/opf'}
                
                # 獲取 manifest
                manifest_items = {}
                for item in opf_root.findall('.//opf:item', namespaces):
                    item_id = item.get('id')
                    href = item.get('href')
                    if item_id and href:
                        manifest_items[item_id] = href
                
                # 獲取 spine 順序
                spine_order = []
                for itemref in opf_root.findall('.//opf:itemref', namespaces):
                    idref = itemref.get('idref')
                    if idref in manifest_items:
                        spine_order.append(manifest_items[idref])
                
                print(f"  ✅ 找到 {len(spine_order)} 個章節: {spine_order}")
                
            except Exception as e:
                print(f"  ❌ 失敗: {e}")
                return False
            
            print("步驟 3: 逐個讀取章節檔案")
            total_content = ""
            processed_count = 0
            
            for i, href in enumerate(spine_order, 1):
                print(f"  處理第 {i} 個檔案: {href}")
                
                try:
                    # 嘗試不同的路徑
                    content = None
                    for attempt_path in [href, f"OEBPS/{href}", f"Text/{href}"]:
                        try:
                            content = epub_zip.read(attempt_path).decode('utf-8', errors='replace')
                            print(f"    ✅ 成功讀取 (路徑: {attempt_path})")
                            break
                        except KeyError:
                            continue
                    
                    if content:
                        # 提取文字
                        text = converter._extract_text_from_html(content)
                        char_count = len(text)
                        total_content += f"\n=== 第 {i} 章 ===\n{text}\n"
                        processed_count += 1
                        print(f"    📝 提取文字: {char_count} 字符")
                    else:
                        print(f"    ❌ 無法讀取檔案")
                        
                except Exception as e:
                    print(f"    ❌ 處理失敗: {e}")
            
            print(f"\n📊 處理結果:")
            print(f"  處理檔案數: {processed_count}/{len(spine_order)}")
            print(f"  總文字長度: {len(total_content)} 字符")
            
            if processed_count == 1:
                print(f"  ⚠️ 只處理了一個檔案 - 這就是問題所在！")
            elif processed_count == len(spine_order):
                print(f"  ✅ 所有檔案都已處理")
            else:
                print(f"  ⚠️ 部分檔案處理失敗")
            
            # 顯示內容預覽
            if total_content:
                print(f"\n📖 內容預覽:")
                print("-" * 30)
                preview = total_content[:500] + "..." if len(total_content) > 500 else total_content
                print(preview)
            
            return processed_count == len(spine_order)
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主程式"""
    print("🔍 EPUB 解析問題調試工具")
    print("=" * 60)
    print("找出為什麼只轉換一頁內容的根本原因")
    print()
    
    # 建立測試檔案
    epub_file = create_multi_chapter_test_epub()
    print(f"✅ 建立多章節測試 EPUB: {epub_file}")
    
    try:
        # 調試 EPUB 結構
        spine_order = debug_epub_structure(epub_file)
        
        if spine_order:
            print(f"\n✅ EPUB 結構解析成功，應該有 {len(spine_order)} 個章節")
            
            # 測試當前提取邏輯
            success = test_current_extraction_step_by_step(epub_file)
            
            print(f"\n📊 調試結論:")
            print("=" * 30)
            
            if success:
                print("✅ 多章節提取成功")
                print("問題可能在其他地方，需要進一步調查")
            else:
                print("❌ 多章節提取失敗")
                print("找到問題根源，需要修正提取邏輯")
                
                print(f"\n💡 可能的問題:")
                print("• 檔案路徑解析錯誤")
                print("• 只處理第一個檔案就停止")
                print("• 錯誤處理導致提前退出")
                print("• spine 順序解析問題")
        else:
            print(f"\n❌ EPUB 結構解析失敗")
            print("問題在基本的 EPUB 解析邏輯")
    
    finally:
        # 清理測試檔案
        if Path(epub_file).exists():
            Path(epub_file).unlink()
            print(f"\n🗑️ 清理測試檔案: {epub_file}")


if __name__ == '__main__':
    main()
