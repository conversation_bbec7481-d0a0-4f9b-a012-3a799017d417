"""
測試多章節修正
驗證是否解決了只轉換一頁內容的問題
"""

import zipfile
from pathlib import Path


def create_multi_chapter_epub():
    """建立多章節測試 EPUB"""
    epub_path = "multi_chapter_fix_test.epub"
    
    files = {
        'mimetype': 'application/epub+zip',
        'META-INF/container.xml': '''<?xml version="1.0"?>
<container version="1.0" xmlns="urn:oasis:names:tc:opendocument:xmlns:container">
  <rootfiles>
    <rootfile full-path="content.opf" media-type="application/oebps-package+xml"/>
  </rootfiles>
</container>''',
        'content.opf': '''<?xml version="1.0" encoding="UTF-8"?>
<package xmlns="http://www.idpf.org/2007/opf" unique-identifier="BookId" version="2.0">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <dc:title>多章節修正測試</dc:title>
    <dc:creator>測試工具</dc:creator>
  </metadata>
  <manifest>
    <item id="ch1" href="chapter1.html" media-type="application/xhtml+xml"/>
    <item id="ch2" href="chapter2.html" media-type="application/xhtml+xml"/>
    <item id="ch3" href="chapter3.html" media-type="application/xhtml+xml"/>
    <item id="ch4" href="chapter4.html" media-type="application/xhtml+xml"/>
    <item id="ch5" href="chapter5.html" media-type="application/xhtml+xml"/>
  </manifest>
  <spine>
    <itemref idref="ch1"/>
    <itemref idref="ch2"/>
    <itemref idref="ch3"/>
    <itemref idref="ch4"/>
    <itemref idref="ch5"/>
  </spine>
</package>''',
        'chapter1.html': '''<!DOCTYPE html>
<html><head><title>第一章</title></head>
<body>
    <h1>第一章：故事開始</h1>
    <p>這是第一章的內容。如果只看到這一章，表示多章節處理有問題。</p>
    <p>第一章應該是完整內容的開始，不是全部。</p>
    <p>關鍵詞：第一章標記</p>
</body></html>''',
        'chapter2.html': '''<!DOCTYPE html>
<html><head><title>第二章</title></head>
<body>
    <h1>第二章：情節發展</h1>
    <p>這是第二章的重要內容。這章必須出現在轉檔結果中。</p>
    <p>如果看不到第二章，表示多章節解析失敗。</p>
    <p>關鍵詞：第二章標記</p>
</body></html>''',
        'chapter3.html': '''<!DOCTYPE html>
<html><head><title>第三章</title></head>
<body>
    <h1>第三章：高潮部分</h1>
    <p>這是第三章的關鍵內容。這是測試的重點章節。</p>
    <p>第三章包含重要的情節轉折。</p>
    <p>關鍵詞：第三章標記</p>
</body></html>''',
        'chapter4.html': '''<!DOCTYPE html>
<html><head><title>第四章</title></head>
<body>
    <h1>第四章：解決方案</h1>
    <p>這是第四章的解決內容。故事開始收尾。</p>
    <p>第四章展示問題的解決過程。</p>
    <p>關鍵詞：第四章標記</p>
</body></html>''',
        'chapter5.html': '''<!DOCTYPE html>
<html><head><title>第五章</title></head>
<body>
    <h1>第五章：完美結局</h1>
    <p>這是第五章的結尾內容。這是最後一章。</p>
    <p>如果看到這章，表示多章節解析完全成功。</p>
    <p>關鍵詞：第五章標記</p>
</body></html>'''
    }
    
    with zipfile.ZipFile(epub_path, 'w', zipfile.ZIP_DEFLATED) as epub_zip:
        for file_path, content in files.items():
            epub_zip.writestr(file_path, content.encode('utf-8'))
    
    return epub_path


def test_multi_chapter_extraction():
    """測試多章節提取"""
    print("🧪 多章節提取修正測試")
    print("=" * 50)
    
    # 建立測試檔案
    epub_file = create_multi_chapter_epub()
    print(f"✅ 建立多章節測試 EPUB: {epub_file}")
    print(f"   包含 5 個章節，每章都有獨特的標記")
    
    # 測試轉換
    try:
        from main import EbookConverter, ConversionStatus
        
        converter = EbookConverter()
        output_file = "multi_chapter_test_output.txt"
        
        def callback(status, message):
            print(f"📊 {status.value}: {message}")
        
        print(f"\n🔄 開始轉換測試...")
        
        # 執行轉換
        success = converter._extract_epub_text(epub_file, output_file, 'txt', callback)
        
        if success and Path(output_file).exists():
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📝 轉換結果:")
            print(f"   總字符數: {len(content)}")
            
            # 檢查每個章節的標記
            chapter_markers = [
                "第一章標記",
                "第二章標記", 
                "第三章標記",
                "第四章標記",
                "第五章標記"
            ]
            
            print(f"\n🔍 章節完整性檢查:")
            found_chapters = []
            missing_chapters = []
            
            for i, marker in enumerate(chapter_markers, 1):
                if marker in content:
                    print(f"  ✅ 第 {i} 章: 找到")
                    found_chapters.append(i)
                else:
                    print(f"  ❌ 第 {i} 章: 缺失")
                    missing_chapters.append(i)
            
            # 分析結果
            total_chapters = len(chapter_markers)
            found_count = len(found_chapters)
            
            print(f"\n📊 測試結果分析:")
            print(f"   找到章節: {found_count}/{total_chapters}")
            print(f"   完整度: {(found_count/total_chapters)*100:.1f}%")
            
            if found_count == 1:
                print(f"   🚨 只找到 1 章 - 確認是「只轉換一頁內容」的問題")
                result = "只有一頁"
            elif found_count == total_chapters:
                print(f"   🎉 找到所有章節 - 多章節問題已解決！")
                result = "完全修正"
            elif found_count > 1:
                print(f"   ⚠️ 找到部分章節 - 有改善但未完全解決")
                result = "部分修正"
            else:
                print(f"   ❌ 沒有找到任何章節 - 提取完全失敗")
                result = "完全失敗"
            
            # 顯示內容預覽
            print(f"\n📖 內容預覽 (前 500 字符):")
            print("-" * 30)
            preview = content[:500] + "..." if len(content) > 500 else content
            print(preview)
            
            return result, found_count, total_chapters
            
        else:
            print("❌ 轉換失敗")
            return "轉換失敗", 0, 5
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return "測試錯誤", 0, 5
    
    finally:
        # 清理測試檔案
        for file in [epub_file, "multi_chapter_test_output.txt"]:
            if Path(file).exists():
                Path(file).unlink()


def main():
    """主程式"""
    print("🔍 多章節轉換問題修正驗證")
    print("=" * 60)
    print("測試是否解決了「只轉換一頁內容」的問題")
    print()
    
    result, found, total = test_multi_chapter_extraction()
    
    print(f"\n📊 最終測試結果:")
    print("=" * 30)
    print(f"測試結果: {result}")
    print(f"章節數量: {found}/{total}")
    
    if result == "完全修正":
        print(f"\n🎉 問題已完全解決！")
        print("💡 現在您可以:")
        print("  • 獲得完整的多章節內容")
        print("  • 所有章節都會被正確提取")
        print("  • 按照正確順序排列")
        print("  • 重新啟動程式並測試您的 EPUB 檔案")
        
    elif result == "部分修正":
        print(f"\n⚠️ 問題有改善但未完全解決")
        print("💡 建議:")
        print("  • 重新啟動電子書轉檔工具")
        print("  • 檢查 EPUB 檔案結構")
        print("  • 嘗試不同的 EPUB 檔案")
        
    elif result == "只有一頁":
        print(f"\n❌ 仍然只轉換一頁內容")
        print("💡 需要進一步調查:")
        print("  • 檢查 EPUB 解析邏輯")
        print("  • 檢查錯誤處理機制")
        print("  • 可能需要更深入的修正")
        
    else:
        print(f"\n❌ 轉換仍有問題")
        print("💡 建議:")
        print("  • 檢查程式安裝")
        print("  • 檢查依賴套件")
        print("  • 嘗試重新安裝")


if __name__ == '__main__':
    main()
