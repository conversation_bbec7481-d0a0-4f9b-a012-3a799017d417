<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>EPUB 內容提取</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        h1 { color: #333; }
        .epub-info { background: #f5f5f5; padding: 15px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="epub-info">
        <h1>EPUB 內容提取</h1>
        <p><strong>來源:</strong> no_popup_test.epub</p>
    </div>
    <pre>電子書檔案: no_popup_test.epub

=== chapter1.html ===
無彈出訊息測試 無彈出訊息測試 測試目標 驗證轉檔過程中不會出現彈出訊息窗，所有狀態都在程式介面中顯示。 測試內容 ✅ 轉檔開始：狀態列顯示進度 ✅ 轉檔進行：進度條動畫 ✅ 轉檔完成：狀態列顯示結果 ✅ 自動開啟輸出資料夾 ❌ 無彈出確認對話框 ❌ 無彈出成功訊息 ❌ 無彈出錯誤訊息 中文測試 測試中文字符：電腦、軟體、網路、資訊、技術 特殊符號：「」『』""''—…※ 如果您看到這個 PDF，且過程中沒有彈出訊息，表示修改成功！
</pre>
</body>
</html>