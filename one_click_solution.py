"""
一鍵解決方案 - 實現完全自動化的 EPUB → PDF 轉換
"""

import subprocess
import sys
import os
import urllib.request
import zipfile
from pathlib import Path
import shutil
import time


class OneClickSolution:
    """一鍵解決方案類別"""
    
    def __init__(self):
        self.solutions = [
            ("安裝 Pandoc", self.install_pandoc),
            ("增強 ReportLab", self.enhance_reportlab),
            ("安裝 wkhtmltopdf", self.install_wkhtmltopdf),
            ("配置 Chrome PDF", self.setup_chrome_pdf)
        ]
    
    def run(self):
        """執行一鍵解決方案"""
        print("🚀 一鍵解決方案 - 實現完全自動化 EPUB → PDF")
        print("=" * 60)
        print("目標：讓您能夠一次轉檔就完成，無需手動步驟")
        print()
        
        success_count = 0
        
        for i, (name, solution_func) in enumerate(self.solutions, 1):
            print(f"📋 方案 {i}: {name}")
            print("-" * 40)
            
            try:
                if solution_func():
                    success_count += 1
                    print(f"✅ 方案 {i} 成功")
                    
                    # 如果 Pandoc 安裝成功，就不需要其他方案了
                    if i == 1:
                        print("🎉 Pandoc 安裝成功！這是最佳解決方案")
                        break
                else:
                    print(f"❌ 方案 {i} 失敗")
            except Exception as e:
                print(f"💥 方案 {i} 例外: {e}")
            
            print()
        
        # 總結
        self.show_summary(success_count)
    
    def install_pandoc(self):
        """安裝 Pandoc"""
        print("🔧 嘗試安裝 Pandoc...")
        
        # 檢查是否已安裝
        if self.test_pandoc():
            print("  ✅ Pandoc 已經安裝")
            return True
        
        # 方法 1: winget
        if self.install_pandoc_winget():
            return True
        
        # 方法 2: 可攜版
        if self.install_pandoc_portable():
            return True
        
        print("  ❌ 所有 Pandoc 安裝方法都失敗")
        return False
    
    def install_pandoc_winget(self):
        """使用 winget 安裝 Pandoc"""
        try:
            print("  正在使用 winget 安裝...")
            result = subprocess.run([
                'winget', 'install', '--id=JohnMacFarlane.Pandoc', '-e', '--accept-source-agreements', '--accept-package-agreements'
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print("  ✅ winget 安裝完成")
                time.sleep(5)  # 等待安裝完成
                return self.test_pandoc()
            else:
                print(f"  ❌ winget 安裝失敗: {result.stderr}")
                return False
        except Exception as e:
            print(f"  ❌ winget 安裝例外: {e}")
            return False
    
    def install_pandoc_portable(self):
        """安裝 Pandoc 可攜版"""
        try:
            print("  正在下載 Pandoc 可攜版...")
            
            pandoc_version = "3.1.8"
            download_url = f"https://github.com/jgm/pandoc/releases/download/{pandoc_version}/pandoc-{pandoc_version}-windows-x86_64.zip"
            
            # 建立目錄
            pandoc_dir = Path("pandoc_portable")
            pandoc_dir.mkdir(exist_ok=True)
            
            zip_file = pandoc_dir / "pandoc.zip"
            
            # 下載
            urllib.request.urlretrieve(download_url, zip_file)
            print("  ✅ 下載完成")
            
            # 解壓縮
            with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                zip_ref.extractall(pandoc_dir)
            
            # 找到 pandoc.exe
            for item in pandoc_dir.rglob("pandoc.exe"):
                pandoc_exe = item
                break
            else:
                print("  ❌ 找不到 pandoc.exe")
                return False
            
            # 測試
            result = subprocess.run([str(pandoc_exe), '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("  ✅ 可攜版安裝成功")
                
                # 更新 PATH
                pandoc_path = str(pandoc_exe.parent.absolute())
                current_path = os.environ.get('PATH', '')
                if pandoc_path not in current_path:
                    os.environ['PATH'] = f"{pandoc_path};{current_path}"
                
                # 建立永久 PATH 設定
                self.create_path_setter(pandoc_path)
                
                return True
            
            return False
            
        except Exception as e:
            print(f"  ❌ 可攜版安裝失敗: {e}")
            return False
    
    def test_pandoc(self):
        """測試 Pandoc"""
        try:
            result = subprocess.run(['pandoc', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except:
            return False
    
    def enhance_reportlab(self):
        """增強 ReportLab 功能"""
        print("📚 增強 ReportLab PDF 生成...")
        
        try:
            # 安裝額外的 PDF 庫
            libraries = ['reportlab', 'Pillow', 'pypdf']
            
            for lib in libraries:
                print(f"  正在安裝 {lib}...")
                result = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', lib, '--upgrade', '--quiet'
                ], capture_output=True, text=True, timeout=120)
                
                if result.returncode == 0:
                    print(f"  ✅ {lib} 安裝成功")
                else:
                    print(f"  ⚠️ {lib} 安裝失敗")
            
            # 測試 ReportLab
            try:
                from reportlab.lib.pagesizes import A4
                from reportlab.platypus import SimpleDocTemplate
                print("  ✅ ReportLab 功能正常")
                return True
            except ImportError:
                print("  ❌ ReportLab 測試失敗")
                return False
                
        except Exception as e:
            print(f"  ❌ ReportLab 增強失敗: {e}")
            return False
    
    def install_wkhtmltopdf(self):
        """安裝 wkhtmltopdf"""
        print("🌐 安裝 wkhtmltopdf...")
        
        try:
            # 檢查是否已安裝
            result = subprocess.run(['wkhtmltopdf', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("  ✅ wkhtmltopdf 已安裝")
                return True
        except:
            pass
        
        # 嘗試使用 winget 安裝
        try:
            print("  正在使用 winget 安裝 wkhtmltopdf...")
            result = subprocess.run([
                'winget', 'install', '--id=wkhtmltopdf.wkhtmltopdf', '-e'
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print("  ✅ wkhtmltopdf 安裝成功")
                return True
            else:
                print("  ❌ wkhtmltopdf 安裝失敗")
                return False
                
        except Exception as e:
            print(f"  ❌ wkhtmltopdf 安裝例外: {e}")
            return False
    
    def setup_chrome_pdf(self):
        """配置 Chrome PDF 功能"""
        print("🌐 配置 Chrome PDF 功能...")
        
        try:
            # 安裝 selenium 和 chrome driver
            libraries = ['selenium', 'webdriver-manager']
            
            for lib in libraries:
                print(f"  正在安裝 {lib}...")
                result = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', lib, '--quiet'
                ], capture_output=True, text=True, timeout=120)
                
                if result.returncode == 0:
                    print(f"  ✅ {lib} 安裝成功")
                else:
                    print(f"  ⚠️ {lib} 安裝失敗")
            
            # 測試 Chrome
            try:
                from selenium import webdriver
                from selenium.webdriver.chrome.options import Options
                print("  ✅ Chrome PDF 功能配置完成")
                return True
            except ImportError:
                print("  ❌ Chrome PDF 配置失敗")
                return False
                
        except Exception as e:
            print(f"  ❌ Chrome PDF 配置例外: {e}")
            return False
    
    def create_path_setter(self, pandoc_path):
        """建立 PATH 設定檔"""
        batch_content = f'''@echo off
echo 設定 Pandoc PATH 環境變數...
setx PATH "%PATH%;{pandoc_path}"
echo ✅ PATH 設定完成
echo 請重新啟動命令提示字元以使用 Pandoc
pause
'''
        
        batch_file = Path("set_pandoc_path.bat")
        with open(batch_file, 'w', encoding='utf-8') as f:
            f.write(batch_content)
        
        print(f"  📝 已建立 PATH 設定檔: {batch_file}")
    
    def show_summary(self, success_count):
        """顯示總結"""
        print("📊 解決方案總結:")
        print("=" * 40)
        
        if success_count > 0:
            print("🎉 成功！現在您可以一次轉檔完成 EPUB → PDF")
            print()
            print("💡 使用方式:")
            print("1. 重新啟動電子書轉檔工具")
            print("2. 選擇 EPUB 檔案")
            print("3. 選擇 PDF 輸出格式")
            print("4. 點擊「開始轉檔」")
            print("5. 等待完成 - 無需手動步驟！")
            
            if success_count == 1 and self.test_pandoc():
                print("\n🌟 Pandoc 已安裝 - 這是最佳解決方案")
                print("   支援最高品質的 EPUB → PDF 轉換")
            
        else:
            print("❌ 自動安裝失敗")
            print()
            print("🔧 手動解決方案:")
            print("1. 手動安裝 Pandoc:")
            print("   https://pandoc.org/installing.html")
            print("2. 或執行: python install_pandoc_manual.py")
            print("3. 重新啟動程式")


def main():
    """主程式"""
    solution = OneClickSolution()
    solution.run()


if __name__ == '__main__':
    main()
