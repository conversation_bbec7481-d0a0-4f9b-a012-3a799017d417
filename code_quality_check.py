"""
程式碼品質檢查工具
檢查 Clean Code 原則的遵循情況
"""

import ast
import inspect
from pathlib import Path
from typing import List, Dict, Tuple
import re


class CodeQualityChecker:
    """程式碼品質檢查器"""
    
    def __init__(self, file_path: str):
        self.file_path = Path(file_path)
        self.source_code = self.file_path.read_text(encoding='utf-8')
        self.tree = ast.parse(self.source_code)
        self.lines = self.source_code.split('\n')
        
    def check_all(self) -> Dict[str, List[str]]:
        """執行所有品質檢查"""
        results = {
            'function_length': self.check_function_length(),
            'class_design': self.check_class_design(),
            'naming_conventions': self.check_naming_conventions(),
            'documentation': self.check_documentation(),
            'complexity': self.check_complexity(),
            'clean_code_principles': self.check_clean_code_principles()
        }
        return results
    
    def check_function_length(self, max_lines: int = 20) -> List[str]:
        """檢查函數長度 (Clean Code: 函數應該簡短)"""
        issues = []
        
        for node in ast.walk(self.tree):
            if isinstance(node, ast.FunctionDef):
                func_lines = node.end_lineno - node.lineno + 1
                if func_lines > max_lines:
                    issues.append(
                        f"函數 '{node.name}' 過長 ({func_lines} 行)，建議拆分為更小的函數"
                    )
        
        return issues
    
    def check_class_design(self) -> List[str]:
        """檢查類別設計 (單一職責原則)"""
        issues = []
        
        for node in ast.walk(self.tree):
            if isinstance(node, ast.ClassDef):
                methods = [n for n in node.body if isinstance(n, ast.FunctionDef)]
                
                # 檢查方法數量
                if len(methods) > 15:
                    issues.append(
                        f"類別 '{node.name}' 方法過多 ({len(methods)} 個)，可能違反單一職責原則"
                    )
                
                # 檢查是否有文檔字串
                if not ast.get_docstring(node):
                    issues.append(f"類別 '{node.name}' 缺少文檔字串")
        
        return issues
    
    def check_naming_conventions(self) -> List[str]:
        """檢查命名規範"""
        issues = []
        
        for node in ast.walk(self.tree):
            if isinstance(node, ast.FunctionDef):
                # 檢查函數命名 (snake_case)
                if not re.match(r'^[a-z_][a-z0-9_]*$', node.name) and not node.name.startswith('__'):
                    issues.append(f"函數 '{node.name}' 命名不符合 snake_case 規範")
            
            elif isinstance(node, ast.ClassDef):
                # 檢查類別命名 (PascalCase)
                if not re.match(r'^[A-Z][a-zA-Z0-9]*$', node.name):
                    issues.append(f"類別 '{node.name}' 命名不符合 PascalCase 規範")
            
            elif isinstance(node, ast.Name) and isinstance(node.ctx, ast.Store):
                # 檢查變數命名
                if node.id.isupper() and len(node.id) > 1:
                    # 常數應該全大寫
                    continue
                elif not re.match(r'^[a-z_][a-z0-9_]*$', node.id):
                    issues.append(f"變數 '{node.id}' 命名不符合 snake_case 規範")
        
        return issues
    
    def check_documentation(self) -> List[str]:
        """檢查文檔完整性"""
        issues = []
        
        # 檢查模組文檔
        module_doc = ast.get_docstring(self.tree)
        if not module_doc:
            issues.append("模組缺少文檔字串")
        
        # 檢查函數文檔
        for node in ast.walk(self.tree):
            if isinstance(node, ast.FunctionDef):
                if not node.name.startswith('_') and not ast.get_docstring(node):
                    issues.append(f"公開函數 '{node.name}' 缺少文檔字串")
        
        return issues
    
    def check_complexity(self) -> List[str]:
        """檢查程式碼複雜度"""
        issues = []
        
        for node in ast.walk(self.tree):
            if isinstance(node, ast.FunctionDef):
                complexity = self._calculate_cyclomatic_complexity(node)
                if complexity > 10:
                    issues.append(
                        f"函數 '{node.name}' 循環複雜度過高 ({complexity})，建議重構"
                    )
        
        return issues
    
    def check_clean_code_principles(self) -> List[str]:
        """檢查 Clean Code 原則遵循情況"""
        issues = []
        
        # 檢查魔術數字
        for node in ast.walk(self.tree):
            if isinstance(node, ast.Num) and isinstance(node.n, int):
                if node.n not in [0, 1, -1] and node.n > 100:
                    issues.append(f"發現魔術數字 {node.n}，建議定義為常數")
        
        # 檢查長字串
        for node in ast.walk(self.tree):
            if isinstance(node, ast.Str) and len(node.s) > 50:
                issues.append("發現長字串，建議提取為常數或配置")
        
        # 檢查深層嵌套
        max_depth = self._find_max_nesting_depth(self.tree)
        if max_depth > 4:
            issues.append(f"程式碼嵌套層級過深 ({max_depth} 層)，建議重構")
        
        return issues
    
    def _calculate_cyclomatic_complexity(self, node: ast.FunctionDef) -> int:
        """計算循環複雜度"""
        complexity = 1  # 基礎複雜度
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.ExceptHandler)):
                complexity += 1
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1
        
        return complexity
    
    def _find_max_nesting_depth(self, node: ast.AST, current_depth: int = 0) -> int:
        """找出最大嵌套深度"""
        max_depth = current_depth
        
        for child in ast.iter_child_nodes(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.With, ast.Try)):
                child_depth = self._find_max_nesting_depth(child, current_depth + 1)
                max_depth = max(max_depth, child_depth)
            else:
                child_depth = self._find_max_nesting_depth(child, current_depth)
                max_depth = max(max_depth, child_depth)
        
        return max_depth


def generate_quality_report(file_path: str) -> str:
    """生成程式碼品質報告"""
    checker = CodeQualityChecker(file_path)
    results = checker.check_all()
    
    report = f"📊 程式碼品質報告 - {Path(file_path).name}\n"
    report += "=" * 50 + "\n\n"
    
    total_issues = 0
    
    for category, issues in results.items():
        category_name = {
            'function_length': '函數長度',
            'class_design': '類別設計',
            'naming_conventions': '命名規範',
            'documentation': '文檔完整性',
            'complexity': '程式碼複雜度',
            'clean_code_principles': 'Clean Code 原則'
        }.get(category, category)
        
        report += f"🔍 {category_name}\n"
        report += "-" * 30 + "\n"
        
        if issues:
            for issue in issues:
                report += f"  ⚠️  {issue}\n"
            total_issues += len(issues)
        else:
            report += "  ✅ 無問題發現\n"
        
        report += "\n"
    
    # 總結
    report += "📈 總結\n"
    report += "-" * 30 + "\n"
    report += f"總計發現 {total_issues} 個問題\n"
    
    if total_issues == 0:
        report += "🎉 程式碼品質優秀！完全符合 Clean Code 原則\n"
    elif total_issues <= 5:
        report += "👍 程式碼品質良好，有少量改進空間\n"
    elif total_issues <= 15:
        report += "⚠️  程式碼品質中等，建議進行重構\n"
    else:
        report += "🚨 程式碼品質需要大幅改進\n"
    
    return report


def main():
    """主程式"""
    print("🔍 電子書轉檔工具 - 程式碼品質檢查")
    print("=" * 40)
    
    # 檢查主程式檔案
    main_file = "main.py"
    if Path(main_file).exists():
        print(f"\n正在檢查 {main_file}...")
        report = generate_quality_report(main_file)
        print(report)
        
        # 儲存報告
        report_file = "code_quality_report.txt"
        Path(report_file).write_text(report, encoding='utf-8')
        print(f"📄 詳細報告已儲存至 {report_file}")
    else:
        print(f"❌ 找不到檔案: {main_file}")


if __name__ == '__main__':
    main()
