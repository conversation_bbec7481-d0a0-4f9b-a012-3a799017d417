"""
簡單的中文字體測試
快速驗證 ReportLab 中文支援
"""

import os
from pathlib import Path


def test_chinese_font_simple():
    """簡單測試中文字體"""
    print("🔤 簡單中文字體測試")
    print("-" * 40)
    
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph
        from reportlab.lib.styles import getSampleStyleSheet
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        
        print("✅ ReportLab 導入成功")
        
        # 嘗試註冊中文字體
        font_registered = False
        font_paths = [
            'C:/Windows/Fonts/msyh.ttc',      # 微軟雅黑
            'C:/Windows/Fonts/simsun.ttc',    # 宋體
            'C:/Windows/Fonts/simhei.ttf',    # 黑體
        ]
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    if font_path.endswith('.ttc'):
                        pdfmetrics.registerFont(TTFont('ChineseFont', font_path, subfontIndex=0))
                    else:
                        pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                    
                    print(f"✅ 成功註冊字體: {font_path}")
                    font_registered = True
                    break
                except Exception as e:
                    print(f"❌ 字體註冊失敗 {font_path}: {e}")
        
        if not font_registered:
            print("⚠️ 未註冊中文字體，使用默認字體")
        
        # 建立測試 PDF
        output_path = "chinese_font_test.pdf"
        doc = SimpleDocTemplate(output_path, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # 測試中文內容
        chinese_texts = [
            "測試中文字體顯示",
            "繁體中文：電腦、軟體、網路",
            "简体中文：电脑、软件、网络",
            "特殊符號：「」『』""''—…",
            "數字混合：Windows 10、macOS 11"
        ]
        
        for text in chinese_texts:
            try:
                if font_registered:
                    # 使用中文字體
                    from reportlab.lib.styles import ParagraphStyle
                    chinese_style = ParagraphStyle(
                        'Chinese',
                        parent=styles['Normal'],
                        fontName='ChineseFont',
                        fontSize=12,
                        encoding='utf-8'
                    )
                    para = Paragraph(text, chinese_style)
                else:
                    # 使用默認字體
                    para = Paragraph(text, styles['Normal'])
                
                story.append(para)
                print(f"✅ 添加文字: {text}")
            except Exception as e:
                print(f"❌ 處理文字失敗: {text}, 錯誤: {e}")
        
        # 生成 PDF
        doc.build(story)
        
        if Path(output_path).exists():
            file_size = Path(output_path).stat().st_size
            print(f"\n🎉 中文 PDF 生成成功！")
            print(f"   檔案: {output_path}")
            print(f"   大小: {file_size} bytes")
            print(f"   字體: {'中文字體' if font_registered else '默認字體'}")
            
            # 提示用戶檢查
            print(f"\n💡 請開啟 {output_path} 檢查中文是否正確顯示")
            print("   如果顯示正常，表示亂碼問題已解決")
            
            return True
        else:
            print("❌ PDF 檔案未生成")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_encoding_handling():
    """測試編碼處理"""
    print("\n📝 測試編碼處理")
    print("-" * 40)
    
    try:
        import html
        import xml.sax.saxutils as saxutils
        
        # 測試各種編碼問題
        test_cases = [
            ("正常中文", "正常中文"),
            ("HTML實體", "&lt;測試&gt;"),
            ("特殊引號", ""測試""),
            ("長破折號", "測試—內容"),
            ("省略號", "測試…內容"),
            ("混合內容", "Test測試123"),
        ]
        
        print("編碼處理測試:")
        for name, text in test_cases:
            try:
                # 模擬處理流程
                decoded = html.unescape(text)
                escaped = saxutils.escape(decoded)
                final = escaped.encode('utf-8', errors='replace').decode('utf-8')
                
                print(f"  ✅ {name}: {text} → {final}")
            except Exception as e:
                print(f"  ❌ {name}: 處理失敗 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 編碼測試失敗: {e}")
        return False


def check_system_fonts():
    """檢查系統字體"""
    print("\n🔍 檢查系統字體")
    print("-" * 40)
    
    font_paths = [
        ('微軟雅黑', 'C:/Windows/Fonts/msyh.ttc'),
        ('宋體', 'C:/Windows/Fonts/simsun.ttc'),
        ('黑體', 'C:/Windows/Fonts/simhei.ttf'),
        ('標楷體', 'C:/Windows/Fonts/kaiu.ttf'),
        ('新細明體', 'C:/Windows/Fonts/mingliu.ttc'),
    ]
    
    available_fonts = []
    
    for name, path in font_paths:
        if os.path.exists(path):
            print(f"  ✅ {name}: {path}")
            available_fonts.append((name, path))
        else:
            print(f"  ❌ {name}: 未找到")
    
    print(f"\n📊 可用中文字體: {len(available_fonts)}/{len(font_paths)}")
    
    if available_fonts:
        print("💡 建議使用的字體:")
        for name, path in available_fonts[:3]:  # 顯示前3個
            print(f"   • {name}")
        return True
    else:
        print("⚠️ 未找到系統中文字體")
        print("💡 建議:")
        print("   1. 確認 Windows 系統已安裝中文字體")
        print("   2. 或下載並安裝開源中文字體")
        return False


def main():
    """主程式"""
    print("🔤 中文字體亂碼問題診斷工具")
    print("=" * 50)
    
    # 檢查系統字體
    fonts_ok = check_system_fonts()
    
    # 測試編碼處理
    encoding_ok = test_encoding_handling()
    
    # 測試字體和 PDF 生成
    pdf_ok = test_chinese_font_simple()
    
    # 總結
    print(f"\n📊 診斷結果:")
    print("=" * 30)
    print(f"系統字體: {'✅ 正常' if fonts_ok else '❌ 問題'}")
    print(f"編碼處理: {'✅ 正常' if encoding_ok else '❌ 問題'}")
    print(f"PDF 生成: {'✅ 正常' if pdf_ok else '❌ 問題'}")
    
    if fonts_ok and encoding_ok and pdf_ok:
        print(f"\n🎉 診斷完成！中文支援正常")
        print("💡 如果轉檔後仍有亂碼，請檢查:")
        print("   1. 原始 EPUB 檔案的編碼")
        print("   2. 重新啟動電子書轉檔工具")
        print("   3. 確認 PDF 閱讀器支援中文")
    else:
        print(f"\n⚠️ 發現問題，需要修正")
        if not fonts_ok:
            print("   • 系統缺少中文字體")
        if not encoding_ok:
            print("   • 編碼處理有問題")
        if not pdf_ok:
            print("   • PDF 生成失敗")
    
    # 清理測試檔案
    test_file = "chinese_font_test.pdf"
    if Path(test_file).exists():
        try:
            Path(test_file).unlink()
            print(f"\n🗑️ 清理測試檔案: {test_file}")
        except:
            pass


if __name__ == '__main__':
    main()
