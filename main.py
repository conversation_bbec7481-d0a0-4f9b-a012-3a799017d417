"""
電子書轉檔工具 - 優化版本
採用 MVC 架構模式，遵循 Clean Code 原則
"""

import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import subprocess
import threading
from pathlib import Path
from typing import Callable, Dict
from enum import Enum
import logging

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ConversionStatus(Enum):
    """轉檔狀態枚舉"""
    IDLE = "待機中"
    CONVERTING = "轉檔中..."
    SUCCESS = "轉檔完成！"
    FAILED = "轉檔失敗"


class AppConstants:
    """應用程式常數"""
    TITLE = "電子書轉檔工具"
    WINDOW_SIZE = "500x300"
    ENTRY_WIDTH = 40
    BUTTON_WIDTH = 20

    # 支援的格式
    SUPPORTED_FORMATS = [
        'epub', 'pdf', 'mobi', 'azw3', 'txt', 'html', 'docx', 'fb2', 'cbz', 'cbr'
    ]

    # 訊息文字
    MESSAGES = {
        'select_input': '請選擇正確的輸入檔案！',
        'invalid_format': '不支援的檔案格式！',
        'file_not_found': '找不到指定的檔案！',
        'conversion_success': '轉檔完成！',
        'conversion_failed': '轉檔失敗',
        'calibre_not_found': '找不到 Calibre，請確認已正確安裝並設定環境變數',
        'output_exists': '輸出檔案已存在，是否要覆蓋？',
    }


class EbookConverter:
    """電子書轉檔核心邏輯類別"""

    def __init__(self):
        self.is_converting = False
        self.available_engines = self._detect_available_engines()

    def _detect_available_engines(self) -> Dict[str, bool]:
        """檢測可用的轉檔引擎"""
        engines = {
            'calibre': self.check_calibre_available(),
            'pandoc': self._check_pandoc_available(),
            'python_libs': self._check_python_libraries(),
        }
        return engines

    def _check_pandoc_available(self) -> bool:
        """檢查 Pandoc 是否可用"""
        try:
            result = subprocess.run(['pandoc', '--version'],
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False

    def _check_python_libraries(self) -> bool:
        """檢查 Python 電子書處理庫是否可用"""
        try:
            # 檢查是否有可用的 Python 庫
            import importlib
            libraries = ['pypdf', 'ebooklib', 'python-docx']
            available = []

            for lib in libraries:
                try:
                    importlib.import_module(lib.replace('-', '_'))
                    available.append(lib)
                except ImportError:
                    pass

            return len(available) > 0
        except Exception:
            return False

    def validate_input_file(self, file_path: str) -> bool:
        """驗證輸入檔案"""
        if not file_path:
            return False

        path = Path(file_path)
        if not path.exists() or not path.is_file():
            return False

        # 檢查檔案格式
        file_extension = path.suffix.lower().lstrip('.')
        return file_extension in AppConstants.SUPPORTED_FORMATS

    def validate_output_path(self, output_path: str) -> bool:
        """驗證輸出路徑"""
        if not output_path:
            return False

        path = Path(output_path)
        # 檢查父目錄是否存在
        return path.parent.exists()

    def check_calibre_available(self) -> bool:
        """檢查 Calibre 是否可用"""
        try:
            result = subprocess.run(['ebook-convert', '--version'],
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False

    def convert_ebook_async(self, input_path: str, output_path: str,
                           progress_callback: Callable[[ConversionStatus, str], None]) -> None:
        """非同步轉檔方法"""
        def conversion_thread():
            try:
                self.is_converting = True
                progress_callback(ConversionStatus.CONVERTING, "正在轉檔...")

                # 選擇可用的轉檔引擎
                success = False

                # 嘗試 Calibre
                if self.available_engines.get('calibre', False):
                    success = self._convert_with_calibre(input_path, output_path, progress_callback)

                # 如果 Calibre 失敗，嘗試 Pandoc
                if not success and self.available_engines.get('pandoc', False):
                    success = self._convert_with_pandoc(input_path, output_path, progress_callback)

                # 如果都失敗，嘗試 Python 庫
                if not success and self.available_engines.get('python_libs', False):
                    success = self._convert_with_python_libs(input_path, output_path, progress_callback)

                # 如果所有方法都失敗
                if not success:
                    available_engines = [name for name, available in self.available_engines.items() if available]
                    if not available_engines:
                        progress_callback(ConversionStatus.FAILED,
                                        "找不到可用的轉檔引擎。請安裝 Calibre、Pandoc 或相關 Python 庫")
                    else:
                        progress_callback(ConversionStatus.FAILED, "所有可用的轉檔引擎都無法處理此檔案")

            except Exception as e:
                progress_callback(ConversionStatus.FAILED, f"發生例外：{str(e)}")
                logger.error(f"轉檔例外: {str(e)}")
            finally:
                self.is_converting = False

        # 在新執行緒中執行轉檔
        thread = threading.Thread(target=conversion_thread, daemon=True)
        thread.start()

    def _convert_with_calibre(self, input_path: str, output_path: str,
                             progress_callback: Callable[[ConversionStatus, str], None]) -> bool:
        """使用 Calibre 轉檔"""
        try:
            cmd = ['ebook-convert', input_path, output_path]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                progress_callback(ConversionStatus.SUCCESS, "Calibre 轉檔完成！")
                logger.info(f"Calibre 轉檔成功: {input_path} -> {output_path}")
                return True
            else:
                logger.warning(f"Calibre 轉檔失敗: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.warning("Calibre 轉檔超時")
            return False
        except Exception as e:
            logger.warning(f"Calibre 轉檔例外: {str(e)}")
            return False

    def _convert_with_pandoc(self, input_path: str, output_path: str,
                            progress_callback: Callable[[ConversionStatus, str], None]) -> bool:
        """使用 Pandoc 轉檔"""
        try:
            # Pandoc 支援的格式對應
            input_ext = Path(input_path).suffix.lower().lstrip('.')
            output_ext = Path(output_path).suffix.lower().lstrip('.')

            # 檢查 Pandoc 是否支援這個轉換
            pandoc_formats = {
                'txt': 'plain', 'html': 'html', 'epub': 'epub',
                'docx': 'docx', 'pdf': 'pdf'
            }

            if input_ext not in pandoc_formats or output_ext not in pandoc_formats:
                return False

            cmd = [
                'pandoc',
                input_path,
                '-f', pandoc_formats[input_ext],
                '-t', pandoc_formats[output_ext],
                '-o', output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                progress_callback(ConversionStatus.SUCCESS, "Pandoc 轉檔完成！")
                logger.info(f"Pandoc 轉檔成功: {input_path} -> {output_path}")
                return True
            else:
                logger.warning(f"Pandoc 轉檔失敗: {result.stderr}")
                return False

        except Exception as e:
            logger.warning(f"Pandoc 轉檔例外: {str(e)}")
            return False

    def _convert_with_python_libs(self, input_path: str, output_path: str,
                                 progress_callback: Callable[[ConversionStatus, str], None]) -> bool:
        """使用 Python 庫轉檔"""
        try:
            input_ext = Path(input_path).suffix.lower().lstrip('.')
            output_ext = Path(output_path).suffix.lower().lstrip('.')

            # 簡單的文字轉換
            if input_ext == 'txt' and output_ext == 'html':
                return self._txt_to_html(input_path, output_path, progress_callback)
            elif input_ext == 'html' and output_ext == 'txt':
                return self._html_to_txt(input_path, output_path, progress_callback)

            # 其他格式暫不支援
            return False

        except Exception as e:
            logger.warning(f"Python 庫轉檔例外: {str(e)}")
            return False

    def _txt_to_html(self, input_path: str, output_path: str,
                     progress_callback: Callable[[ConversionStatus, str], None]) -> bool:
        """TXT 轉 HTML"""
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                content = f.read()

            html_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{Path(input_path).stem}</title>
</head>
<body>
    <pre>{content}</pre>
</body>
</html>"""

            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            progress_callback(ConversionStatus.SUCCESS, "Python 內建轉檔完成！")
            return True

        except Exception as e:
            logger.warning(f"TXT 轉 HTML 失敗: {str(e)}")
            return False

    def _html_to_txt(self, input_path: str, output_path: str,
                     progress_callback: Callable[[ConversionStatus, str], None]) -> bool:
        """HTML 轉 TXT"""
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 簡單的 HTML 標籤移除
            import re
            text_content = re.sub(r'<[^>]+>', '', content)
            text_content = re.sub(r'\s+', ' ', text_content).strip()

            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(text_content)

            progress_callback(ConversionStatus.SUCCESS, "Python 內建轉檔完成！")
            return True

        except Exception as e:
            logger.warning(f"HTML 轉 TXT 失敗: {str(e)}")
            return False

class EbookConverterGUI:
    """電子書轉檔 GUI 控制器類別"""

    def __init__(self):
        self.converter = EbookConverter()
        self.root = tk.Tk()
        self.setup_window()
        self.create_widgets()

    def setup_window(self):
        """設定主視窗"""
        self.root.title(AppConstants.TITLE)
        self.root.geometry(AppConstants.WINDOW_SIZE)
        self.root.resizable(False, False)

        # 設定視窗圖示（如果有的話）
        try:
            # self.root.iconbitmap('icon.ico')  # 可選：加入圖示
            pass
        except:
            pass

    def create_widgets(self):
        """建立 GUI 元件"""
        # 輸入檔案區域
        self.create_input_section()

        # 輸出格式區域
        self.create_format_section()

        # 輸出檔案區域
        self.create_output_section()

        # 控制按鈕區域
        self.create_control_section()

        # 狀態顯示區域
        self.create_status_section()

        # 進度條
        self.create_progress_section()

    def create_input_section(self):
        """建立輸入檔案選擇區域"""
        tk.Label(self.root, text='選擇輸入檔案：').grid(
            row=0, column=0, padx=10, pady=10, sticky='e'
        )

        self.input_entry = tk.Entry(self.root, width=AppConstants.ENTRY_WIDTH)
        self.input_entry.grid(row=0, column=1, padx=5)

        tk.Button(self.root, text='瀏覽', command=self.select_input_file).grid(
            row=0, column=2, padx=5
        )

    def create_format_section(self):
        """建立輸出格式選擇區域"""
        tk.Label(self.root, text='選擇輸出格式：').grid(
            row=1, column=0, padx=10, pady=10, sticky='e'
        )

        self.format_var = tk.StringVar(value=AppConstants.SUPPORTED_FORMATS[0])
        self.format_menu = ttk.Combobox(
            self.root,
            textvariable=self.format_var,
            values=AppConstants.SUPPORTED_FORMATS,
            state='readonly',
            width=12
        )
        self.format_menu.grid(row=1, column=1, sticky='w', padx=5)

        # 當格式改變時自動更新輸出路徑
        self.format_menu.bind('<<ComboboxSelected>>', self.on_format_changed)

    def create_output_section(self):
        """建立輸出檔案預覽區域"""
        tk.Label(self.root, text='輸出檔案路徑：').grid(
            row=2, column=0, padx=10, pady=10, sticky='e'
        )

        self.output_entry = tk.Entry(self.root, width=AppConstants.ENTRY_WIDTH, state='readonly')
        self.output_entry.grid(row=2, column=1, padx=5, columnspan=2)

    def create_control_section(self):
        """建立控制按鈕區域"""
        self.convert_button = tk.Button(
            self.root,
            text='開始轉檔',
            command=self.start_conversion,
            width=AppConstants.BUTTON_WIDTH,
            bg='#4CAF50',
            fg='white',
            font=('Arial', 10, 'bold')
        )
        self.convert_button.grid(row=3, column=0, columnspan=3, pady=15)

    def create_status_section(self):
        """建立狀態顯示區域"""
        self.status_label = tk.Label(
            self.root,
            text=ConversionStatus.IDLE.value,
            font=('Arial', 9)
        )
        self.status_label.grid(row=5, column=0, columnspan=3, pady=5)

    def create_progress_section(self):
        """建立進度條區域"""
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.root,
            variable=self.progress_var,
            mode='indeterminate',
            length=300
        )
        self.progress_bar.grid(row=4, column=0, columnspan=3, pady=10, padx=20)

    def select_input_file(self):
        """選擇輸入檔案"""
        filetypes = [
            ('電子書檔案', ' '.join(f'*.{fmt}' for fmt in AppConstants.SUPPORTED_FORMATS)),
            ('所有檔案', '*.*')
        ]

        file_path = filedialog.askopenfilename(
            title='選擇要轉換的電子書檔案',
            filetypes=filetypes
        )

        if file_path:
            self.input_entry.delete(0, tk.END)
            self.input_entry.insert(0, file_path)
            # 自動生成輸出檔案路徑
            self.update_output_path()
            self.update_status(ConversionStatus.IDLE, "已選擇輸入檔案")

    def update_output_path(self):
        """根據輸入檔案和選擇的格式自動更新輸出路徑"""
        input_path = self.input_entry.get().strip()
        if not input_path:
            return

        input_file = Path(input_path)
        output_format = self.format_var.get()

        # 生成輸出檔案名稱：原檔名_converted.新格式
        output_filename = f"{input_file.stem}_converted.{output_format}"
        output_path = input_file.parent / output_filename

        # 更新輸出路徑顯示
        self.output_entry.config(state='normal')
        self.output_entry.delete(0, tk.END)
        self.output_entry.insert(0, str(output_path))
        self.output_entry.config(state='readonly')

    def on_format_changed(self, _=None):
        """當輸出格式改變時的回調函數"""
        # _ 參數由 tkinter 事件系統提供，但我們不需要使用它
        self.update_output_path()
        self.update_status(ConversionStatus.IDLE, f"已選擇 {self.format_var.get().upper()} 格式")

    def validate_inputs(self) -> bool:
        """驗證使用者輸入"""
        input_path = self.input_entry.get().strip()

        # 驗證輸入檔案
        if not self.converter.validate_input_file(input_path):
            if not input_path:
                messagebox.showwarning('警告', AppConstants.MESSAGES['select_input'])
            elif not Path(input_path).exists():
                messagebox.showwarning('警告', AppConstants.MESSAGES['file_not_found'])
            else:
                messagebox.showwarning('警告', AppConstants.MESSAGES['invalid_format'])
            return False

        # 檢查輸出路徑是否已生成
        output_path = self.output_entry.get().strip()
        if not output_path:
            messagebox.showwarning('警告', '無法生成輸出檔案路徑，請重新選擇輸入檔案')
            return False

        # 檢查輸出檔案是否已存在
        if Path(output_path).exists():
            result = messagebox.askyesno(
                '檔案已存在',
                f'輸出檔案已存在：\n{Path(output_path).name}\n\n是否要覆蓋？'
            )
            if not result:
                return False

        return True

    def start_conversion(self):
        """開始轉檔流程"""
        # 檢查是否正在轉檔
        if self.converter.is_converting:
            messagebox.showinfo('提示', '轉檔正在進行中，請稍候...')
            return

        # 驗證輸入
        if not self.validate_inputs():
            return

        # 取得參數
        input_path = self.input_entry.get().strip()
        output_path = self.output_entry.get().strip()

        # 開始轉檔
        self.set_converting_state(True)
        self.converter.convert_ebook_async(
            input_path,
            output_path,
            self.on_conversion_progress
        )

    def on_conversion_progress(self, status: ConversionStatus, message: str):
        """轉檔進度回調函數"""
        # 在主執行緒中更新 GUI
        self.root.after(0, lambda: self._update_conversion_status(status, message))

    def _update_conversion_status(self, status: ConversionStatus, message: str):
        """更新轉檔狀態（在主執行緒中執行）"""
        self.update_status(status, message)

        if status == ConversionStatus.CONVERTING:
            self.progress_bar.start(10)  # 開始進度條動畫
        else:
            self.progress_bar.stop()  # 停止進度條動畫
            self.set_converting_state(False)

            if status == ConversionStatus.SUCCESS:
                messagebox.showinfo('成功', message)
                # 詢問是否開啟輸出資料夾
                if messagebox.askyesno('完成', '轉檔完成！是否開啟輸出資料夾？'):
                    self.open_output_folder()
            elif status == ConversionStatus.FAILED:
                messagebox.showerror('錯誤', message)

    def set_converting_state(self, is_converting: bool):
        """設定轉檔狀態"""
        if is_converting:
            self.convert_button.config(
                text='轉檔中...',
                state='disabled',
                bg='#FF9800'
            )
        else:
            self.convert_button.config(
                text='開始轉檔',
                state='normal',
                bg='#4CAF50'
            )

    def update_status(self, status: ConversionStatus, message: str = ""):
        """更新狀態顯示"""
        display_text = message if message else status.value
        self.status_label.config(text=display_text)

        # 根據狀態設定顏色
        color_map = {
            ConversionStatus.IDLE: 'black',
            ConversionStatus.CONVERTING: 'blue',
            ConversionStatus.SUCCESS: 'green',
            ConversionStatus.FAILED: 'red'
        }
        self.status_label.config(fg=color_map.get(status, 'black'))

    def open_output_folder(self):
        """開啟輸出檔案所在資料夾"""
        try:
            output_path = self.output_entry.get().strip()
            if output_path:
                folder_path = Path(output_path).parent
                if folder_path.exists():
                    import platform
                    system = platform.system()
                    if system == "Windows":
                        subprocess.run(['explorer', str(folder_path)])
                    elif system == "Darwin":  # macOS
                        subprocess.run(['open', str(folder_path)])
                    else:  # Linux
                        subprocess.run(['xdg-open', str(folder_path)])
        except Exception as e:
            logger.error(f"無法開啟資料夾: {e}")

    def run(self):
        """啟動應用程式"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            logger.info("使用者中斷程式")
        except Exception as e:
            logger.error(f"應用程式錯誤: {e}")
            messagebox.showerror('錯誤', f'應用程式發生錯誤：{e}')


def main():
    """主程式進入點"""
    try:
        # 建立並啟動 GUI 應用程式
        app = EbookConverterGUI()
        app.run()
    except Exception as e:
        logger.error(f"程式啟動失敗: {e}")
        print(f"程式啟動失敗: {e}")


if __name__ == '__main__':
    main()