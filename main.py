"""
電子書轉檔工具 - 優化版本
採用 MVC 架構模式，遵循 Clean Code 原則
"""

import tkinter as tk
from tkinter import filedialog, ttk
import subprocess
import threading
from pathlib import Path
from typing import Callable, Dict
from enum import Enum
import logging

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ConversionStatus(Enum):
    """轉檔狀態枚舉"""
    IDLE = "待機中"
    CONVERTING = "轉檔中..."
    SUCCESS = "轉檔完成！"
    FAILED = "轉檔失敗"


class AppConstants:
    """應用程式常數"""
    TITLE = "電子書轉檔工具"
    WINDOW_SIZE = "500x300"
    ENTRY_WIDTH = 40
    BUTTON_WIDTH = 20

    # 支援的格式 (按可用性排序)
    SUPPORTED_FORMATS = [
        'txt', 'html', 'md',  # 內建轉換器支援
        'epub', 'pdf', 'mobi', 'azw3', 'docx', 'fb2', 'cbz', 'cbr'  # 需要外部工具
    ]

    # 內建轉換器支援的格式
    BUILTIN_SUPPORTED = ['txt', 'html', 'md', 'markdown']

    # 擴展支援的格式（需要額外處理）
    EXTENDED_SUPPORTED = ['epub']  # 基本 EPUB 文字提取

    # 訊息文字
    MESSAGES = {
        'select_input': '請選擇正確的輸入檔案！',
        'invalid_format': '不支援的檔案格式！',
        'file_not_found': '找不到指定的檔案！',
        'conversion_success': '轉檔完成！',
        'conversion_failed': '轉檔失敗',
        'calibre_not_found': '找不到 Calibre，請確認已正確安裝並設定環境變數',
        'output_exists': '輸出檔案已存在，是否要覆蓋？',
    }


class EbookConverter:
    """電子書轉檔核心邏輯類別"""

    def __init__(self):
        self.is_converting = False
        self.available_engines = self._detect_available_engines()

    def _detect_available_engines(self) -> Dict[str, bool]:
        """檢測可用的轉檔引擎"""
        engines = {
            'calibre': self.check_calibre_available(),
            'pandoc': self._check_pandoc_available(),
            'python_libs': self._check_python_libraries(),
            'builtin': True,  # 內建轉換器永遠可用
        }
        return engines

    def _check_pandoc_available(self) -> bool:
        """檢查 Pandoc 是否可用"""
        try:
            result = subprocess.run(['pandoc', '--version'],
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False

    def _check_python_libraries(self) -> bool:
        """檢查 Python 電子書處理庫是否可用"""
        try:
            # 檢查是否有可用的 Python 庫
            import importlib
            libraries = [
                ('pypdf', 'pypdf'),
                ('pypdf2', 'PyPDF2'),
                ('ebooklib', 'ebooklib'),
                ('python-docx', 'docx'),
                ('beautifulsoup4', 'bs4'),
                ('lxml', 'lxml'),
                ('markdown', 'markdown')
            ]
            available = []

            for lib_name, import_name in libraries:
                try:
                    importlib.import_module(import_name)
                    available.append(lib_name)
                except ImportError:
                    pass

            logger.info(f"可用的 Python 庫: {available}")
            return len(available) > 0
        except Exception as e:
            logger.warning(f"檢查 Python 庫時發生錯誤: {e}")
            return False

    def validate_input_file(self, file_path: str) -> bool:
        """驗證輸入檔案"""
        if not file_path:
            return False

        path = Path(file_path)
        if not path.exists() or not path.is_file():
            return False

        # 檢查檔案格式
        file_extension = path.suffix.lower().lstrip('.')
        return file_extension in AppConstants.SUPPORTED_FORMATS

    def validate_output_path(self, output_path: str) -> bool:
        """驗證輸出路徑"""
        if not output_path:
            return False

        path = Path(output_path)
        # 檢查父目錄是否存在
        return path.parent.exists()

    def check_calibre_available(self) -> bool:
        """檢查 Calibre 是否可用"""
        try:
            result = subprocess.run(['ebook-convert', '--version'],
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False

    def convert_ebook_async(self, input_path: str, output_path: str,
                           progress_callback: Callable[[ConversionStatus, str], None]) -> None:
        """非同步轉檔方法"""
        def conversion_thread():
            try:
                self.is_converting = True
                progress_callback(ConversionStatus.CONVERTING, "正在轉檔...")

                # 選擇可用的轉檔引擎
                success = False

                # 嘗試 Calibre
                if self.available_engines.get('calibre', False):
                    success = self._convert_with_calibre(input_path, output_path, progress_callback)

                # 如果 Calibre 失敗，嘗試 Pandoc
                if not success and self.available_engines.get('pandoc', False):
                    success = self._convert_with_pandoc(input_path, output_path, progress_callback)

                # 如果都失敗，嘗試 Python 庫
                if not success and self.available_engines.get('python_libs', False):
                    success = self._convert_with_python_libs(input_path, output_path, progress_callback)

                # 最後嘗試內建轉換器
                if not success and self.available_engines.get('builtin', False):
                    success = self._convert_with_builtin(input_path, output_path, progress_callback)

                # 如果所有方法都失敗
                if not success:
                    available_engines = [name for name, available in self.available_engines.items() if available]
                    if not available_engines:
                        progress_callback(ConversionStatus.FAILED,
                                        "找不到可用的轉檔引擎。請安裝 Calibre、Pandoc 或相關 Python 庫")
                    else:
                        progress_callback(ConversionStatus.FAILED, "所有可用的轉檔引擎都無法處理此檔案格式")

            except Exception as e:
                progress_callback(ConversionStatus.FAILED, f"發生例外：{str(e)}")
                logger.error(f"轉檔例外: {str(e)}")
            finally:
                self.is_converting = False

        # 在新執行緒中執行轉檔
        thread = threading.Thread(target=conversion_thread, daemon=True)
        thread.start()

    def _convert_with_calibre(self, input_path: str, output_path: str,
                             progress_callback: Callable[[ConversionStatus, str], None]) -> bool:
        """使用 Calibre 轉檔"""
        try:
            cmd = ['ebook-convert', input_path, output_path]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                progress_callback(ConversionStatus.SUCCESS, "Calibre 轉檔完成！")
                logger.info(f"Calibre 轉檔成功: {input_path} -> {output_path}")
                return True
            else:
                logger.warning(f"Calibre 轉檔失敗: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.warning("Calibre 轉檔超時")
            return False
        except Exception as e:
            logger.warning(f"Calibre 轉檔例外: {str(e)}")
            return False

    def _convert_with_pandoc(self, input_path: str, output_path: str,
                            progress_callback: Callable[[ConversionStatus, str], None]) -> bool:
        """使用 Pandoc 轉檔"""
        try:
            # Pandoc 支援的格式對應
            input_ext = Path(input_path).suffix.lower().lstrip('.')
            output_ext = Path(output_path).suffix.lower().lstrip('.')

            # 檢查 Pandoc 是否支援這個轉換
            pandoc_formats = {
                'txt': 'plain', 'html': 'html', 'epub': 'epub',
                'docx': 'docx', 'pdf': 'pdf'
            }

            if input_ext not in pandoc_formats or output_ext not in pandoc_formats:
                return False

            cmd = [
                'pandoc',
                input_path,
                '-f', pandoc_formats[input_ext],
                '-t', pandoc_formats[output_ext],
                '-o', output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                progress_callback(ConversionStatus.SUCCESS, "Pandoc 轉檔完成！")
                logger.info(f"Pandoc 轉檔成功: {input_path} -> {output_path}")
                return True
            else:
                logger.warning(f"Pandoc 轉檔失敗: {result.stderr}")
                return False

        except Exception as e:
            logger.warning(f"Pandoc 轉檔例外: {str(e)}")
            return False

    def _convert_with_python_libs(self, input_path: str, output_path: str,
                                 progress_callback: Callable[[ConversionStatus, str], None]) -> bool:
        """使用 Python 庫轉檔"""
        try:
            input_ext = Path(input_path).suffix.lower().lstrip('.')
            output_ext = Path(output_path).suffix.lower().lstrip('.')

            # 簡單的文字轉換
            if input_ext == 'txt' and output_ext == 'html':
                return self._txt_to_html(input_path, output_path, progress_callback)
            elif input_ext == 'html' and output_ext == 'txt':
                return self._html_to_txt(input_path, output_path, progress_callback)

            # 其他格式暫不支援
            return False

        except Exception as e:
            logger.warning(f"Python 庫轉檔例外: {str(e)}")
            return False

    def _txt_to_html(self, input_path: str, output_path: str,
                     progress_callback: Callable[[ConversionStatus, str], None]) -> bool:
        """TXT 轉 HTML"""
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                content = f.read()

            html_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{Path(input_path).stem}</title>
</head>
<body>
    <pre>{content}</pre>
</body>
</html>"""

            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            progress_callback(ConversionStatus.SUCCESS, "Python 內建轉檔完成！")
            return True

        except Exception as e:
            logger.warning(f"TXT 轉 HTML 失敗: {str(e)}")
            return False

    def _html_to_txt(self, input_path: str, output_path: str,
                     progress_callback: Callable[[ConversionStatus, str], None]) -> bool:
        """HTML 轉 TXT"""
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 簡單的 HTML 標籤移除
            import re
            text_content = re.sub(r'<[^>]+>', '', content)
            text_content = re.sub(r'\s+', ' ', text_content).strip()

            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(text_content)

            progress_callback(ConversionStatus.SUCCESS, "Python 內建轉檔完成！")
            return True

        except Exception as e:
            logger.warning(f"HTML 轉 TXT 失敗: {str(e)}")
            return False

    def _convert_with_builtin(self, input_path: str, output_path: str,
                             progress_callback: Callable[[ConversionStatus, str], None]) -> bool:
        """使用內建轉換器"""
        try:
            input_ext = Path(input_path).suffix.lower().lstrip('.')
            output_ext = Path(output_path).suffix.lower().lstrip('.')

            logger.info(f"內建轉換器嘗試轉換: {input_ext} → {output_ext}")

            # 支援的內建轉換
            if input_ext == 'txt' and output_ext == 'html':
                return self._txt_to_html(input_path, output_path, progress_callback)
            elif input_ext == 'html' and output_ext == 'txt':
                return self._html_to_txt(input_path, output_path, progress_callback)
            elif input_ext == 'txt' and output_ext == 'md':
                return self._txt_to_markdown(input_path, output_path, progress_callback)
            # 擴展支援：任何文字格式都可以轉為 HTML
            elif input_ext in ['txt', 'md', 'markdown'] and output_ext == 'html':
                return self._txt_to_html(input_path, output_path, progress_callback)
            # 擴展支援：HTML 可以轉為任何文字格式
            elif input_ext == 'html' and output_ext in ['txt', 'md', 'markdown']:
                return self._html_to_txt(input_path, output_path, progress_callback)
            # 通用文字轉換：嘗試將任何文字檔案轉為其他文字格式
            elif input_ext in ['txt', 'md', 'html'] and output_ext in ['txt', 'md', 'html']:
                return self._universal_text_convert(input_path, output_path, input_ext, output_ext, progress_callback)
            # EPUB 文字提取
            elif input_ext == 'epub' and output_ext in ['txt', 'html', 'md']:
                return self._extract_epub_text(input_path, output_path, output_ext, progress_callback)
            # EPUB → PDF 特殊處理
            elif input_ext == 'epub' and output_ext == 'pdf':
                return self._convert_epub_to_pdf(input_path, output_path, progress_callback)
            else:
                logger.info(f"內建轉換器不支援 {input_ext} → {output_ext}")
                progress_callback(ConversionStatus.FAILED,
                                f"內建轉換器不支援 {input_ext.upper()} → {output_ext.upper()} 轉換\n\n"
                                f"建議安裝 Pandoc 或 Calibre 以支援更多格式")
                return False

        except Exception as e:
            logger.warning(f"內建轉換器例外: {str(e)}")
            progress_callback(ConversionStatus.FAILED, f"內建轉換器錯誤: {str(e)}")
            return False

    def _txt_to_markdown(self, input_path: str, output_path: str,
                        progress_callback: Callable[[ConversionStatus, str], None]) -> bool:
        """TXT 轉 Markdown"""
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 簡單的 Markdown 格式化
            lines = content.split('\n')
            markdown_lines = []

            for line in lines:
                line = line.strip()
                if line:
                    # 如果是標題（全大寫或特殊格式）
                    if line.isupper() or (line.startswith('第') and '章' in line):
                        markdown_lines.append(f"# {line}")
                    elif line.endswith('：') or line.endswith(':'):
                        markdown_lines.append(f"## {line}")
                    else:
                        markdown_lines.append(line)
                else:
                    markdown_lines.append("")

            with open(output_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(markdown_lines))

            progress_callback(ConversionStatus.SUCCESS, "內建轉換器轉檔完成！")
            return True

        except Exception as e:
            logger.warning(f"TXT 轉 Markdown 失敗: {str(e)}")
            return False

    def _universal_text_convert(self, input_path: str, output_path: str,
                               input_ext: str, output_ext: str,
                               progress_callback: Callable[[ConversionStatus, str], None]) -> bool:
        """通用文字格式轉換"""
        try:
            # 讀取輸入檔案
            with open(input_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 根據輸出格式處理內容
            if output_ext == 'html':
                # 轉為 HTML
                if input_ext == 'md' or input_ext == 'markdown':
                    # Markdown 轉 HTML (簡單版本)
                    html_content = self._markdown_to_html(content, Path(input_path).stem)
                else:
                    # 純文字轉 HTML
                    html_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{Path(input_path).stem}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }}
        pre {{ white-space: pre-wrap; }}
    </style>
</head>
<body>
    <h1>{Path(input_path).stem}</h1>
    <pre>{content}</pre>
</body>
</html>"""

                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)

            elif output_ext in ['txt', 'md', 'markdown']:
                # 轉為純文字或 Markdown
                if input_ext == 'html':
                    # HTML 轉文字
                    import re
                    text_content = re.sub(r'<[^>]+>', '', content)
                    text_content = re.sub(r'\s+', ' ', text_content).strip()
                else:
                    # 保持原內容
                    text_content = content

                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(text_content)
            else:
                return False

            progress_callback(ConversionStatus.SUCCESS, f"內建轉換器完成 {input_ext.upper()} → {output_ext.upper()} 轉換")
            return True

        except Exception as e:
            logger.warning(f"通用文字轉換失敗: {str(e)}")
            return False

    def _markdown_to_html(self, markdown_content: str, title: str) -> str:
        """簡單的 Markdown 轉 HTML"""
        lines = markdown_content.split('\n')
        html_lines = []

        for line in lines:
            line = line.strip()
            if line.startswith('# '):
                html_lines.append(f"<h1>{line[2:]}</h1>")
            elif line.startswith('## '):
                html_lines.append(f"<h2>{line[3:]}</h2>")
            elif line.startswith('### '):
                html_lines.append(f"<h3>{line[4:]}</h3>")
            elif line.startswith('- ') or line.startswith('* '):
                html_lines.append(f"<li>{line[2:]}</li>")
            elif line:
                html_lines.append(f"<p>{line}</p>")
            else:
                html_lines.append("<br>")

        return f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{title}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }}
        h1, h2, h3 {{ color: #333; }}
        li {{ margin: 5px 0; }}
    </style>
</head>
<body>
    {''.join(html_lines)}
</body>
</html>"""

    def _extract_epub_text(self, input_path: str, output_path: str, output_ext: str,
                          progress_callback: Callable[[ConversionStatus, str], None]) -> bool:
        """從 EPUB 檔案提取文字內容（完整版本）"""
        try:
            import zipfile
            import xml.etree.ElementTree as ET

            progress_callback(ConversionStatus.CONVERTING, "正在解析 EPUB 檔案...")

            # EPUB 是一個 ZIP 檔案
            with zipfile.ZipFile(input_path, 'r') as epub_zip:
                # 第一步：找到 container.xml 並解析 OPF 檔案位置
                try:
                    container_content = epub_zip.read('META-INF/container.xml').decode('utf-8')
                    container_root = ET.fromstring(container_content)

                    # 找到 OPF 檔案路徑
                    opf_path = None
                    for rootfile in container_root.findall('.//{urn:oasis:names:tc:opendocument:xmlns:container}rootfile'):
                        if rootfile.get('media-type') == 'application/oebps-package+xml':
                            opf_path = rootfile.get('full-path')
                            break

                    if not opf_path:
                        raise Exception("未找到 OPF 檔案")

                except Exception as e:
                    logger.warning(f"解析 container.xml 失敗: {e}")
                    # 回退到簡單方法
                    return self._extract_epub_text_simple(input_path, output_path, output_ext, progress_callback)

                # 第二步：解析 OPF 檔案獲取章節順序
                try:
                    opf_content = epub_zip.read(opf_path).decode('utf-8')
                    opf_root = ET.fromstring(opf_content)

                    # 定義命名空間
                    namespaces = {'opf': 'http://www.idpf.org/2007/opf'}

                    # 獲取 manifest 項目（ID 到檔案的映射）
                    manifest_items = {}
                    for item in opf_root.findall('.//opf:item', namespaces):
                        item_id = item.get('id')
                        href = item.get('href')
                        media_type = item.get('media-type')
                        if item_id and href:
                            manifest_items[item_id] = {
                                'href': href,
                                'media_type': media_type
                            }

                    # 獲取 spine 順序（章節閱讀順序）
                    spine_order = []
                    for itemref in opf_root.findall('.//opf:itemref', namespaces):
                        idref = itemref.get('idref')
                        if idref in manifest_items:
                            spine_order.append(manifest_items[idref]['href'])

                    progress_callback(ConversionStatus.CONVERTING, f"找到 {len(spine_order)} 個章節檔案")

                except Exception as e:
                    logger.warning(f"解析 OPF 檔案失敗: {e}")
                    # 回退到簡單方法
                    return self._extract_epub_text_simple(input_path, output_path, output_ext, progress_callback)

                # 第三步：按順序提取每個章節的文字
                all_text = []
                all_text.append(f"電子書檔案: {Path(input_path).name}")
                all_text.append("")

                # 獲取 OPF 檔案的基礎路徑
                opf_dir = str(Path(opf_path).parent) if Path(opf_path).parent != Path('.') else ''

                processed_files = 0
                total_chapters = len(spine_order)

                progress_callback(ConversionStatus.CONVERTING, f"開始處理 {total_chapters} 個章節...")

                for i, href in enumerate(spine_order, 1):
                    progress_callback(ConversionStatus.CONVERTING, f"正在處理第 {i}/{total_chapters} 章: {href}")

                    try:
                        # 嘗試多種路徑組合來讀取檔案
                        html_content = None
                        attempted_paths = []

                        # 可能的路徑組合
                        possible_paths = [
                            href,                           # 直接路徑
                            f"{opf_dir}/{href}" if opf_dir else href,  # OPF 目錄 + href
                            f"OEBPS/{href}",               # 常見的 OEBPS 目錄
                            f"Text/{href}",                # 常見的 Text 目錄
                            f"content/{href}",             # 常見的 content 目錄
                            f"src/{href}",                 # 常見的 src 目錄
                        ]

                        for attempt_path in possible_paths:
                            try:
                                html_content = epub_zip.read(attempt_path).decode('utf-8', errors='replace')
                                logger.info(f"成功讀取章節 {i}: {attempt_path}")
                                break
                            except KeyError:
                                attempted_paths.append(attempt_path)
                                continue

                        if not html_content:
                            logger.warning(f"無法找到章節檔案 {href}，嘗試過的路徑: {attempted_paths}")
                            # 即使找不到檔案，也繼續處理下一個
                            continue

                        # 提取文字內容
                        text = self._extract_text_from_html_enhanced(html_content)

                        if text.strip():
                            all_text.append(f"=== 第 {i} 章：{href} ===")
                            all_text.append(text)
                            all_text.append("")
                            processed_files += 1

                            logger.info(f"成功處理章節 {i}: {len(text)} 字符")
                        else:
                            logger.warning(f"章節 {i} 提取的文字為空")

                    except Exception as e:
                        logger.warning(f"處理章節 {href} 時發生錯誤: {e}")
                        # 記錄錯誤但繼續處理下一個章節
                        continue

                progress_callback(ConversionStatus.CONVERTING, f"章節處理完成: {processed_files}/{total_chapters}")

                if processed_files == 0:
                    # 如果沒有成功處理任何檔案，回退到簡單方法
                    logger.warning("按順序提取失敗，回退到簡單方法")
                    return self._extract_epub_text_simple(input_path, output_path, output_ext, progress_callback)

                # 合併所有文字
                full_text = '\n'.join(all_text)

                # 根據輸出格式處理
                if output_ext == 'html':
                    output_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>EPUB 內容提取</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }}
        h1 {{ color: #333; }}
        .epub-info {{ background: #f5f5f5; padding: 15px; margin-bottom: 20px; }}
    </style>
</head>
<body>
    <div class="epub-info">
        <h1>EPUB 內容提取</h1>
        <p><strong>來源:</strong> {Path(input_path).name}</p>
    </div>
    <pre>{full_text}</pre>
</body>
</html>"""
                elif output_ext == 'md':
                    output_content = f"""# EPUB 內容提取

**來源:** {Path(input_path).name}

---

{full_text}"""
                else:  # txt
                    output_content = full_text

                # 寫入輸出檔案
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(output_content)

                progress_callback(ConversionStatus.SUCCESS, f"EPUB 文字提取完成！處理了 {processed_files} 個檔案")
                return True

        except Exception as e:
            logger.warning(f"EPUB 文字提取失敗: {str(e)}")
            progress_callback(ConversionStatus.FAILED, f"EPUB 處理失敗: {str(e)}\n建議安裝 Pandoc 或 Calibre 以獲得更好的 EPUB 支援")
            return False

    def _extract_epub_text_simple(self, input_path: str, output_path: str, output_ext: str,
                                 progress_callback: Callable[[ConversionStatus, str], None]) -> bool:
        """簡單的 EPUB 文字提取方法（回退方案）"""
        try:
            import zipfile

            progress_callback(ConversionStatus.CONVERTING, "使用簡單方法解析 EPUB...")

            with zipfile.ZipFile(input_path, 'r') as epub_zip:
                # 找到所有 HTML 檔案
                html_files = []
                for file_info in epub_zip.filelist:
                    if file_info.filename.endswith(('.html', '.xhtml', '.htm')):
                        html_files.append(file_info.filename)

                progress_callback(ConversionStatus.CONVERTING, f"找到 {len(html_files)} 個 HTML 檔案")

                # 提取文字內容
                all_text = []
                all_text.append(f"電子書檔案: {Path(input_path).name}")
                all_text.append("")

                processed_count = 0
                for i, html_file in enumerate(html_files, 1):
                    progress_callback(ConversionStatus.CONVERTING, f"處理檔案 {i}/{len(html_files)}: {html_file}")

                    try:
                        html_content = epub_zip.read(html_file).decode('utf-8', errors='replace')
                        text = self._extract_text_from_html_enhanced(html_content)
                        if text.strip():
                            all_text.append(f"=== {html_file} ===")
                            all_text.append(text)
                            all_text.append("")
                            processed_count += 1
                            logger.info(f"成功處理檔案 {i}: {html_file}, {len(text)} 字符")
                        else:
                            logger.warning(f"檔案 {html_file} 提取的文字為空")
                    except Exception as e:
                        logger.warning(f"無法處理 {html_file}: {e}")
                        continue

                progress_callback(ConversionStatus.CONVERTING, f"簡單方法處理完成: {processed_count}/{len(html_files)} 個檔案")

                if len(all_text) <= 2:
                    all_text.append("無法提取詳細內容，這是一個 EPUB 檔案。")

                # 合併文字並輸出
                full_text = '\n'.join(all_text)

                # 根據格式輸出
                if output_ext == 'html':
                    output_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>EPUB 內容提取</title>
</head>
<body>
    <h1>EPUB 內容提取</h1>
    <pre>{full_text}</pre>
</body>
</html>"""
                elif output_ext == 'md':
                    output_content = f"# EPUB 內容提取\n\n{full_text}"
                else:
                    output_content = full_text

                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(output_content)

                progress_callback(ConversionStatus.SUCCESS, f"EPUB 文字提取完成！處理了 {len(html_files)} 個檔案")
                return True

        except Exception as e:
            logger.warning(f"簡單 EPUB 提取失敗: {e}")
            progress_callback(ConversionStatus.FAILED, f"EPUB 處理失敗: {str(e)}")
            return False

    def _extract_text_from_html_enhanced(self, html_content: str) -> str:
        """增強的 HTML 文字提取，最大化內容保留"""
        try:
            import re
            # 嘗試使用 BeautifulSoup 進行更好的解析
            try:
                from bs4 import BeautifulSoup

                soup = BeautifulSoup(html_content, 'html.parser')

                # 移除不需要的標籤但保留內容
                for script in soup(["script", "style", "meta", "link"]):
                    script.decompose()

                # 多層次文字提取策略
                text_parts = []

                # 策略 1: 按結構提取
                structured_content = self._extract_structured_content(soup)
                if structured_content:
                    text_parts.extend(structured_content)

                # 策略 2: 如果結構化提取內容不足，使用全文提取
                if not text_parts or len('\n'.join(text_parts)) < 100:
                    full_text = soup.get_text(separator='\n', strip=True)
                    if full_text:
                        # 清理並分割文字
                        lines = [line.strip() for line in full_text.split('\n') if line.strip()]
                        text_parts = lines

                # 策略 3: 如果還是不夠，嘗試提取所有文字節點
                if not text_parts or len('\n'.join(text_parts)) < 50:
                    all_text = self._extract_all_text_nodes(soup)
                    if all_text:
                        text_parts = [all_text]

                # 最終清理和合併
                final_text = '\n'.join(text_parts)

                # 清理多餘的空行
                final_text = re.sub(r'\n\s*\n', '\n\n', final_text)
                final_text = final_text.strip()

                return final_text

            except ImportError:
                # 如果沒有 BeautifulSoup，使用增強的正則表達式方法
                return self._extract_text_regex_enhanced(html_content)

        except Exception as e:
            logger.warning(f"增強文字提取失敗: {e}")
            # 最後回退到基本方法
            return self._extract_text_from_html(html_content)

    def _extract_structured_content(self, soup):
        """提取結構化內容"""
        text_parts = []

        try:
            # 按順序處理不同元素
            elements_processed = set()

            # 處理標題
            for heading in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
                if heading not in elements_processed:
                    text = heading.get_text(strip=True)
                    if text:
                        text_parts.append(f"\n{text}\n")
                        elements_processed.add(heading)

            # 處理段落
            for para in soup.find_all('p'):
                if para not in elements_processed:
                    text = para.get_text(strip=True)
                    if text and len(text) > 3:  # 忽略太短的段落
                        text_parts.append(text)
                        elements_processed.add(para)

            # 處理 div 中的文字（很多內容可能在 div 中）
            for div in soup.find_all('div'):
                if div not in elements_processed:
                    # 只提取直接文字，不包含子元素
                    direct_text = ''.join(div.find_all(text=True, recursive=False)).strip()
                    if direct_text and len(direct_text) > 10:
                        text_parts.append(direct_text)
                        elements_processed.add(div)

            # 處理列表
            for ul in soup.find_all(['ul', 'ol']):
                if ul not in elements_processed:
                    for li in ul.find_all('li'):
                        text = li.get_text(strip=True)
                        if text:
                            text_parts.append(f"• {text}")
                    elements_processed.add(ul)

            # 處理表格
            for table in soup.find_all('table'):
                if table not in elements_processed:
                    for row in table.find_all('tr'):
                        cells = [cell.get_text(strip=True) for cell in row.find_all(['td', 'th'])]
                        if any(cells):
                            text_parts.append(" | ".join(cells))
                    elements_processed.add(table)

            # 處理引用
            for blockquote in soup.find_all('blockquote'):
                if blockquote not in elements_processed:
                    text = blockquote.get_text(strip=True)
                    if text:
                        text_parts.append(f"「{text}」")
                        elements_processed.add(blockquote)

            # 處理預格式化文字
            for pre in soup.find_all('pre'):
                if pre not in elements_processed:
                    text = pre.get_text(strip=True)
                    if text:
                        text_parts.append(text)
                        elements_processed.add(pre)

            return text_parts

        except Exception as e:
            logger.warning(f"結構化內容提取失敗: {e}")
            return []

    def _extract_all_text_nodes(self, soup):
        """提取所有文字節點（最後手段）"""
        try:
            import re
            # 獲取所有文字節點
            all_texts = soup.find_all(text=True)

            # 過濾和清理
            valid_texts = []
            for text in all_texts:
                # 跳過空白和很短的文字
                cleaned = text.strip()
                if cleaned and len(cleaned) > 2:
                    # 跳過看起來像標籤或屬性的文字
                    if not re.match(r'^[<>{}[\]()]+$', cleaned):
                        valid_texts.append(cleaned)

            return ' '.join(valid_texts)

        except Exception as e:
            logger.warning(f"全文字節點提取失敗: {e}")
            return ""

    def _extract_text_regex_enhanced(self, html_content):
        """增強的正則表達式文字提取"""
        try:
            import re
            # 保存重要內容的標記
            important_patterns = [
                (r'<h[1-6][^>]*>(.*?)</h[1-6]>', r'\n\1\n'),  # 標題
                (r'<p[^>]*>(.*?)</p>', r'\1\n'),              # 段落
                (r'<li[^>]*>(.*?)</li>', r'• \1\n'),          # 列表項
                (r'<td[^>]*>(.*?)</td>', r'\1 | '),           # 表格單元格
                (r'<blockquote[^>]*>(.*?)</blockquote>', r'「\1」\n'),  # 引用
            ]

            # 先處理重要標籤
            processed_content = html_content
            for pattern, replacement in important_patterns:
                processed_content = re.sub(pattern, replacement, processed_content, flags=re.DOTALL | re.IGNORECASE)

            # 移除 script 和 style 標籤
            processed_content = re.sub(r'<script[^>]*>.*?</script>', '', processed_content, flags=re.DOTALL | re.IGNORECASE)
            processed_content = re.sub(r'<style[^>]*>.*?</style>', '', processed_content, flags=re.DOTALL | re.IGNORECASE)

            # 移除其他 HTML 標籤
            text = re.sub(r'<[^>]+>', '', processed_content)

            # 清理空白字符
            text = re.sub(r'\s+', ' ', text)
            text = re.sub(r'\n\s*\n', '\n\n', text)

            return text.strip()

        except Exception as e:
            logger.warning(f"增強正則提取失敗: {e}")
            return html_content

    def _extract_text_from_html(self, html_content: str) -> str:
        """從 HTML 內容提取純文字"""
        try:
            import re

            # 移除 script 和 style 標籤
            html_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
            html_content = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.DOTALL | re.IGNORECASE)

            # 移除所有 HTML 標籤
            text = re.sub(r'<[^>]+>', '', html_content)

            # 清理空白字符
            text = re.sub(r'\s+', ' ', text)
            text = text.strip()

            return text

        except Exception:
            return html_content

    def _register_chinese_fonts(self):
        """註冊中文字體以解決亂碼問題"""
        try:
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            import os

            # 嘗試註冊系統中文字體
            font_paths = [
                # Windows 系統字體
                'C:/Windows/Fonts/msyh.ttc',      # 微軟雅黑
                'C:/Windows/Fonts/simsun.ttc',    # 宋體
                'C:/Windows/Fonts/simhei.ttf',    # 黑體
                'C:/Windows/Fonts/kaiu.ttf',      # 標楷體
                # 其他可能的字體路徑
                'C:/Windows/Fonts/NotoSansCJK-Regular.ttc',
                'C:/Windows/Fonts/SourceHanSans-Regular.ttc'
            ]

            font_registered = False

            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        # 註冊字體
                        if font_path.endswith('.ttc'):
                            # TTC 字體需要指定子字體索引
                            pdfmetrics.registerFont(TTFont('ChineseFont', font_path, subfontIndex=0))
                        else:
                            pdfmetrics.registerFont(TTFont('ChineseFont', font_path))

                        font_registered = True
                        logger.info(f"成功註冊中文字體: {font_path}")
                        break
                    except Exception as e:
                        logger.warning(f"註冊字體失敗 {font_path}: {e}")
                        continue

            if not font_registered:
                logger.warning("未找到可用的中文字體，將使用默認字體")

        except Exception as e:
            logger.warning(f"字體註冊過程失敗: {e}")

    def _clean_text_for_pdf(self, text: str) -> str:
        """清理文字以避免 PDF 生成時的亂碼問題"""
        try:
            import html
            import re

            # 解碼 HTML 實體
            text = html.unescape(text)

            # 移除或替換可能導致問題的字符
            # 替換常見的問題字符
            replacements = {
                '\u2018': "'",  # 左單引號
                '\u2019': "'",  # 右單引號
                '\u201c': '"',  # 左雙引號
                '\u201d': '"',  # 右雙引號
                '\u2013': '-',  # en dash
                '\u2014': '--', # em dash
                '\u2026': '...', # 省略號
                '\u00a0': ' ',  # 不間斷空格
            }

            for old, new in replacements.items():
                text = text.replace(old, new)

            # 移除控制字符（保留換行和製表符）
            text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f]', '', text)

            # 確保文字是有效的 UTF-8
            text = text.encode('utf-8', errors='replace').decode('utf-8')

            return text

        except Exception as e:
            logger.warning(f"文字清理失敗: {e}")
            return text

    def _make_text_safe_for_pdf(self, text: str) -> str:
        """讓文字安全地用於 PDF 生成，避免亂碼"""
        try:
            import html
            import xml.sax.saxutils as saxutils

            # 確保輸入是字符串
            if not isinstance(text, str):
                text = str(text)

            # 解碼 HTML 實體
            text = html.unescape(text)

            # 轉義 XML/HTML 特殊字符以避免 ReportLab 解析錯誤
            text = saxutils.escape(text)

            # 移除或替換可能導致 PDF 問題的字符
            problematic_chars = {
                '\x00': '',     # NULL 字符
                '\x0b': ' ',    # 垂直製表符
                '\x0c': ' ',    # 換頁符
                '\ufeff': '',   # BOM 字符
                '\u200b': '',   # 零寬度空格
                '\u200c': '',   # 零寬度非連接符
                '\u200d': '',   # 零寬度連接符
                '\u2060': '',   # 字符連接抑制符
            }

            for char, replacement in problematic_chars.items():
                text = text.replace(char, replacement)

            # 確保文字長度合理（避免過長的行導致問題）
            if len(text) > 1000:
                text = text[:997] + '...'

            # 最終確保是有效的 UTF-8
            text = text.encode('utf-8', errors='replace').decode('utf-8')

            return text

        except Exception as e:
            logger.warning(f"文字安全處理失敗: {e}")
            # 如果所有處理都失敗，返回一個安全的替代文字
            return "（文字處理失敗）"

    def _convert_epub_to_pdf(self, input_path: str, output_path: str,
                            progress_callback: Callable[[ConversionStatus, str], None]) -> bool:
        """EPUB → PDF 轉換（透過 HTML 中介）"""
        try:
            progress_callback(ConversionStatus.CONVERTING, "正在進行 EPUB → PDF 轉換...")

            # 第一步：EPUB → HTML
            html_path = output_path.replace('.pdf', '_temp.html')

            progress_callback(ConversionStatus.CONVERTING, "步驟 1/2: 正在將 EPUB 轉為 HTML...")

            if not self._extract_epub_text(input_path, html_path, 'html', progress_callback):
                return False

            # 第二步：嘗試 HTML → PDF
            progress_callback(ConversionStatus.CONVERTING, "步驟 2/2: 正在嘗試轉為 PDF...")

            # 嘗試使用已安裝的 PDF 庫
            pdf_success = False

            # 方法 1: 使用 reportlab (優先使用，最穩定)
            try:
                from reportlab.lib.pagesizes import A4
                from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
                from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
                from reportlab.lib.enums import TA_LEFT, TA_CENTER
                from reportlab.pdfbase import pdfmetrics
                from reportlab.pdfbase.ttfonts import TTFont

                progress_callback(ConversionStatus.CONVERTING, "正在使用 ReportLab 生成 PDF...")

                # 註冊中文字體
                self._register_chinese_fonts()

                # 讀取 HTML 內容並提取文字
                with open(html_path, 'r', encoding='utf-8') as f:
                    html_content = f.read()

                text_content = self._extract_text_from_html(html_content)

                # 確保文字內容是正確的 UTF-8 編碼
                if isinstance(text_content, bytes):
                    text_content = text_content.decode('utf-8', errors='replace')

                # 清理和標準化文字
                text_content = self._clean_text_for_pdf(text_content)

                # 建立 PDF
                doc = SimpleDocTemplate(output_path, pagesize=A4,
                                      rightMargin=72, leftMargin=72,
                                      topMargin=72, bottomMargin=18)

                # 取得樣式
                styles = getSampleStyleSheet()

                # 建立自訂樣式（支援中文）
                try:
                    # 嘗試使用註冊的中文字體
                    chinese_font = 'ChineseFont'
                    # 測試字體是否可用
                    from reportlab.pdfbase import pdfmetrics
                    pdfmetrics.getFont(chinese_font)
                except:
                    # 如果中文字體不可用，使用默認字體
                    chinese_font = 'Helvetica'

                title_style = ParagraphStyle(
                    'CustomTitle',
                    parent=styles['Heading1'],
                    fontName=chinese_font,
                    fontSize=18,
                    spaceAfter=30,
                    alignment=TA_CENTER,
                    encoding='utf-8'
                )

                heading_style = ParagraphStyle(
                    'CustomHeading',
                    parent=styles['Heading2'],
                    fontName=chinese_font,
                    fontSize=14,
                    spaceAfter=12,
                    spaceBefore=12,
                    encoding='utf-8'
                )

                normal_style = ParagraphStyle(
                    'CustomNormal',
                    parent=styles['Normal'],
                    fontName=chinese_font,
                    fontSize=11,
                    spaceAfter=6,
                    leading=14,
                    encoding='utf-8'
                )

                story = []

                # 解析內容並格式化
                lines = text_content.split('\n')

                # 添加標題
                epub_name = Path(input_path).stem
                title = Paragraph(f"電子書內容：{epub_name}", title_style)
                story.append(title)
                story.append(Spacer(1, 20))

                # 處理內容
                for line in lines:
                    line = line.strip()
                    if not line:
                        continue

                    # 確保每行文字都是正確的 UTF-8 編碼
                    try:
                        # 檢測章節標題
                        if line.startswith('===') and line.endswith('==='):
                            chapter_title = line.replace('===', '').strip()
                            # 使用 UTF-8 編碼處理中文
                            safe_title = self._make_text_safe_for_pdf(chapter_title)
                            para = Paragraph(safe_title, heading_style)
                            story.append(para)
                        elif any(keyword in line for keyword in ['第一章', '第二章', '第三章', '章節', 'Chapter', '第', '章']):
                            safe_line = self._make_text_safe_for_pdf(line)
                            para = Paragraph(safe_line, heading_style)
                            story.append(para)
                        else:
                            # 一般內容
                            safe_line = self._make_text_safe_for_pdf(line)
                            para = Paragraph(safe_line, normal_style)
                            story.append(para)
                    except Exception as e:
                        # 如果處理失敗，記錄錯誤並跳過這行
                        logger.warning(f"處理文字行失敗: {e}, 內容: {line[:50]}...")
                        continue

                # 生成 PDF
                doc.build(story)
                pdf_success = True

                progress_callback(ConversionStatus.SUCCESS, "🎉 PDF 轉換完成！(使用 ReportLab - 一次轉檔完成)")

            except ImportError:
                logger.info("ReportLab 不可用")
            except Exception as e:
                logger.warning(f"ReportLab 轉換失敗: {e}")

            # 方法 2: 使用 weasyprint (如果 ReportLab 失敗)
            if not pdf_success:
                try:
                    import weasyprint

                    with open(html_path, 'r', encoding='utf-8') as f:
                        html_content = f.read()

                    # 修正 HTML 以適合 PDF
                    html_content = html_content.replace('<pre>', '<div style="white-space: pre-wrap; font-family: Arial, sans-serif;">')
                    html_content = html_content.replace('</pre>', '</div>')

                    html_doc = weasyprint.HTML(string=html_content)
                    html_doc.write_pdf(output_path)
                    pdf_success = True

                    progress_callback(ConversionStatus.SUCCESS, "PDF 轉換完成！(使用 WeasyPrint)")

                except ImportError:
                    pass
                except Exception as e:
                    logger.warning(f"WeasyPrint 轉換失敗: {e}")

            # 方法 3: 使用 Chrome 無頭模式 (如果前面都失敗)
            if not pdf_success:
                try:
                    from selenium import webdriver
                    from selenium.webdriver.chrome.options import Options
                    from selenium.webdriver.chrome.service import Service
                    from webdriver_manager.chrome import ChromeDriverManager

                    progress_callback(ConversionStatus.CONVERTING, "正在使用 Chrome 生成 PDF...")

                    # 設定 Chrome 選項
                    chrome_options = Options()
                    chrome_options.add_argument('--headless')
                    chrome_options.add_argument('--no-sandbox')
                    chrome_options.add_argument('--disable-dev-shm-usage')
                    chrome_options.add_argument('--disable-gpu')
                    chrome_options.add_argument('--print-to-pdf')

                    # 設定 PDF 列印選項
                    chrome_options.add_experimental_option('useAutomationExtension', False)
                    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])

                    # 啟動 Chrome
                    service = Service(ChromeDriverManager().install())
                    driver = webdriver.Chrome(service=service, options=chrome_options)

                    try:
                        # 開啟 HTML 檔案
                        html_url = f"file://{Path(html_path).absolute()}"
                        driver.get(html_url)

                        # 等待頁面載入
                        import time
                        time.sleep(2)

                        # 使用 Chrome 的列印功能生成 PDF
                        pdf_data = driver.execute_cdp_cmd('Page.printToPDF', {
                            'format': 'A4',
                            'printBackground': True,
                            'marginTop': 1,
                            'marginBottom': 1,
                            'marginLeft': 1,
                            'marginRight': 1
                        })

                        # 儲存 PDF
                        import base64
                        with open(output_path, 'wb') as f:
                            f.write(base64.b64decode(pdf_data['data']))

                        pdf_success = True
                        progress_callback(ConversionStatus.SUCCESS, "PDF 轉換完成！(使用 Chrome)")

                    finally:
                        driver.quit()

                except ImportError:
                    logger.info("Chrome/Selenium 不可用")
                except Exception as e:
                    logger.warning(f"Chrome PDF 轉換失敗: {e}")

            # 如果都失敗，提供手動方案
            if not pdf_success:
                progress_callback(ConversionStatus.SUCCESS,
                                f"HTML 檔案已生成: {html_path}\n\n"
                                f"請手動轉為 PDF:\n"
                                f"1. 用瀏覽器開啟 HTML 檔案\n"
                                f"2. 按 Ctrl+P 列印\n"
                                f"3. 選擇「另存為 PDF」\n"
                                f"4. 儲存為: {output_path}")

                # 嘗試開啟 HTML 檔案
                try:
                    import webbrowser
                    webbrowser.open(f"file://{Path(html_path).absolute()}")
                except:
                    pass
            else:
                # 清理臨時 HTML 檔案
                try:
                    Path(html_path).unlink()
                except:
                    pass

            return True

        except Exception as e:
            logger.error(f"EPUB → PDF 轉換失敗: {e}")
            progress_callback(ConversionStatus.FAILED, f"EPUB → PDF 轉換失敗: {str(e)}")
            return False

class EbookConverterGUI:
    """電子書轉檔 GUI 控制器類別"""

    def __init__(self):
        self.converter = EbookConverter()
        self.root = tk.Tk()
        self.setup_window()
        self.create_widgets()
        self.show_engine_status()

    def setup_window(self):
        """設定主視窗"""
        self.root.title(AppConstants.TITLE)
        self.root.geometry(AppConstants.WINDOW_SIZE)
        self.root.resizable(False, False)

        # 設定視窗圖示（如果有的話）
        try:
            # self.root.iconbitmap('icon.ico')  # 可選：加入圖示
            pass
        except:
            pass

    def create_widgets(self):
        """建立 GUI 元件"""
        # 輸入檔案區域
        self.create_input_section()

        # 輸出格式區域
        self.create_format_section()

        # 輸出檔案區域
        self.create_output_section()

        # 控制按鈕區域
        self.create_control_section()

        # 狀態顯示區域
        self.create_status_section()

        # 進度條
        self.create_progress_section()

    def create_input_section(self):
        """建立輸入檔案選擇區域"""
        tk.Label(self.root, text='選擇輸入檔案：').grid(
            row=0, column=0, padx=10, pady=10, sticky='e'
        )

        self.input_entry = tk.Entry(self.root, width=AppConstants.ENTRY_WIDTH)
        self.input_entry.grid(row=0, column=1, padx=5)

        tk.Button(self.root, text='瀏覽', command=self.select_input_file).grid(
            row=0, column=2, padx=5
        )

    def create_format_section(self):
        """建立輸出格式選擇區域"""
        tk.Label(self.root, text='選擇輸出格式：').grid(
            row=1, column=0, padx=10, pady=10, sticky='e'
        )

        self.format_var = tk.StringVar(value=AppConstants.SUPPORTED_FORMATS[0])
        self.format_menu = ttk.Combobox(
            self.root,
            textvariable=self.format_var,
            values=AppConstants.SUPPORTED_FORMATS,
            state='readonly',
            width=12
        )
        self.format_menu.grid(row=1, column=1, sticky='w', padx=5)

        # 當格式改變時自動更新輸出路徑
        self.format_menu.bind('<<ComboboxSelected>>', self.on_format_changed)

    def create_output_section(self):
        """建立輸出檔案預覽區域"""
        tk.Label(self.root, text='輸出檔案路徑：').grid(
            row=2, column=0, padx=10, pady=10, sticky='e'
        )

        self.output_entry = tk.Entry(self.root, width=AppConstants.ENTRY_WIDTH, state='readonly')
        self.output_entry.grid(row=2, column=1, padx=5, columnspan=2)

    def create_control_section(self):
        """建立控制按鈕區域"""
        self.convert_button = tk.Button(
            self.root,
            text='開始轉檔',
            command=self.start_conversion,
            width=AppConstants.BUTTON_WIDTH,
            bg='#4CAF50',
            fg='white',
            font=('Arial', 10, 'bold')
        )
        self.convert_button.grid(row=3, column=0, columnspan=3, pady=15)

    def create_status_section(self):
        """建立狀態顯示區域"""
        self.status_label = tk.Label(
            self.root,
            text=ConversionStatus.IDLE.value,
            font=('Arial', 9),
            wraplength=500,  # 自動換行
            justify='left',   # 左對齊
            height=2         # 增加高度以顯示更多內容
        )
        self.status_label.grid(row=5, column=0, columnspan=3, pady=5, sticky='ew')

    def create_progress_section(self):
        """建立進度條區域"""
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.root,
            variable=self.progress_var,
            mode='indeterminate',
            length=300
        )
        self.progress_bar.grid(row=4, column=0, columnspan=3, pady=10, padx=20)

    def select_input_file(self):
        """選擇輸入檔案"""
        filetypes = [
            ('電子書檔案', ' '.join(f'*.{fmt}' for fmt in AppConstants.SUPPORTED_FORMATS)),
            ('所有檔案', '*.*')
        ]

        file_path = filedialog.askopenfilename(
            title='選擇要轉換的電子書檔案',
            filetypes=filetypes
        )

        if file_path:
            self.input_entry.delete(0, tk.END)
            self.input_entry.insert(0, file_path)
            # 自動生成輸出檔案路徑
            self.update_output_path()
            self.update_status(ConversionStatus.IDLE, "已選擇輸入檔案")

    def update_output_path(self):
        """根據輸入檔案和選擇的格式自動更新輸出路徑"""
        input_path = self.input_entry.get().strip()
        if not input_path:
            return

        input_file = Path(input_path)
        output_format = self.format_var.get()

        # 生成輸出檔案名稱：原檔名_converted.新格式
        output_filename = f"{input_file.stem}_converted.{output_format}"
        output_path = input_file.parent / output_filename

        # 更新輸出路徑顯示
        self.output_entry.config(state='normal')
        self.output_entry.delete(0, tk.END)
        self.output_entry.insert(0, str(output_path))
        self.output_entry.config(state='readonly')

    def on_format_changed(self, _=None):
        """當輸出格式改變時的回調函數"""
        # _ 參數由 tkinter 事件系統提供，但我們不需要使用它
        self.update_output_path()
        self.update_status(ConversionStatus.IDLE, f"已選擇 {self.format_var.get().upper()} 格式")

    def validate_inputs(self) -> bool:
        """驗證使用者輸入"""
        input_path = self.input_entry.get().strip()

        # 驗證輸入檔案
        if not self.converter.validate_input_file(input_path):
            if not input_path:
                self.update_status(f'⚠️ {AppConstants.MESSAGES["select_input"]}')
            elif not Path(input_path).exists():
                self.update_status(f'⚠️ {AppConstants.MESSAGES["file_not_found"]}')
            else:
                self.update_status(f'⚠️ {AppConstants.MESSAGES["invalid_format"]}')
            return False

        # 檢查輸出路徑是否已生成
        output_path = self.output_entry.get().strip()
        if not output_path:
            self.update_status('⚠️ 無法生成輸出檔案路徑，請重新選擇輸入檔案')
            return False

        # 檢查格式相容性
        if not self.check_format_compatibility(input_path, output_path):
            return False

        # 檢查輸出檔案是否已存在
        if Path(output_path).exists():
            # 自動覆蓋現有檔案，在狀態列顯示訊息
            self.update_status(f'📝 輸出檔案已存在，將自動覆蓋: {Path(output_path).name}')

        return True

    def check_format_compatibility(self, input_path: str, output_path: str) -> bool:
        """檢查格式相容性"""
        input_ext = Path(input_path).suffix.lower().lstrip('.')
        output_ext = Path(output_path).suffix.lower().lstrip('.')

        # 檢查可用引擎
        engines = self.converter.available_engines

        # 如果有 Calibre，支援所有格式
        if engines.get('calibre', False):
            return True

        # 如果有 Pandoc，檢查 Pandoc 支援的格式
        if engines.get('pandoc', False):
            pandoc_formats = ['txt', 'html', 'epub', 'docx', 'pdf', 'md', 'markdown']
            if input_ext in pandoc_formats and output_ext in pandoc_formats:
                return True

        # 檢查內建轉換器支援
        if engines.get('builtin', False):
            builtin_formats = AppConstants.BUILTIN_SUPPORTED
            extended_formats = AppConstants.EXTENDED_SUPPORTED

            # 基本格式互轉
            if input_ext in builtin_formats and output_ext in builtin_formats:
                return True

            # EPUB 文字提取
            if input_ext in extended_formats and output_ext in builtin_formats:
                return True

            # 特殊處理：EPUB → PDF (透過 HTML 中介)
            if input_ext == 'epub' and output_ext == 'pdf':
                # 直接支援 EPUB → PDF 轉換，無需確認對話框
                return True

        # 如果都不支援，顯示警告
        available_engines = [name for name, available in engines.items() if available]
        engine_names = {
            'calibre': 'Calibre',
            'pandoc': 'Pandoc',
            'python_libs': 'Python庫',
            'builtin': '內建轉換器'
        }

        available_names = [engine_names.get(name, name) for name in available_engines]

        # 在狀態列顯示格式不支援訊息
        self.update_status(
            f'⚠️ 無法處理 {input_ext.upper()} → {output_ext.upper()} 轉換。'
            f'可用引擎: {", ".join(available_names)}。建議安裝 Pandoc 或 Calibre'
        )
        return False

    def start_conversion(self):
        """開始轉檔流程"""
        # 檢查是否正在轉檔
        if self.converter.is_converting:
            self.update_status('轉檔正在進行中，請稍候...')
            return

        # 驗證輸入
        if not self.validate_inputs():
            return

        # 取得參數
        input_path = self.input_entry.get().strip()
        output_path = self.output_entry.get().strip()

        # 開始轉檔
        self.set_converting_state(True)
        self.converter.convert_ebook_async(
            input_path,
            output_path,
            self.on_conversion_progress
        )

    def on_conversion_progress(self, status: ConversionStatus, message: str):
        """轉檔進度回調函數"""
        # 在主執行緒中更新 GUI
        self.root.after(0, lambda: self._update_conversion_status(status, message))

    def _update_conversion_status(self, status: ConversionStatus, message: str):
        """更新轉檔狀態（在主執行緒中執行）"""
        self.update_status(status, message)

        if status == ConversionStatus.CONVERTING:
            self.progress_bar.start(10)  # 開始進度條動畫
        else:
            self.progress_bar.stop()  # 停止進度條動畫
            self.set_converting_state(False)

            if status == ConversionStatus.SUCCESS:
                # 在狀態列顯示成功訊息，不彈出對話框
                self.update_status(f'✅ {message}')
                # 自動開啟輸出資料夾（無需確認）
                self.open_output_folder()
            elif status == ConversionStatus.FAILED:
                # 在狀態列顯示錯誤訊息，不彈出對話框
                self.update_status(f'❌ {message}')

    def set_converting_state(self, is_converting: bool):
        """設定轉檔狀態"""
        if is_converting:
            self.convert_button.config(
                text='轉檔中...',
                state='disabled',
                bg='#FF9800'
            )
        else:
            self.convert_button.config(
                text='開始轉檔',
                state='normal',
                bg='#4CAF50'
            )

    def update_status(self, status_or_message, message: str = ""):
        """更新狀態顯示"""
        # 支援兩種調用方式：
        # 1. update_status(ConversionStatus.SUCCESS, "訊息")
        # 2. update_status("直接訊息")

        if isinstance(status_or_message, ConversionStatus):
            # 舊的調用方式
            status = status_or_message
            display_text = message if message else status.value

            # 根據狀態設定顏色
            color_map = {
                ConversionStatus.IDLE: 'black',
                ConversionStatus.CONVERTING: 'blue',
                ConversionStatus.SUCCESS: 'green',
                ConversionStatus.FAILED: 'red'
            }
            color = color_map.get(status, 'black')
        else:
            # 新的調用方式：直接傳入訊息
            display_text = status_or_message

            # 根據訊息前綴設定顏色
            if display_text.startswith('✅'):
                color = 'green'
            elif display_text.startswith('❌'):
                color = 'red'
            elif display_text.startswith('⚠️'):
                color = 'orange'
            elif display_text.startswith('📝') or display_text.startswith('📊'):
                color = 'blue'
            else:
                color = 'black'

        self.status_label.config(text=display_text, fg=color)

    def open_output_folder(self):
        """開啟輸出檔案所在資料夾"""
        try:
            output_path = self.output_entry.get().strip()
            if output_path:
                folder_path = Path(output_path).parent
                if folder_path.exists():
                    import platform
                    system = platform.system()
                    if system == "Windows":
                        subprocess.run(['explorer', str(folder_path)])
                    elif system == "Darwin":  # macOS
                        subprocess.run(['open', str(folder_path)])
                    else:  # Linux
                        subprocess.run(['xdg-open', str(folder_path)])
        except Exception as e:
            logger.error(f"無法開啟資料夾: {e}")

    def run(self):
        """啟動應用程式"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            logger.info("使用者中斷程式")
        except Exception as e:
            logger.error(f"應用程式錯誤: {e}")
            # 記錄錯誤但不彈出對話框
            print(f'❌ 應用程式發生錯誤：{e}')

    def show_engine_status(self):
        """顯示可用引擎狀態"""
        engines = self.converter.available_engines
        available_engines = [name for name, available in engines.items() if available]

        engine_names = {
            'calibre': 'Calibre',
            'pandoc': 'Pandoc',
            'python_libs': 'Python庫',
            'builtin': '內建轉換器'
        }

        if available_engines:
            engine_list = [engine_names.get(name, name) for name in available_engines]
            status_msg = f"可用引擎: {', '.join(engine_list)}"
            self.update_status(ConversionStatus.IDLE, status_msg)
            logger.info(f"檢測到可用引擎: {available_engines}")
        else:
            self.update_status(ConversionStatus.FAILED, "未檢測到可用的轉檔引擎")
            logger.warning("未檢測到任何可用的轉檔引擎")


def main():
    """主程式進入點"""
    try:
        # 建立並啟動 GUI 應用程式
        app = EbookConverterGUI()
        app.run()
    except Exception as e:
        logger.error(f"程式啟動失敗: {e}")
        print(f"程式啟動失敗: {e}")


if __name__ == '__main__':
    main()