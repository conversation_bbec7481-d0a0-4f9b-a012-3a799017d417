"""
深度診斷內容短少問題
詳細分析每個步驟的內容損失
"""

import zipfile
import xml.etree.ElementTree as ET
from pathlib import Path
from bs4 import BeautifulSoup
import re


def analyze_epub_completely(epub_path):
    """完全分析 EPUB 檔案內容"""
    print(f"🔍 深度分析 EPUB: {epub_path}")
    print("=" * 60)
    
    if not Path(epub_path).exists():
        print("❌ EPUB 檔案不存在")
        return None
    
    analysis = {
        'total_files': 0,
        'html_files': [],
        'total_chars': 0,
        'chapters': [],
        'spine_order': [],
        'extraction_results': {}
    }
    
    try:
        with zipfile.ZipFile(epub_path, 'r') as epub_zip:
            # 1. 分析檔案結構
            all_files = epub_zip.namelist()
            analysis['total_files'] = len(all_files)
            
            print(f"📁 EPUB 包含 {len(all_files)} 個檔案:")
            for file in all_files:
                print(f"  • {file}")
            
            # 2. 找到所有 HTML 檔案
            html_files = [f for f in all_files if f.endswith(('.html', '.xhtml', '.htm'))]
            analysis['html_files'] = html_files
            
            print(f"\n📄 找到 {len(html_files)} 個 HTML 檔案:")
            for html_file in html_files:
                try:
                    content = epub_zip.read(html_file).decode('utf-8', errors='replace')
                    char_count = len(content)
                    analysis['total_chars'] += char_count
                    print(f"  • {html_file}: {char_count} 字符")
                except Exception as e:
                    print(f"  ❌ {html_file}: 讀取失敗 - {e}")
            
            # 3. 解析 OPF 檔案獲取正確順序
            try:
                container_content = epub_zip.read('META-INF/container.xml').decode('utf-8')
                container_root = ET.fromstring(container_content)
                
                opf_path = None
                for rootfile in container_root.findall('.//{urn:oasis:names:tc:opendocument:xmlns:container}rootfile'):
                    if rootfile.get('media-type') == 'application/oebps-package+xml':
                        opf_path = rootfile.get('full-path')
                        break
                
                if opf_path:
                    print(f"\n📋 OPF 檔案: {opf_path}")
                    opf_content = epub_zip.read(opf_path).decode('utf-8')
                    opf_root = ET.fromstring(opf_content)
                    
                    namespaces = {'opf': 'http://www.idpf.org/2007/opf'}
                    
                    # 獲取 manifest
                    manifest_items = {}
                    for item in opf_root.findall('.//opf:item', namespaces):
                        item_id = item.get('id')
                        href = item.get('href')
                        if item_id and href:
                            manifest_items[item_id] = href
                    
                    # 獲取 spine 順序
                    spine_order = []
                    for itemref in opf_root.findall('.//opf:itemref', namespaces):
                        idref = itemref.get('idref')
                        if idref in manifest_items:
                            spine_order.append(manifest_items[idref])
                    
                    analysis['spine_order'] = spine_order
                    
                    print(f"📚 Spine 順序 ({len(spine_order)} 個項目):")
                    for i, href in enumerate(spine_order, 1):
                        print(f"  {i}. {href}")
                
            except Exception as e:
                print(f"⚠️ OPF 解析失敗: {e}")
                analysis['spine_order'] = html_files  # 回退到簡單列表
            
            # 4. 逐個分析每個章節的內容
            print(f"\n📖 詳細章節內容分析:")
            print("-" * 50)
            
            files_to_analyze = analysis['spine_order'] if analysis['spine_order'] else html_files
            
            for i, file_path in enumerate(files_to_analyze, 1):
                print(f"\n第 {i} 個檔案: {file_path}")
                
                try:
                    # 嘗試不同的路徑組合
                    content = None
                    for attempt_path in [file_path, f"OEBPS/{file_path}", f"Text/{file_path}"]:
                        try:
                            content = epub_zip.read(attempt_path).decode('utf-8', errors='replace')
                            break
                        except KeyError:
                            continue
                    
                    if not content:
                        print(f"  ❌ 無法讀取檔案")
                        continue
                    
                    # 分析原始 HTML
                    print(f"  📄 原始 HTML: {len(content)} 字符")
                    
                    # 使用不同方法提取文字
                    methods = {
                        '簡單正則': extract_text_simple_regex,
                        'BeautifulSoup': extract_text_beautifulsoup,
                        '增強解析': extract_text_enhanced
                    }
                    
                    for method_name, method_func in methods.items():
                        try:
                            extracted = method_func(content)
                            print(f"  📝 {method_name}: {len(extracted)} 字符")
                            
                            # 儲存結果供比較
                            if file_path not in analysis['extraction_results']:
                                analysis['extraction_results'][file_path] = {}
                            analysis['extraction_results'][file_path][method_name] = {
                                'length': len(extracted),
                                'content': extracted[:200] + '...' if len(extracted) > 200 else extracted
                            }
                            
                        except Exception as e:
                            print(f"  ❌ {method_name} 失敗: {e}")
                
                except Exception as e:
                    print(f"  ❌ 處理失敗: {e}")
    
    except Exception as e:
        print(f"❌ EPUB 分析失敗: {e}")
        return None
    
    return analysis


def extract_text_simple_regex(html_content):
    """簡單正則表達式提取（原始方法）"""
    # 移除 script 和 style 標籤
    html_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
    html_content = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
    
    # 移除所有 HTML 標籤
    text = re.sub(r'<[^>]+>', '', html_content)
    
    # 清理空白字符
    text = re.sub(r'\s+', ' ', text)
    return text.strip()


def extract_text_beautifulsoup(html_content):
    """使用 BeautifulSoup 提取"""
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 移除 script 和 style 標籤
    for script in soup(["script", "style"]):
        script.decompose()
    
    # 獲取純文字
    return soup.get_text(separator=' ', strip=True)


def extract_text_enhanced(html_content):
    """增強提取方法"""
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 移除不需要的標籤
    for script in soup(["script", "style", "meta", "link"]):
        script.decompose()
    
    text_parts = []
    
    # 提取標題
    for heading in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
        text = heading.get_text(strip=True)
        if text:
            text_parts.append(f"\n{text}\n")
    
    # 提取段落
    for para in soup.find_all('p'):
        text = para.get_text(strip=True)
        if text:
            text_parts.append(text)
    
    # 提取列表
    for ul in soup.find_all(['ul', 'ol']):
        for li in ul.find_all('li'):
            text = li.get_text(strip=True)
            if text:
                text_parts.append(f"• {text}")
    
    # 提取表格
    for table in soup.find_all('table'):
        for row in table.find_all('tr'):
            cells = [cell.get_text(strip=True) for cell in row.find_all(['td', 'th'])]
            if any(cells):
                text_parts.append(" | ".join(cells))
    
    # 提取其他文字（如果上面沒有找到足夠內容）
    if not text_parts:
        text_parts.append(soup.get_text(separator=' ', strip=True))
    
    return '\n'.join(text_parts)


def test_current_extraction_method(epub_path):
    """測試當前程式的提取方法"""
    print(f"\n🧪 測試當前程式的提取方法")
    print("-" * 50)
    
    try:
        from main import EbookConverter
        
        converter = EbookConverter()
        output_path = "current_method_test.txt"
        
        def callback(status, message):
            print(f"📊 {status.value}: {message}")
        
        # 使用當前方法提取
        success = converter._extract_epub_text(epub_path, output_path, 'txt', callback)
        
        if success and Path(output_path).exists():
            with open(output_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"📝 當前方法結果: {len(content)} 字符")
            print(f"前 300 字符: {content[:300]}...")
            
            # 清理測試檔案
            Path(output_path).unlink()
            
            return len(content), content
        else:
            print("❌ 當前方法提取失敗")
            return 0, ""
            
    except Exception as e:
        print(f"❌ 測試當前方法失敗: {e}")
        return 0, ""


def main():
    """主程式"""
    print("🔍 深度內容短少診斷工具")
    print("=" * 60)
    print("詳細分析每個步驟的內容損失情況")
    print()
    
    # 請用戶提供 EPUB 檔案路徑
    epub_path = input("請輸入 EPUB 檔案路徑（或按 Enter 使用測試檔案）: ").strip()
    
    if not epub_path:
        print("使用內建測試檔案...")
        # 這裡可以建立一個測試檔案
        print("請提供一個實際的 EPUB 檔案進行診斷")
        return
    
    if not Path(epub_path).exists():
        print(f"❌ 檔案不存在: {epub_path}")
        return
    
    # 深度分析
    analysis = analyze_epub_completely(epub_path)
    
    if analysis:
        # 測試當前方法
        current_length, current_content = test_current_extraction_method(epub_path)
        
        # 總結報告
        print(f"\n📊 診斷總結報告")
        print("=" * 50)
        print(f"EPUB 總檔案數: {analysis['total_files']}")
        print(f"HTML 檔案數: {len(analysis['html_files'])}")
        print(f"原始總字符數: {analysis['total_chars']}")
        print(f"當前方法提取: {current_length} 字符")
        
        if analysis['total_chars'] > 0:
            extraction_rate = (current_length / analysis['total_chars']) * 100
            print(f"提取率: {extraction_rate:.1f}%")
            
            if extraction_rate < 80:
                print(f"⚠️ 提取率偏低，存在明顯內容短少")
            elif extraction_rate < 95:
                print(f"⚠️ 提取率一般，可能有輕微內容短少")
            else:
                print(f"✅ 提取率良好")
        
        # 提供改進建議
        print(f"\n💡 改進建議:")
        if analysis['spine_order']:
            print("• EPUB 結構正常，建議檢查文字提取邏輯")
        else:
            print("• EPUB 結構解析有問題，建議使用簡單方法")
        
        print("• 比較不同提取方法的效果")
        print("• 檢查是否有隱藏或嵌套的內容")


if __name__ == '__main__':
    main()
