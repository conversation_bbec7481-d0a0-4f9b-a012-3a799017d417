# Calibre 安裝和環境變數設定腳本
# 作者：AI Assistant
# 用途：自動安裝 Calibre 並設定 ebook-convert 環境變數

Write-Host "=== Calibre 安裝和環境變數設定腳本 ===" -ForegroundColor Green

# 檢查是否以管理員身份運行
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

if (-not (Test-Administrator)) {
    Write-Warning "建議以管理員身份運行此腳本以獲得最佳效果"
}

# 檢查 Calibre 是否已安裝
function Find-CalibreInstallation {
    $possiblePaths = @(
        "C:\Program Files\Calibre2",
        "C:\Program Files (x86)\Calibre2",
        "$env:LOCALAPPDATA\Programs\Calibre",
        "$env:PROGRAMFILES\Calibre2",
        "$env:PROGRAMFILES(X86)\Calibre2"
    )
    
    foreach ($path in $possiblePaths) {
        if (Test-Path "$path\ebook-convert.exe") {
            return $path
        }
    }
    return $null
}

# 檢查現有安裝
$calibrePath = Find-CalibreInstallation
if ($calibrePath) {
    Write-Host "找到現有的 Calibre 安裝: $calibrePath" -ForegroundColor Yellow
} else {
    Write-Host "未找到 Calibre 安裝，開始下載安裝..." -ForegroundColor Yellow
    
    # 下載 Calibre
    $downloadUrl = "https://github.com/kovidgoyal/calibre/releases/download/v8.4.0/calibre-64bit-8.4.0.msi"
    $installerPath = "$env:TEMP\calibre-installer.msi"
    
    try {
        Write-Host "正在下載 Calibre 8.4.0..." -ForegroundColor Cyan
        Invoke-WebRequest -Uri $downloadUrl -OutFile $installerPath -UseBasicParsing
        Write-Host "下載完成！" -ForegroundColor Green
        
        # 安裝 Calibre
        Write-Host "正在安裝 Calibre..." -ForegroundColor Cyan
        Start-Process msiexec.exe -ArgumentList "/i `"$installerPath`" /quiet /norestart" -Wait
        Write-Host "安裝完成！" -ForegroundColor Green
        
        # 重新檢查安裝
        Start-Sleep -Seconds 3
        $calibrePath = Find-CalibreInstallation
        
        if (-not $calibrePath) {
            Write-Error "安裝後仍未找到 Calibre，請手動安裝"
            exit 1
        }
    }
    catch {
        Write-Error "下載或安裝失敗: $($_.Exception.Message)"
        Write-Host "請手動下載並安裝 Calibre: https://calibre-ebook.com/download_windows" -ForegroundColor Yellow
        exit 1
    }
}

# 設定環境變數
Write-Host "正在設定環境變數..." -ForegroundColor Cyan

# 獲取當前 PATH
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
$systemPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")

# 檢查是否已經在 PATH 中
if ($currentPath -notlike "*$calibrePath*" -and $systemPath -notlike "*$calibrePath*") {
    try {
        # 添加到用戶 PATH
        $newPath = $currentPath + ";" + $calibrePath
        [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
        Write-Host "已將 Calibre 路徑添加到用戶環境變數" -ForegroundColor Green
        
        # 更新當前會話的 PATH
        $env:PATH = $env:PATH + ";" + $calibrePath
        Write-Host "已更新當前會話的 PATH" -ForegroundColor Green
    }
    catch {
        Write-Warning "無法自動設定環境變數，請手動添加: $calibrePath"
    }
} else {
    Write-Host "Calibre 路徑已存在於 PATH 中" -ForegroundColor Yellow
}

# 驗證安裝
Write-Host "正在驗證安裝..." -ForegroundColor Cyan
try {
    $version = & "$calibrePath\ebook-convert.exe" --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ ebook-convert 可正常使用" -ForegroundColor Green
        Write-Host "版本信息: $($version -split "`n" | Select-Object -First 1)" -ForegroundColor Cyan
    } else {
        Write-Warning "ebook-convert 執行時出現問題"
    }
}
catch {
    Write-Warning "無法執行 ebook-convert，可能需要重新啟動終端"
}

Write-Host "`n=== 安裝完成 ===" -ForegroundColor Green
Write-Host "Calibre 安裝路徑: $calibrePath" -ForegroundColor Cyan
Write-Host "如果 ebook-convert 命令仍無法使用，請重新啟動 PowerShell 或命令提示字元" -ForegroundColor Yellow

# 顯示使用示例
Write-Host "`n=== 使用示例 ===" -ForegroundColor Green
Write-Host "轉換 EPUB 到 PDF:" -ForegroundColor Cyan
Write-Host "ebook-convert input.epub output.pdf" -ForegroundColor White
Write-Host "`n轉換 MOBI 到 EPUB:" -ForegroundColor Cyan
Write-Host "ebook-convert input.mobi output.epub" -ForegroundColor White
Write-Host "`n查看所有支援格式:" -ForegroundColor Cyan
Write-Host "ebook-convert --help" -ForegroundColor White
