"""
快速測試內容提取修正
"""

from main import EbookConverter
import zipfile
from pathlib import Path


def create_simple_test_epub():
    """建立簡單測試 EPUB"""
    epub_path = "simple_content_test.epub"
    
    files = {
        'mimetype': 'application/epub+zip',
        'META-INF/container.xml': '''<?xml version="1.0"?>
<container version="1.0" xmlns="urn:oasis:names:tc:opendocument:xmlns:container">
  <rootfiles>
    <rootfile full-path="content.opf" media-type="application/oebps-package+xml"/>
  </rootfiles>
</container>''',
        'content.opf': '''<?xml version="1.0" encoding="UTF-8"?>
<package xmlns="http://www.idpf.org/2007/opf" unique-identifier="BookId" version="2.0">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <dc:title>簡單測試</dc:title>
    <dc:creator>測試</dc:creator>
  </metadata>
  <manifest>
    <item id="ch1" href="ch1.html" media-type="application/xhtml+xml"/>
    <item id="ch2" href="ch2.html" media-type="application/xhtml+xml"/>
  </manifest>
  <spine>
    <itemref idref="ch1"/>
    <itemref idref="ch2"/>
  </spine>
</package>''',
        'ch1.html': '''<!DOCTYPE html>
<html>
<head><title>第一章</title></head>
<body>
    <h1>第一章標題</h1>
    <p>第一章第一段內容。</p>
    <p>第一章第二段內容。</p>
    <ul>
        <li>列表項目1</li>
        <li>列表項目2</li>
    </ul>
</body>
</html>''',
        'ch2.html': '''<!DOCTYPE html>
<html>
<head><title>第二章</title></head>
<body>
    <h1>第二章標題</h1>
    <p>第二章第一段內容。</p>
    <p>第二章第二段內容。</p>
    <table>
        <tr><td>表格內容A</td><td>表格內容B</td></tr>
    </table>
</body>
</html>'''
    }
    
    with zipfile.ZipFile(epub_path, 'w', zipfile.ZIP_DEFLATED) as epub_zip:
        for file_path, content in files.items():
            epub_zip.writestr(file_path, content.encode('utf-8'))
    
    return epub_path


def test_extraction():
    """測試提取功能"""
    print("🧪 快速內容提取測試")
    print("-" * 40)
    
    # 建立測試檔案
    epub_file = create_simple_test_epub()
    print(f"✅ 建立測試 EPUB: {epub_file}")
    
    # 測試提取
    converter = EbookConverter()
    output_file = "test_output.txt"
    
    def callback(status, message):
        print(f"📊 {status.value}: {message}")
    
    try:
        # 直接調用內部方法測試
        success = converter._extract_epub_text(epub_file, output_file, 'txt', callback)
        
        if success and Path(output_file).exists():
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📝 提取內容 ({len(content)} 字符):")
            print("-" * 30)
            print(content[:500] + "..." if len(content) > 500 else content)
            
            # 檢查關鍵內容
            key_items = [
                "第一章標題",
                "第二章標題", 
                "第一章第一段內容",
                "第二章第一段內容",
                "列表項目1",
                "表格內容A"
            ]
            
            print(f"\n🔍 關鍵內容檢查:")
            missing = []
            for item in key_items:
                if item in content:
                    print(f"  ✅ {item}")
                else:
                    print(f"  ❌ {item}")
                    missing.append(item)
            
            if missing:
                print(f"\n⚠️ 缺失 {len(missing)} 項內容")
                return False
            else:
                print(f"\n🎉 所有內容都已正確提取！")
                return True
        else:
            print("❌ 提取失敗")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False
    
    finally:
        # 清理
        for file in [epub_file, output_file]:
            if Path(file).exists():
                Path(file).unlink()


def main():
    print("🔍 內容提取快速驗證")
    print("=" * 50)
    
    success = test_extraction()
    
    print(f"\n📊 測試結果:")
    if success:
        print("🎉 內容提取修正成功！")
        print("💡 現在轉檔應該能獲得完整內容")
    else:
        print("❌ 仍有問題需要修正")
        print("💡 建議檢查 EPUB 解析邏輯")


if __name__ == '__main__':
    main()
