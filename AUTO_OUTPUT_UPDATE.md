# 📁 自動輸出路徑功能更新

## 🎯 更新目標
根據使用者需求，修改電子書轉檔工具，讓輸出檔案自動放在與輸入檔案相同的目錄中，簡化操作流程。

## 🔄 主要變更

### 1. **GUI 介面調整**

#### 原本設計：
- 使用者需要手動選擇輸出檔案位置
- 有獨立的「瀏覽」按鈕選擇輸出路徑

#### 新設計：
```python
def create_output_section(self):
    """建立輸出檔案預覽區域"""
    tk.Label(self.root, text='輸出檔案路徑：').grid(...)
    
    # 設為唯讀，顯示自動生成的路徑
    self.output_entry = tk.Entry(self.root, state='readonly', bg="#f0f0f0")
```

### 2. **自動路徑生成邏輯**

<augment_code_snippet path="main.py" mode="EXCERPT">
```python
def update_output_path(self):
    """根據輸入檔案和選擇的格式自動更新輸出路徑"""
    input_path = self.input_entry.get().strip()
    if not input_path:
        return
    
    input_file = Path(input_path)
    output_format = self.format_var.get()
    
    # 生成輸出檔案名稱：原檔名_converted.新格式
    output_filename = f"{input_file.stem}_converted.{output_format}"
    output_path = input_file.parent / output_filename
```
</augment_code_snippet>

### 3. **即時更新機制**

#### 輸入檔案變更時：
```python
def select_input_file(self):
    if file_path:
        self.input_entry.insert(0, file_path)
        # 自動生成輸出檔案路徑
        self.update_output_path()
```

#### 輸出格式變更時：
```python
def on_format_changed(self, _=None):
    """當輸出格式改變時的回調函數"""
    self.update_output_path()
    self.update_status(ConversionStatus.IDLE, f"已選擇 {self.format_var.get().upper()} 格式")
```

### 4. **檔案覆蓋保護**

```python
def validate_inputs(self) -> bool:
    # 檢查輸出檔案是否已存在
    if Path(output_path).exists():
        result = messagebox.askyesno(
            '檔案已存在', 
            f'輸出檔案已存在：\n{Path(output_path).name}\n\n是否要覆蓋？'
        )
        if not result:
            return False
```

## 📋 路徑生成規則

### **命名規則**
- **格式**: `{原檔名}_converted.{新格式}`
- **位置**: 與輸入檔案相同目錄

### **範例**
| 輸入檔案 | 輸出格式 | 生成路徑 |
|---------|---------|---------|
| `C:/Books/小說.epub` | `pdf` | `C:/Books/小說_converted.pdf` |
| `D:/Documents/報告.pdf` | `epub` | `D:/Documents/報告_converted.epub` |
| `/home/<USER>/電子書.mobi` | `txt` | `/home/<USER>/電子書_converted.txt` |

## 🎨 使用者體驗改善

### ✅ **優點**
1. **簡化操作**: 不需要手動選擇輸出位置
2. **直觀預覽**: 即時顯示將要生成的檔案路徑
3. **避免覆蓋**: 自動加上 `_converted` 後綴
4. **檔案管理**: 輸出檔案與原檔案在同一位置，便於管理

### 🔄 **操作流程**
1. 選擇輸入檔案 → 自動生成輸出路徑
2. 選擇輸出格式 → 自動更新路徑副檔名
3. 點擊轉檔 → 直接開始轉換

## 🧪 測試結果

### **功能測試**
- ✅ 路徑自動生成正常
- ✅ 格式切換即時更新
- ✅ 檔案覆蓋保護有效
- ✅ 所有單元測試通過 (15/15)

### **演示程式**
建立了 `demo_auto_output.py` 展示新功能：

```
🎯 電子書轉檔工具 - 自動輸出路徑功能
==================================================

📁 路徑生成邏輯演示
------------------------------
輸入: C:/Books/小說.epub
格式: pdf
輸出: C:\Books\小說_converted.pdf

輸入: D:/Documents/報告.pdf
格式: epub
輸出: D:\Documents\報告_converted.epub
```

## 🔧 技術實現細節

### **關鍵方法**
1. `update_output_path()` - 核心路徑生成邏輯
2. `on_format_changed()` - 格式變更回調
3. `validate_inputs()` - 增強的輸入驗證

### **事件綁定**
```python
# 格式選擇器綁定事件
self.format_menu.bind('<<ComboboxSelected>>', self.on_format_changed)

# 輸入檔案選擇後自動更新
self.update_output_path()
```

## 📊 程式碼品質

### **Clean Code 原則維持**
- ✅ 單一職責原則
- ✅ 清晰的方法命名
- ✅ 適當的錯誤處理
- ✅ 完整的文檔說明

### **測試覆蓋率**
- 15 個單元測試全部通過
- 涵蓋核心功能和邊界情況
- 程式碼品質檢查通過

## 🎉 總結

這次更新成功實現了自動輸出路徑功能，大幅簡化了使用者操作流程：

1. **操作步驟減少**: 從 4 步減少到 2 步
2. **使用者體驗提升**: 即時預覽，直觀明確
3. **錯誤預防**: 自動避免檔案覆蓋
4. **程式碼品質**: 維持 Clean Code 標準

新功能讓電子書轉檔工具更加易用，同時保持了程式碼的高品質和可維護性！
